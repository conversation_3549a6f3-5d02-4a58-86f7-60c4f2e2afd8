.wood-tooltip {
  position: absolute;
  z-index: 1000;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 25px;
  width: 680px;
  pointer-events: auto;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s, transform 0.3s;
}

.wood-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

.wood-tooltip-container {
  display: flex;
  gap: 25px;
}

.wood-tooltip-image {
  flex: 0 0 auto;
  width: 255px;
  height: 476px;
  overflow: hidden;
  border-radius: 4px;
}

.wood-tooltip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.wood-tooltip-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.wood-tooltip-title {
  margin: 0 0 20px 0;
  font-size: 31px;
  color: #333;
  padding-bottom: 14px;
  border-bottom: 1px solid #eee;
}

.wood-tooltip-content {
  font-size: 24px;
}

.wood-tooltip-row {
  display: flex;
  margin-bottom: 14px;
}

.wood-tooltip-label {
  font-weight: bold;
  width: 102px;
  color: #666;
  flex-shrink: 0;
}

.wood-tooltip-value {
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.wood-tooltip-arrow {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: white;
  transform: rotate(45deg);
  top: -6px;
  left: 20px;
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
}

/* For tooltip positioned at the bottom */
.wood-tooltip.position-bottom .wood-tooltip-arrow {
  top: auto;
  bottom: -6px;
  border-left: none;
  border-top: none;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}

/* For tooltip positioned at the right */
.wood-tooltip.position-right .wood-tooltip-arrow {
  left: auto;
  right: 20px;
}

/* For tooltip positioned at the top (when near bottom of screen) */
.wood-tooltip.position-top {
  margin-top: -10px;
}

/* Adjust tooltip when it's near the bottom of the screen */
.wood-tooltip.near-bottom {
  margin-top: -200px; /* Move up by approximate tooltip height */
}
