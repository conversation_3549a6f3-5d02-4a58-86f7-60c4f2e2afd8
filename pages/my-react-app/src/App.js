import React, { useState, useEffect, useRef } from 'react';
import './App.css';
import logo from './images/logo.png';
import Util from './common/Util.js';
import { woodImages, empty, tang, song, yuan, ming, qing, changgui } from './components/Source.js';
import Drawer from './components/Drawer';
import SplashScreen from './components/SplashScreen';
import WoodTooltip from './components/WoodTooltip';
import PrivacyPolicy from './components/PrivacyPolicy';
import TermsOfUse from './components/TermsOfUse';
import WoodSpeciesIntro from './components/WoodSpeciesIntro';

// 获取 electron API
// @ts-ignore
const { ipcRenderer } = window.electron || {};

function App() {
  // 添加splash screen状态
  const [showSplash, setShowSplash] = useState(true);

  // 添加页面路由状态
  const [currentPage, setCurrentPage] = useState('main');

  // 添加license信息状态
  const [licenseInfo, setLicenseInfo] = useState({
    isValid: false,
    expireDate: '',
    remainingDays: 0
  });

  const [canvasSize, setCanvasSize] = useState(0);
  const canvasRef = useRef(null);
  const listContentRef = useRef(null);
  const [selectedAreas, setSeletedAreas] = useState([]);
  const [hoverArea, setHoverArea] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerItems, setDrawerItems] = useState([]);
  const [selectedSource, setSelectedSource] = useState(empty);
  const [history, setHistory] = useState([]);
  const hoverTimerRef = useRef(null);
  const lastPositionRef = useRef({ x: 0, y: 0 });
  const [areaImages, setAreaImages] = useState(selectedSource.clipPath.map(() => null));
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const saveSuccessTimerRef = useRef(null);
  const tooltipTimerRef = useRef(null); // 添加tooltip延迟显示的定时器引用

  // 添加木种提示框状态
  const [tooltipInfo, setTooltipInfo] = useState({
    visible: false,
    wood: null,
    position: 'bottom',
    x: 0,
    y: 0
  });

  // 处理画布大小调整
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width } = entry.contentRect;
        setCanvasSize(width);
      }
    });

    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 定义 handleUndo 函数
  const handleUndo = () => {
    if (history.length > 0) {
      const newHistory = [...history]
      setAreaImages(newHistory.pop());
      setHistory(newHistory);
    }
  };

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 检查是否按下了 Command/Ctrl + Z
      if ((e.metaKey || e.ctrlKey) && e.key === 'z') {
        e.preventDefault(); // 阻止默认行为
        handleUndo();
      }
    };

    // 添加键盘事件监听器
    window.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [history, handleUndo]);

  // 获取 license 信息
  useEffect(() => {
    // 通过 IPC 获取 license 信息
    ipcRenderer?.send('get-license-info');

    // 监听 license 信息返回
    ipcRenderer?.on('license-info', (data) => {
      if (data) {
        // 格式化日期为 yyyy-MM-dd
        const expireDate = new Date(data.expireTime);
        const year = expireDate.getFullYear();
        const month = String(expireDate.getMonth() + 1).padStart(2, '0');
        const day = String(expireDate.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;

        setLicenseInfo({
          isValid: data.isValid,
          expireDate: formattedDate,
          remainingDays: data.remainingDays
        });
      }
    });

    // 清理函数
    return () => {
      ipcRenderer?.removeAllListeners('license-info');
    };
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      // 清理悬停定时器
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
      }

      // 清理成功提示定时器
      if (saveSuccessTimerRef.current) {
        clearTimeout(saveSuccessTimerRef.current);
      }

      // 清理tooltip显示定时器
      if (tooltipTimerRef.current) {
        clearTimeout(tooltipTimerRef.current);
      }
    };
  }, []);

  // 直接显示主应用界面

  const handleWoodSelect = (woodId) => {
    if (selectedAreas.length === 0) {
      return;
    }
    let newAreaImages = [...areaImages]
    setHistory([...history, areaImages]);
    const selectedWood = woodImages.find(t => t.id === woodId)
    selectedAreas.forEach(t => {
      newAreaImages[t] = selectedWood
    })
    setAreaImages(newAreaImages)
    setSeletedAreas([])
  };



  const handleDynastyClick = (dynasty) => {
    let items = [];
    switch (dynasty) {
      case '唐':
        items = tang;
        break;
      case '宋':
        items = song;
        break;
      case '元':
        items = yuan;
        break;
      case '明':
        items = ming;
        break;
      case '清':
        items = qing;
        break;
    }
    setDrawerItems(items);
    setIsDrawerOpen(true);
  };

  const handleDrawerSelect = (item) => {
    setSeletedAreas([]);
    setAreaImages(item.clipPath.map(() => null));
    setHistory([])
    setSelectedSource(item);
    setIsDrawerOpen(false);
  };

  const scrollToIndex = (index) => {
    if (index === null) {
      return;
    }
    if (listContentRef.current) {
      const itemWidth = 70;
      const gap = 10;
      const scrollPosition = index * (itemWidth + gap);
      listContentRef.current.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  };


  const handleListContentWheel = (e) => {
    if (listContentRef.current) {
      e.preventDefault();
      listContentRef.current.scrollLeft += e.deltaY;
    }
  };

  const handleAreaImageClick = (index) => {
    if (selectedAreas.includes(index)) {
      setSeletedAreas(prev => prev.filter(t => t !== index));
    } else {
      setSeletedAreas([...selectedAreas, index]);
    }
  };

  function checkElementPos(clientX, clientY, left, top, width, height) {
    let hitElement = null
    selectedSource.clipPath.forEach((path, index) => {
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');

      ctx.beginPath();
      if (path == '' || path.startsWith('polygon(')) {
        const points = Util.clipPathPolygonToPoints(path, width, height)
        Util.clipImagePolygon(ctx, points)
      } else if (path.startsWith('path(')) {
        const cmds = Util.extractPathCommands(path, width, height)
        Util.clipImagePath(ctx, cmds)
      }
      ctx.clip();
      if (ctx.isPointInPath(clientX - left, clientY - top)) {
        hitElement = index
      }
    })
    return hitElement
  }

  function clear() {
    setSeletedAreas([])
    setAreaImages(selectedSource.clipPath.map(() => null))
    setHistory([])
  }

  async function drawCanvas(canvas, ctx, size) {
    // 异步加载图片
    function loadImage(src) {
      return new Promise((resolve) => {
        const img = new Image();
        img.src = src;
        img.onload = () => resolve(img);
      });
    }

    async function drawImage(ctx, imgPath) {
      if (!imgPath) {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, size, size);
        return;
      }
      const img = await loadImage(imgPath);
      const targetWidth = 1500;
      const scale = targetWidth / img.width;
      const targetHeight = Math.floor(img.height * scale);

      for (let y = 0; y < canvas.height; y += targetHeight) {
        for (let x = 0; x < canvas.width; x += targetWidth) {
          ctx.drawImage(img, x, y, targetWidth, targetHeight);
        }
      }
    }

    for (let i = 0; i < selectedSource.clipPath.length; i++) {
      const clipPath = selectedSource.clipPath[i]
      ctx.save();
      if (clipPath == '' || clipPath.startsWith('polygon(')) {
        const points = Util.clipPathPolygonToPoints(clipPath, size, size)
        Util.clipImagePolygon(ctx, points)
      } else if (clipPath.startsWith('path(')) {
        const cmds = Util.extractPathCommands(clipPath, size, size)
        Util.clipImagePath(ctx, cmds)
      }
      await drawImage(ctx, areaImages[i]?.src)
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 3;
      ctx.stroke();
      ctx.restore();
    }
    // if (selectedSource.bg) {
    //     ctx.save();
    //     const points = Util.clipPathPolygonToPoints('', size, size)
    //     Util.clipImagePolygon(ctx, points)
    //     const img = await loadImage(selectedSource.bg);
    //     ctx.drawImage(img, 0, 0, size, size);
    //     ctx.restore();
    // }
  }

  // 生成雪花算法ID（机器码+时间戳+随机数，6位字符）
  function generateSnowflakeId() {
    // 字符集：大写字母、数字、小写字母
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    // 获取机器码（通过IPC从主进程获取）
    let machineCode = 0;
    try {
      // 在React环境中，通过IPC获取机器ID
      // @ts-ignore
      if (window.electron && window.electron.ipcRenderer) {
        // 同步获取机器ID的hash值
        // @ts-ignore
        const machineId = window.electron.ipcRenderer.sendSync('get-machine-id');
        if (machineId) {
          // 将机器ID转换为数字hash
          machineCode = machineId.split('').reduce((hash, char) => {
            return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff;
          }, 0);
        }
      }
    } catch (error) {
      console.warn('无法获取机器ID，使用备用方案');
      // 备用方案：使用navigator.userAgent
      machineCode = navigator.userAgent.split('').reduce((hash, char) => {
        return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff;
      }, 0);
    }

    // 获取时间戳
    const timestamp = Date.now();

    // 生成随机数
    const random = Math.floor(Math.random() * 1000000);

    // 组合所有因子
    const combined = Math.abs(machineCode) + timestamp + random;

    // 转换为6位字符
    let result = '';
    let num = combined;

    for (let i = 0; i < 6; i++) {
      result = charset[num % charset.length] + result;
      num = Math.floor(num / charset.length);
    }

    // 如果结果不足6位，用随机字符补齐
    while (result.length < 6) {
      result = charset[Math.floor(Math.random() * charset.length)] + result;
    }

    return result.substring(0, 6); // 确保只返回6位
  }

  // 获取当前选择的系列名称
  function getCurrentSeries() {
    if (!selectedSource || !selectedSource.path || selectedSource.path === 'empty') {
      return '';
    }

    // 从路径中提取系列名称，如 "tang/1" -> "唐"
    const pathParts = selectedSource.path.split('/');
    const seriesKey = pathParts[0];

    const seriesMap = {
      'tang': '唐',
      'song': '宋',
      'yuan': '元',
      'ming': '明',
      'qing': '清'
    };

    return {
      name: seriesMap[seriesKey] || seriesKey,
      shortcut: seriesKey.charAt(0).toUpperCase()
    };
  }

  // 获取选用的木种信息
  function getSelectedWoodNames() {
    const selectedWoods = areaImages.filter(wood => wood !== null);
    if (selectedWoods.length === 0) {
      return '未选择';
    }

    // 去重并获取木种名称
    const uniqueWoodNames = [...new Set(selectedWoods.map(wood => wood.name))];
    return uniqueWoodNames.join('、');
  }

  // 格式化当前日期
  function formatCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // 月份从0开始，需要+1
    const day = now.getDate();
    return `${year}.${month}.${day}`;
  }

  function formatCurrentDateToYYYYMMDD() {
    const today = new Date();
    const year = today.getFullYear();
    // getMonth() 返回0-11，需+1得到实际月份，并补零到2位
    const month = String(today.getMonth() + 1).padStart(2, '0');
    // getDate() 返回1-31，直接补零到2位
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}


  function buildProjectInfo() {
    const currentSeries = getCurrentSeries();
    const currentDate = formatCurrentDate();
    const currentYYYYMMDD = formatCurrentDateToYYYYMMDD();
    const snowflakeId = generateSnowflakeId();
    const seriesPrefix = currentSeries ? currentSeries.shortcut : 'X'; // 取系列名称首字母，如果没有系列则用X

    return {
      id: `BOER-${currentYYYYMMDD}-${snowflakeId}-GDPH`,
      code: `${seriesPrefix}-${currentYYYYMMDD}-${snowflakeId}`, // 系列-日期-雪花算法id
      date: currentDate, // 保存时的日期
      woodCombined: getSelectedWoodNames(), // 选用了哪些木种
      unitPrice: calculateTotalPrice().toFixed(0),
      series: currentSeries.name // 选择时的目录名
    }
  }


  // 文本换行辅助函数
  const wrapText = (ctx, text, maxWidth) => {
    const words = text.split('');
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine + words[i];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && currentLine !== '') {
        lines.push(currentLine);
        currentLine = words[i];
      } else {
        currentLine = testLine;
      }
    }
    lines.push(currentLine);
    return lines;
  };

  // 绘制多行文本
  const drawMultilineText = (ctx, text, x, y, maxWidth, lineHeight, align = 'center') => {
    const lines = wrapText(ctx, text, maxWidth);
    const totalHeight = lines.length * lineHeight;
    const startY = y - (totalHeight / 2) + (lineHeight / 2);

    lines.forEach((line, index) => {
      const lineY = startY + (index * lineHeight);
      ctx.textAlign = align;
      ctx.fillText(line, x, lineY);
    });

    return totalHeight;
  };

  // 绘制表格到画布
  const drawTableToCanvas = (ctx, x, y, width, title, headers, data) => {
    const baseRowHeight = 40;
    const headerHeight = 50;
    const titleHeight = 40;
    const lineHeight = 18;
    const cellPadding = 10;

    // 设置字体
    ctx.font = '16px Arial, sans-serif';
    ctx.fillStyle = '#333';

    // 绘制标题
    ctx.fillStyle = '#000';
    ctx.font = 'bold 18px Arial, sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(title, x, y + titleHeight - 10);

    // 计算列宽
    const colWidth = width / headers.length;

    // 计算数据行需要的高度
    ctx.font = '14px Arial, sans-serif';
    let maxLinesInRow = 1;
    data.forEach((cellData) => {
      const maxCellWidth = colWidth - cellPadding * 2;
      const lines = wrapText(ctx, String(cellData), maxCellWidth);
      maxLinesInRow = Math.max(maxLinesInRow, lines.length);
    });

    const actualRowHeight = Math.max(baseRowHeight, maxLinesInRow * lineHeight + cellPadding * 2);

    // 绘制表头背景
    ctx.fillStyle = '#f5f5f5';
    ctx.fillRect(x, y + titleHeight, width, headerHeight);

    // 绘制表头边框
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y + titleHeight, width, headerHeight);

    // 绘制表头文字
    ctx.fillStyle = '#333';
    ctx.font = 'bold 14px Arial, sans-serif';
    headers.forEach((header, index) => {
      const cellX = x + index * colWidth;
      const textX = cellX + colWidth / 2;
      const textY = y + titleHeight + headerHeight / 2 + 5;

      ctx.textAlign = 'center';
      ctx.fillText(header, textX, textY);

      // 绘制列分隔线
      if (index > 0) {
        ctx.beginPath();
        ctx.moveTo(cellX, y + titleHeight);
        ctx.lineTo(cellX, y + titleHeight + headerHeight);
        ctx.stroke();
      }
    });

    // 绘制数据行背景
    ctx.fillStyle = '#fff';
    ctx.fillRect(x, y + titleHeight + headerHeight, width, actualRowHeight);

    // 绘制数据行边框
    ctx.strokeRect(x, y + titleHeight + headerHeight, width, actualRowHeight);

    // 绘制数据
    ctx.fillStyle = '#333';
    ctx.font = '14px Arial, sans-serif';
    data.forEach((cellData, index) => {
      const cellX = x + index * colWidth;
      const textX = cellX + colWidth / 2;
      const textY = y + titleHeight + headerHeight + actualRowHeight / 2;
      const maxCellWidth = colWidth - cellPadding * 2;

      drawMultilineText(ctx, String(cellData), textX, textY, maxCellWidth, lineHeight, 'center');

      // 绘制列分隔线
      if (index > 0) {
        ctx.beginPath();
        ctx.moveTo(cellX, y + titleHeight + headerHeight);
        ctx.lineTo(cellX, y + titleHeight + headerHeight + actualRowHeight);
        ctx.stroke();
      }
    });

    return titleHeight + headerHeight + actualRowHeight + 20; // 返回表格总高度
  };

  // 保存画布为图片并下载
  const handleSaveCanvas = () => {
    // 设置保存状态和清除错误/成功提示
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    // 清除之前的成功提示定时器
    if (saveSuccessTimerRef.current) {
      clearTimeout(saveSuccessTimerRef.current);
      saveSuccessTimerRef.current = null;
    }

    const areaCanvas = document.createElement('canvas');
    const size = 500;
    areaCanvas.width = size;
    areaCanvas.height = size;

    drawCanvas(areaCanvas, areaCanvas.getContext('2d'), size).then(() => {
      // 创建包含图像和表格的完整画布
      createCompleteCanvas(areaCanvas);
    });
  };

  // 创建包含图像和表格的完整画布
  const createCompleteCanvas = (originalCanvas) => {
    const projectInfo = buildProjectInfo();

    // 计算有效期（30天后）
    const validDate = new Date();
    validDate.setDate(validDate.getDate() + 30);
    const validDateStr = `${validDate.getFullYear()}.${validDate.getMonth() + 1}.${validDate.getDate()}`;

    // 创建新的画布，高度增加以容纳表格
    const completeCanvas = document.createElement('canvas');
    const originalCanvasPaddingY = 100;
    const originalCanvasWidth = originalCanvas.width;
    const canvasWidth = originalCanvasWidth * 2;
    const tableHeight = 400; // 两个表格的总高度
    const padding = 40;

    completeCanvas.width = canvasWidth;
    completeCanvas.height = originalCanvasWidth + originalCanvasPaddingY + tableHeight + padding;

    const ctx = completeCanvas.getContext('2d');

    // 设置白色背景
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, completeCanvas.width, completeCanvas.height);

    // 绘制原始图像
    ctx.drawImage(originalCanvas, (canvasWidth - originalCanvas.width) / 2, originalCanvasPaddingY);

    // 准备表格数据
    const quoteHeaders = ['报价单号', '报价日期', '单价（元/㎡）', '报价有效期'];
    const quoteData = [
      projectInfo.id,
      projectInfo.date,
      `${projectInfo.unitPrice}`,
      validDateStr
    ];

    const productHeaders = ['系列分类', '图纸编码', '树种组合', '产品规格'];
    const productData = [
      projectInfo.series || '未选择',
      projectInfo.code,
      projectInfo.woodCombined,
      '60×60cm'
    ];

    // 绘制第一个表格（报价概要）
    const table1Y = originalCanvasWidth + originalCanvasPaddingY + 20;
    const table1Height = drawTableToCanvas(
      ctx,
      50,
      table1Y,
      canvasWidth - 100,
      '报价概要',
      quoteHeaders,
      quoteData
    );

    // 绘制第二个表格（产品清单）
    const table2Y = table1Y + table1Height + 10;
    drawTableToCanvas(
      ctx,
      50,
      table2Y,
      canvasWidth - 100,
      '产品清单',
      productHeaders,
      productData
    );
    // 下载完整画布
    downloadCanvas(completeCanvas);
  };

  // 下载 canvas 为图片
  const downloadCanvas = (canvas) => {
    try {
      // 重置保存状态并显示成功提示
      setIsSaving(false);
      setSaveSuccess(true);

      // 3秒后自动隐藏成功提示
      saveSuccessTimerRef.current = setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);

      // 创建一个链接元素
      const link = document.createElement('a');

      // 将 canvas 转换为数据 URL
      const dataUrl = canvas.toDataURL('image/png');

      // 设置链接属性
      link.href = dataUrl;
      const projectInfo = buildProjectInfo();
      link.download = `柏尔地板设计报价单_${projectInfo.code}.png`;

      // 模拟点击链接
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading canvas:', error);
      setIsSaving(false);
      setSaveError('保存图片时出错，请重试');
    }
  };

  function calculateTotalPrice() {
    if (!selectedSource?.area || areaImages.filter(t => t === null).length > 0) {
      return 0;
    }
    let result = 0;
    for (let i = 0; i < areaImages.length; i++) {
      if (areaImages[i] && i < selectedSource.area.length) {
        result += areaImages[i].price * selectedSource.area[i];
      }
    }
    result *= (1 / 0.36)
    if (selectedSource.profit) {
      result += selectedSource.profit;
    }
    return result * 3;
  }

  function checkClickElement(e) {
    const { clientX, clientY } = e
    const { left, top, width, height } = e.target.getBoundingClientRect()
    let hitElement = checkElementPos(clientX, clientY, left, top, width, height)
    if (hitElement !== null) {
      if (selectedAreas.includes(hitElement)) {
        setSeletedAreas(prev => prev.filter(t => t !== hitElement))
      } else {
        setSeletedAreas(prev => [...prev, hitElement])
      }
      // alert(selectedSource.clipPath[hitElement])
      scrollToIndex(hitElement);
    }
  }

  const handleMouseMove = (e) => {
    const { clientX, clientY } = e;
    const { left, top, width, height } = e.target.getBoundingClientRect();

    if (clientX === lastPositionRef.current.x && clientY === lastPositionRef.current.y) {
      return;
    }

    lastPositionRef.current = { x: clientX, y: clientY };

    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
    }

    hoverTimerRef.current = setTimeout(() => {
      const hitElement = checkElementPos(clientX, clientY, left, top, width, height);
      setHoverArea(hitElement);
      scrollToIndex(hitElement);
    }, 40);
  };

  // 渲染页面内容
  const renderPageContent = () => {
    let display = 'block';
    let item = '';
    if (currentPage === 'privacy') {
      display = 'none';
      item = <PrivacyPolicy setCurrentPage={setCurrentPage} />;
    } else if (currentPage === 'terms') {
      display = 'none';
      item = <TermsOfUse setCurrentPage={setCurrentPage} />;
    } else if (currentPage === 'wood-species') {
      display = 'none';
      item = <WoodSpeciesIntro setCurrentPage={setCurrentPage} />;
    }
    return (
      <div>
        {item}
        <div style={{ display: display }}>
          {/* 顶部工具栏 */}
          <div className="toolbar">
            <div className="tool-group">
              <span className="group-choice">拼花花型选择：</span>
              <button className="tool-button" onClick={() => handleDynastyClick('唐')}>唐</button>
              <button className="tool-button" onClick={() => handleDynastyClick('宋')}>宋</button>
              <button className="tool-button" onClick={() => handleDynastyClick('元')}>元</button>
              <button className="tool-button" onClick={() => handleDynastyClick('明')}>明</button>
              <button className="tool-button" onClick={() => handleDynastyClick('清')}>清</button>
            </div>
            <div className="tool-group">
              <button className="tool-button" onClick={() => clear()}>清空</button>
              <button className="tool-button" onClick={handleUndo} disabled={history.length === 0}>撤销</button>
              <button className="tool-button" onClick={handleSaveCanvas} disabled={isSaving || selectedSource.clipPath.length === 0 || areaImages.filter(t => t === null).length > 0}>
                {isSaving ? '正在保存...' : '保存'}
              </button>
              {saveError && <div className="save-error">{saveError}</div>}
              {saveSuccess && <div className="save-success">图片已保存成功</div>}
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="main-content">
            {/* 左侧属性面板 */}
            <div className="property-panel">
              <div><h3>选择木种</h3></div>
              <div className="wood-selection">
                {woodImages.map((wood) => (
                  <div
                    key={wood.id}
                    onClick={(e) => handleWoodSelect(wood.id, e)}
                    className='wood-item'
                    onMouseEnter={(e) => {
                      // 清除之前的定时器（如果存在）
                      if (tooltipTimerRef.current) {
                        clearTimeout(tooltipTimerRef.current);
                      }

                      // 立即获取元素位置
                      const rect = e.currentTarget.getBoundingClientRect();
                      const tooltipX = rect.right + 10;

                      // 判断tooltip的垂直位置，避免被屏幕底部遮挡
                      let tooltipY;
                      const tooltipHeight = 510; // 估计的tooltip高度（增加70%后）
                      const windowHeight = window.innerHeight;

                      // 如果元素位置太靠下，将tooltip显示在元素上方
                      if (rect.top + tooltipHeight > windowHeight) {
                        tooltipY = Math.max(10, windowHeight - tooltipHeight - 20); // 确保至少有10px的上边距
                      } else {
                        tooltipY = rect.top - 60; // 默认位置
                      }

                      // 设置1秒延迟后显示tooltip
                      tooltipTimerRef.current = setTimeout(() => {
                        setTooltipInfo({
                          visible: true,
                          wood: wood,
                          position: 'bottom',
                          x: tooltipX,
                          y: tooltipY
                        });
                      }, 1000); // 1秒延迟
                    }}
                    onMouseLeave={() => {
                      // 清除定时器
                      if (tooltipTimerRef.current) {
                        clearTimeout(tooltipTimerRef.current);
                        tooltipTimerRef.current = null;
                      }

                      // 隐藏tooltip
                      setTooltipInfo(prev => ({
                        ...prev,
                        visible: false
                      }));
                    }}
                  >
                    <img src={wood.src} alt={wood.name} />
                    <span>{wood.name}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* 画布区域 */}
            <div className="canvas-container">
              <div
                ref={canvasRef}
                className="canvas"
                onClick={e => checkClickElement(e)}
                onMouseMove={handleMouseMove}
                onMouseLeave={() => {
                  if (hoverTimerRef.current) {
                    clearTimeout(hoverTimerRef.current);
                  }
                  setHoverArea(null);
                }}
              >
                {/* 预定义的裁剪区域 */}
                {selectedSource.clipPath.map((path, index) => {
                  return (
                    <div
                      key={index}
                      className="clip-area"
                      style={{
                        clipPath: Util.formatClipPath(canvasSize, path),
                        position: 'absolute',
                        width: '100%',
                        height: '100%',
                        backgroundSize: `${parseInt(canvasSize * 1.5)}px auto`,
                        backgroundRepeat: 'repeat',
                        backgroundPosition: '0 0',
                        backgroundColor: 'white',
                        backgroundImage: areaImages[index] && `url(${areaImages[index].src})`
                      }}
                    >
                      {selectedAreas.includes(index) && <div className="overlay"></div>}
                      {hoverArea === index && <div className="hover-area"></div>}
                    </div>
                  )
                })}
                {
                  <div
                    style={{
                      width: '100%',
                      height: '100%',
                      zIndex: 999
                    }}
                  >
                    {selectedSource.bg && <img src={selectedSource.bg} alt="bg" style={{ width: '100%', height: '100%' }} />}
                  </div>
                }
              </div>

              {/* 添加横向滚动列表 */}
              {
                selectedSource.clipPath.length > 0 &&
                <div className="area-images-list" style={{ width: canvasSize + 20, marginLeft: '14%' }}>
                  <div className="list-content" ref={listContentRef} onWheel={handleListContentWheel}>
                    {areaImages.map((image, index) => (
                      <div
                        key={index + 1}
                        className="area-image-item"
                        onClick={() => handleAreaImageClick(index)}
                        onMouseEnter={() => setHoverArea(index)}
                        onMouseLeave={() => setHoverArea(null)}
                      >
                        {image && <img src={image.src} style={{ width: '100%', height: '100%' }}></img>}
                        <div className='list-area-span'>{index + 1}</div>
                        <div className='list-image-span'>{image ? image.name : '未选择'}</div>
                        {selectedAreas.includes(index) && <div className="overlay"></div>}
                        {hoverArea === index && <div className="hover-area"></div>}
                      </div>
                    ))}
                  </div>
                </div>
              }

              {
                selectedSource.area && (
                  /* 计算价格 */
                  <div className="total-price-area">
                    <div className="total-price-content">
                      {calculateTotalPrice().toFixed(0) + ' 元/㎡'}
                    </div>
                  </div>)
              }
            </div>
          </div>

          {/* 抽屉组件 */}
          <Drawer
            isOpen={isDrawerOpen}
            onClose={() => setIsDrawerOpen(false)}
            items={drawerItems}
            onSelect={handleDrawerSelect}
          />

          {/* 木种提示框 */}
          {tooltipInfo.visible && (
            <div
              style={{
                position: 'fixed',
                top: tooltipInfo.y + 'px',
                left: tooltipInfo.x + 'px',
                zIndex: 2000
              }}
              onMouseEnter={() => {
                // 鼠标进入tooltip时，清除隐藏定时器
                if (tooltipTimerRef.current) {
                  clearTimeout(tooltipTimerRef.current);
                  tooltipTimerRef.current = null;
                }
              }}
              onMouseLeave={() => {
                // 鼠标离开tooltip时，隐藏tooltip
                setTooltipInfo(prev => ({
                  ...prev,
                  visible: false
                }));
              }}
            >
              <WoodTooltip
                wood={tooltipInfo.wood}
                position={tooltipInfo.position}
                visible={tooltipInfo.visible}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="app-container">
      {showSplash && <SplashScreen onFinish={() => setShowSplash(false)} />}
      <div style={{ display: showSplash ? 'none' : 'block' }}>
        {/* 顶部导航栏 */}
        <header className="header">
          <div className="header-content">
            <div className="logo-section">
              <img
                src={logo}
                alt="柏尔地板"
                className="logo"
                style={{ cursor: 'pointer' }}
                onClick={() => setCurrentPage('main')}
              />
              <span className="slogan">中国高端实木定制地板全国销量第一</span>
            </div>
            <div className="user-section">
              {/* 登录相关按钮已移除 */}
            </div>
          </div>
        </header>

        {/* 根据当前页面状态渲染不同内容 */}
        {renderPageContent()}

        {/* 底部公司信息 */}
        <footer className="footer">
          <div className="footer-content">
            <div className="footer-info">
              <p>© 2025 柏尔木业有限公司. 保留所有权利。</p>
            </div>
            <div className="footer-links">
              {licenseInfo.expireDate && (
                <span className="license-info">授权有效期至: {licenseInfo.expireDate}</span>
              )}
              <span className="separator">|</span>
              <a href="#" onClick={(e) => { e.preventDefault(); setCurrentPage('wood-species'); }}>木种介绍</a>
              <span className="separator">|</span>
              <a href="#" onClick={(e) => { e.preventDefault(); setCurrentPage('privacy'); }}>隐私政策</a>
              <span className="separator">|</span>
              <a href="#" onClick={(e) => { e.preventDefault(); setCurrentPage('terms'); }}>使用条款</a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}


export default App;
