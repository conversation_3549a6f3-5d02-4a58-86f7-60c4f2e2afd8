[{"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/index.js": "1", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/reportWebVitals.js": "2", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/App.js": "3", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/contexts/AuthContext.js": "4", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Source.js": "5", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/SplashScreen.js": "6", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Drawer.js": "7", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/PrivacyPolicy.js": "8", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/TermsOfUse.js": "9", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodSpeciesIntro.js": "10", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/common/Util.js": "11", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodTooltip.js": "12"}, {"size": 631, "mtime": 1744717586000, "results": "13", "hashOfConfig": "14"}, {"size": 362, "mtime": 1744196164000, "results": "15", "hashOfConfig": "14"}, {"size": 34588, "mtime": 1750914310724, "results": "16", "hashOfConfig": "14"}, {"size": 600, "mtime": 1745251976000, "results": "17", "hashOfConfig": "14"}, {"size": 95178, "mtime": 1749964830237, "results": "18", "hashOfConfig": "14"}, {"size": 3129, "mtime": 1750738854058, "results": "19", "hashOfConfig": "14"}, {"size": 1969, "mtime": 1744727146000, "results": "20", "hashOfConfig": "14"}, {"size": 4858, "mtime": 1745860012174, "results": "21", "hashOfConfig": "14"}, {"size": 5258, "mtime": 1745860039585, "results": "22", "hashOfConfig": "14"}, {"size": 3068, "mtime": 1750739704127, "results": "23", "hashOfConfig": "14"}, {"size": 6054, "mtime": 1745858598053, "results": "24", "hashOfConfig": "14"}, {"size": 1598, "mtime": 1745839380595, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "60lcup", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/index.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/reportWebVitals.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/App.js", ["62", "63", "64", "65", "66", "67", "68", "69", "70"], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/contexts/AuthContext.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Source.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/SplashScreen.js", ["71", "72", "73"], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Drawer.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/PrivacyPolicy.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/TermsOfUse.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodSpeciesIntro.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/common/Util.js", ["74"], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodTooltip.js", [], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 5, "column": 59, "nodeType": "77", "messageId": "78", "endLine": 5, "endColumn": 67}, {"ruleId": "79", "severity": 1, "message": "80", "line": 78, "column": 9, "nodeType": "81", "endLine": 84, "endColumn": 4, "suggestions": "82"}, {"ruleId": "83", "severity": 1, "message": "84", "line": 174, "column": 5, "nodeType": "85", "messageId": "86", "endLine": 190, "endColumn": 6}, {"ruleId": "87", "severity": 1, "message": "88", "line": 243, "column": 16, "nodeType": "89", "messageId": "90", "endLine": 243, "endColumn": 18}, {"ruleId": "87", "severity": 1, "message": "88", "line": 295, "column": 20, "nodeType": "89", "messageId": "90", "endLine": 295, "endColumn": 22}, {"ruleId": "91", "severity": 1, "message": "92", "line": 930, "column": 35, "nodeType": "93", "endLine": 930, "endColumn": 98}, {"ruleId": "94", "severity": 1, "message": "95", "line": 1034, "column": 15, "nodeType": "93", "endLine": 1034, "endColumn": 100}, {"ruleId": "94", "severity": 1, "message": "95", "line": 1036, "column": 15, "nodeType": "93", "endLine": 1036, "endColumn": 95}, {"ruleId": "94", "severity": 1, "message": "95", "line": 1038, "column": 15, "nodeType": "93", "endLine": 1038, "endColumn": 93}, {"ruleId": "75", "severity": 1, "message": "96", "line": 10, "column": 10, "nodeType": "77", "messageId": "78", "endLine": 10, "endColumn": 23}, {"ruleId": "75", "severity": 1, "message": "97", "line": 10, "column": 25, "nodeType": "77", "messageId": "78", "endLine": 10, "endColumn": 41}, {"ruleId": "75", "severity": 1, "message": "98", "line": 11, "column": 9, "nodeType": "77", "messageId": "78", "endLine": 11, "endColumn": 16}, {"ruleId": "99", "severity": 1, "message": "100", "line": 197, "column": 1, "nodeType": "101", "endLine": 203, "endColumn": 3}, "no-unused-vars", "'changgui' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "The 'handleUndo' function makes the dependencies of useEffect Hook (at line 103) change on every render. To fix this, wrap the definition of 'handleUndo' in its own useCallback() Hook.", "VariableDeclarator", ["102"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'logoAnimating' is assigned a value but never used.", "'setLogoAnimating' is assigned a value but never used.", "'logoRef' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "103", "fix": "104"}, "Wrap the definition of 'handleUndo' in its own useCallback() Hook.", {"range": "105", "text": "106"}, [2447, 2601], "useCallback(() => {\n    if (history.length > 0) {\n      const newHistory = [...history]\n      setAreaImages(newHistory.pop());\n      setHistory(newHistory);\n    }\n  })"]