[{"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/index.js": "1", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/App.js": "2", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/reportWebVitals.js": "3", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/contexts/AuthContext.js": "4", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/common/Util.js": "5", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Source.js": "6", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodTooltip.js": "7", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/PrivacyPolicy.js": "8", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/SplashScreen.js": "9", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/TermsOfUse.js": "10", "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Drawer.js": "11"}, {"size": 631, "mtime": 1744717586000, "results": "12", "hashOfConfig": "13"}, {"size": 33399, "mtime": 1750069883462, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1744196164000, "results": "15", "hashOfConfig": "13"}, {"size": 600, "mtime": 1745251976000, "results": "16", "hashOfConfig": "13"}, {"size": 6054, "mtime": 1745858598053, "results": "17", "hashOfConfig": "13"}, {"size": 95178, "mtime": 1749964830237, "results": "18", "hashOfConfig": "13"}, {"size": 1598, "mtime": 1745839380595, "results": "19", "hashOfConfig": "13"}, {"size": 4858, "mtime": 1745860012174, "results": "20", "hashOfConfig": "13"}, {"size": 3129, "mtime": 1745858075892, "results": "21", "hashOfConfig": "13"}, {"size": 5258, "mtime": 1745860039585, "results": "22", "hashOfConfig": "13"}, {"size": 1969, "mtime": 1744727146000, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "60lcup", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/index.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/App.js", ["57", "58", "59", "60", "61", "62", "63", "64"], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/reportWebVitals.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/contexts/AuthContext.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/common/Util.js", ["65"], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Source.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodTooltip.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/PrivacyPolicy.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/SplashScreen.js", ["66", "67", "68"], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/TermsOfUse.js", [], [], "/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Drawer.js", [], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 5, "column": 59, "nodeType": "71", "messageId": "72", "endLine": 5, "endColumn": 67}, {"ruleId": "73", "severity": 1, "message": "74", "line": 77, "column": 9, "nodeType": "75", "endLine": 83, "endColumn": 4, "suggestions": "76"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 173, "column": 5, "nodeType": "79", "messageId": "80", "endLine": 189, "endColumn": 6}, {"ruleId": "81", "severity": 1, "message": "82", "line": 242, "column": 16, "nodeType": "83", "messageId": "84", "endLine": 242, "endColumn": 18}, {"ruleId": "81", "severity": 1, "message": "82", "line": 294, "column": 20, "nodeType": "83", "messageId": "84", "endLine": 294, "endColumn": 22}, {"ruleId": "85", "severity": 1, "message": "86", "line": 924, "column": 35, "nodeType": "87", "endLine": 924, "endColumn": 98}, {"ruleId": "88", "severity": 1, "message": "89", "line": 1014, "column": 15, "nodeType": "87", "endLine": 1014, "endColumn": 95}, {"ruleId": "88", "severity": 1, "message": "89", "line": 1016, "column": 15, "nodeType": "87", "endLine": 1016, "endColumn": 93}, {"ruleId": "90", "severity": 1, "message": "91", "line": 197, "column": 1, "nodeType": "92", "endLine": 203, "endColumn": 3}, {"ruleId": "69", "severity": 1, "message": "93", "line": 10, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 10, "endColumn": 23}, {"ruleId": "69", "severity": 1, "message": "94", "line": 10, "column": 25, "nodeType": "71", "messageId": "72", "endLine": 10, "endColumn": 41}, {"ruleId": "69", "severity": 1, "message": "95", "line": 11, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 11, "endColumn": 16}, "no-unused-vars", "'changgui' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "The 'handleUndo' function makes the dependencies of useEffect Hook (at line 102) change on every render. To fix this, wrap the definition of 'handleUndo' in its own useCallback() Hook.", "VariableDeclarator", ["96"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'logoAnimating' is assigned a value but never used.", "'setLogoAnimating' is assigned a value but never used.", "'logoRef' is assigned a value but never used.", {"desc": "97", "fix": "98"}, "Wrap the definition of 'handleUndo' in its own useCallback() Hook.", {"range": "99", "text": "100"}, [2385, 2539], "useCallback(() => {\n    if (history.length > 0) {\n      const newHistory = [...history]\n      setAreaImages(newHistory.pop());\n      setHistory(newHistory);\n    }\n  })"]