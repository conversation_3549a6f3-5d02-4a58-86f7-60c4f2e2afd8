{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodTooltip.js\";\nimport React from 'react';\nimport '../styles/WoodTooltip.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WoodTooltip = ({\n  wood,\n  position,\n  visible\n}) => {\n  if (!wood || !wood.desc) {\n    return null;\n  }\n  const {\n    desc\n  } = wood;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `wood-tooltip ${visible ? 'visible' : ''} position-${position}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"wood-tooltip-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"wood-tooltip-image\",\n        children: desc.img && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: desc.img,\n          alt: wood.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 24\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"wood-tooltip-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"wood-tooltip-title\",\n          children: wood.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"wood-tooltip-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wood-tooltip-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-label\",\n              children: \"\\u5B66\\u540D:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-value\",\n              children: desc.scientificName || '暂无数据'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wood-tooltip-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-label\",\n              children: \"\\u4EA7\\u5730:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-value\",\n              children: desc.origin || '暂无数据'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wood-tooltip-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-label\",\n              children: \"\\u6750\\u6027:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-value\",\n              children: desc.woodProperty || '暂无数据'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wood-tooltip-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-label\",\n              children: \"\\u7528\\u9014:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-tooltip-value\",\n              children: desc.purpose || '暂无数据'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = WoodTooltip;\nexport default WoodTooltip;\nvar _c;\n$RefreshReg$(_c, \"WoodTooltip\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WoodTooltip", "wood", "position", "visible", "desc", "className", "children", "img", "src", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scientificName", "origin", "woodProperty", "purpose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodTooltip.js"], "sourcesContent": ["import React from 'react';\nimport '../styles/WoodTooltip.css';\n\nconst WoodTooltip = ({ wood, position, visible }) => {\n  if (!wood || !wood.desc) {\n    return null;\n  }\n\n  const { desc } = wood;\n\n  return (\n    <div className={`wood-tooltip ${visible ? 'visible' : ''} position-${position}`}>\n      <div className=\"wood-tooltip-container\">\n        <div className=\"wood-tooltip-image\">\n          {desc.img && <img src={desc.img} alt={wood.name} />}\n        </div>\n        <div className=\"wood-tooltip-info\">\n          <h3 className=\"wood-tooltip-title\">{wood.name}</h3>\n          <div className=\"wood-tooltip-content\">\n            <div className=\"wood-tooltip-row\">\n              <div className=\"wood-tooltip-label\">学名:</div>\n              <div className=\"wood-tooltip-value\">{desc.scientificName || '暂无数据'}</div>\n            </div>\n            <div className=\"wood-tooltip-row\">\n              <div className=\"wood-tooltip-label\">产地:</div>\n              <div className=\"wood-tooltip-value\">{desc.origin || '暂无数据'}</div>\n            </div>\n            <div className=\"wood-tooltip-row\">\n              <div className=\"wood-tooltip-label\">材性:</div>\n              <div className=\"wood-tooltip-value\">{desc.woodProperty || '暂无数据'}</div>\n            </div>\n            <div className=\"wood-tooltip-row\">\n              <div className=\"wood-tooltip-label\">用途:</div>\n              <div className=\"wood-tooltip-value\">{desc.purpose || '暂无数据'}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WoodTooltip;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EACnD,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACG,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,MAAM;IAAEA;EAAK,CAAC,GAAGH,IAAI;EAErB,oBACEF,OAAA;IAAKM,SAAS,EAAE,gBAAgBF,OAAO,GAAG,SAAS,GAAG,EAAE,aAAaD,QAAQ,EAAG;IAAAI,QAAA,eAC9EP,OAAA;MAAKM,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCP,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChCF,IAAI,CAACG,GAAG,iBAAIR,OAAA;UAAKS,GAAG,EAAEJ,IAAI,CAACG,GAAI;UAACE,GAAG,EAAER,IAAI,CAACS;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNf,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCP,OAAA;UAAIM,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEL,IAAI,CAACS;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnDf,OAAA;UAAKM,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCP,OAAA;YAAKM,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BP,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7Cf,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEF,IAAI,CAACW,cAAc,IAAI;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACNf,OAAA;YAAKM,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BP,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7Cf,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEF,IAAI,CAACY,MAAM,IAAI;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNf,OAAA;YAAKM,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BP,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7Cf,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEF,IAAI,CAACa,YAAY,IAAI;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNf,OAAA;YAAKM,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BP,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7Cf,OAAA;cAAKM,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEF,IAAI,CAACc,OAAO,IAAI;YAAM;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GArCInB,WAAW;AAuCjB,eAAeA,WAAW;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}