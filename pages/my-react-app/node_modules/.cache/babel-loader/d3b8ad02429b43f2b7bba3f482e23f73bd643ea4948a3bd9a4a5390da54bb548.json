{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/contexts/AuthContext.js\",\n  _s = $RefreshSig$();\nimport React, { createContext, useContext } from 'react';\n\n// 创建身份验证上下文\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\n\n// 身份验证提供者组件 - 简化版本，不包含登录功能\nexport const AuthProvider = ({\n  children\n}) => {\n  // 始终认为用户已认证\n  const isAuthenticated = true;\n\n  // 提供简化的上下文值\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      isAuthenticated\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n\n// 自定义钩子，方便在组件中使用身份验证上下文\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "isAuthenticated", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s", "$RefreshReg$"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext } from 'react';\n\n// 创建身份验证上下文\nconst AuthContext = createContext();\n\n// 身份验证提供者组件 - 简化版本，不包含登录功能\nexport const AuthProvider = ({ children }) => {\n  // 始终认为用户已认证\n  const isAuthenticated = true;\n\n  // 提供简化的上下文值\n  return (\n    <AuthContext.Provider value={{ isAuthenticated }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// 自定义钩子，方便在组件中使用身份验证上下文\nexport const useAuth = () => {\n  return useContext(AuthContext);\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAQ,OAAO;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGJ,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMK,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC5C;EACA,MAAMC,eAAe,GAAG,IAAI;;EAE5B;EACA,oBACEJ,OAAA,CAACC,WAAW,CAACI,QAAQ;IAACC,KAAK,EAAE;MAAEF;IAAgB,CAAE;IAAAD,QAAA,EAC9CA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAC,EAAA,GAZaT,YAAY;AAazB,OAAO,MAAMU,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,OAAOf,UAAU,CAACG,WAAW,CAAC;AAChC,CAAC;AAACY,EAAA,CAFWD,OAAO;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}