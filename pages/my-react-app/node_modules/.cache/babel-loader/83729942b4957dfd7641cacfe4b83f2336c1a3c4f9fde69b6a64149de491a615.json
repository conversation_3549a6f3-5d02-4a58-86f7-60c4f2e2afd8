{"ast": null, "code": "function clipPathPolygonToPoints(clipPathStr, width, height) {\n  if (clipPathStr === '') {\n    clipPathStr = 'polygon(0 0, 0% 100%, 100% 100%, 100% 0)';\n  }\n  // 去除多余的前缀和后缀，提取出实际的点信息\n  const polygonStr = clipPathStr.replace('polygon(', '').replace(');', '').trim();\n  // 按逗号分割每个点的信息\n  const pointsStr = polygonStr.split(',');\n  const points = [];\n  for (let pointStr of pointsStr) {\n    // 去除首尾空格\n    pointStr = pointStr.trim();\n    // 按空格分割出 x 和 y 坐标\n    const [xStr, yStr] = pointStr.split(' ');\n    let x, y;\n    if (xStr.includes('%')) {\n      // 如果 x 坐标是百分比，将其转换为像素值\n      x = parseFloat(xStr.replace('%', '')) / 100 * width;\n    } else {\n      x = parseFloat(xStr);\n    }\n    if (yStr.includes('%')) {\n      // 如果 y 坐标是百分比，将其转换为像素值\n      y = parseFloat(yStr.replace('%', '')) / 100 * height;\n    } else {\n      y = parseFloat(yStr);\n    }\n    points.push([x, y]);\n  }\n  return points;\n}\nfunction extractPathCommands(clipPath, width, height) {\n  // 提取 path 括号内的路径指令字符串\n  const pathCommandsStr = clipPath.match(/path\\([\"']([^\"']+)[\"']\\)/)[1];\n  const commands = [];\n  const commandRegex = /([MLHVCSQTAZ])([^MLHVCSQTAZ]*)/g;\n  let match;\n  while ((match = commandRegex.exec(pathCommandsStr)) !== null) {\n    const type = match[1];\n    const argsStr = match[2].trim();\n    let args = argsStr ? argsStr.split(/\\s+/) : [];\n\n    // 处理参数，将百分比转换为像素值\n    args = args.map((arg, index) => {\n      if (arg.includes('%')) {\n        const value = parseFloat(arg.replace('%', ''));\n        // 根据索引判断是 x 坐标还是 y 坐标，以确定使用宽度还是高度进行转换\n        return index % 2 === 0 ? value / 100 * width : value / 100 * height;\n      }\n      return parseFloat(arg);\n    });\n    commands.push({\n      type,\n      args\n    });\n  }\n  return commands;\n}\nfunction findCircleCenter(x1, y1, x2, y2, r, largeArcFlag, sweepFlag) {\n  // 计算两点之间的中点\n  const cx = (x1 + x2) / 2;\n  const cy = (y1 + y2) / 2;\n\n  // 计算两点之间的距离\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const d = Math.sqrt(dx * dx + dy * dy);\n\n  // 如果两点之间的距离大于直径，无法构成圆弧\n  if (d > 2 * r) {\n    r = d / 2;\n  }\n\n  // 计算圆心到中点的距离\n  const dist = Math.sqrt(r * r - d / 2 * (d / 2));\n\n  // 计算从 (x1, y1) 到 (x2, y2) 的向量的垂直向量\n  const vx = -dy / d;\n  const vy = dx / d;\n\n  // 根据 largeArcFlag 确定圆心在中垂线的哪一侧\n  let sign = 1;\n  if (largeArcFlag && sweepFlag || !largeArcFlag && !sweepFlag) {\n    sign = -1;\n  }\n\n  // 计算圆心的坐标\n  const centerX = cx + sign * dist * vx;\n  const centerY = cy + sign * dist * vy;\n  return [centerX, centerY];\n}\nfunction clipImagePath(ctx, cmds) {\n  ctx.beginPath();\n  let currentPoint = {\n    x: 0,\n    y: 0\n  };\n  cmds.forEach(command => {\n    const {\n      type,\n      args\n    } = command;\n    switch (type) {\n      case 'M':\n        ctx.moveTo(args[0], args[1]);\n        currentPoint = {\n          x: args[0],\n          y: args[1]\n        };\n        break;\n      case 'L':\n        ctx.lineTo(args[0], args[1]);\n        currentPoint = {\n          x: args[0],\n          y: args[1]\n        };\n        break;\n      case 'A':\n        const [rx, ry, rotation, largeArcFlag, sweepFlag, x, y] = args;\n        const startX = currentPoint.x;\n        const startY = currentPoint.y;\n        const center = findCircleCenter(startX, startY, x, y, rx, largeArcFlag, sweepFlag);\n        const centerX = center[0];\n        const centerY = center[1];\n        const startAngle = Math.atan2(startY - centerY, startX - centerX);\n        const endAngle = Math.atan2(y - centerY, x - centerX);\n        ctx.ellipse(centerX, centerY, rx, ry, rotation * (Math.PI / 180), startAngle, endAngle, sweepFlag === 0);\n        currentPoint = {\n          x: args[5],\n          y: args[6]\n        };\n        break;\n      case 'Z':\n        ctx.closePath();\n        break;\n      default:\n        console.warn(`Unsupported command type: ${type}`);\n    }\n  });\n  ctx.closePath();\n  ctx.clip();\n}\nfunction clipImagePolygon(ctx, config) {\n  ctx.beginPath();\n  config.forEach((point, index) => {\n    index === 0 ? ctx.moveTo(...point) : ctx.lineTo(...point);\n  });\n  ctx.closePath();\n  ctx.clip();\n}\nfunction formatClipPath(canvasSize, clipPath) {\n  if (!clipPath.startsWith('path(')) {\n    return clipPath;\n  }\n\n  // 提取路径部分\n  const startIndex = 'path('.length;\n  const endIndex = clipPath.length - 1;\n  const pathPart = clipPath.slice(startIndex, endIndex);\n\n  // 按空格分割路径部分\n  const parts = pathPart.split(' ');\n\n  // 处理每个部分\n  const newParts = parts.map(part => {\n    // 检查是否为百分数\n    if (/^\\d+(\\.\\d+)?%$/.test(part)) {\n      // 去掉百分号并转换为数字\n      const percentage = parseFloat(part.slice(0, -1));\n      // 计算新值\n      const newValue = canvasSize * (percentage / 100);\n      return newValue.toString();\n    }\n    return part;\n  });\n  // 重新组合路径部分\n  return 'path(' + newParts.join(' ') + ')';\n}\nexport default {\n  clipPathPolygonToPoints,\n  extractPathCommands,\n  clipImagePath,\n  clipImagePolygon,\n  formatClipPath\n};", "map": {"version": 3, "names": ["clipPathPolygonToPoints", "clipPathStr", "width", "height", "polygonStr", "replace", "trim", "pointsStr", "split", "points", "pointStr", "xStr", "yStr", "x", "y", "includes", "parseFloat", "push", "extractPathCommands", "clipPath", "pathCommandsStr", "match", "commands", "commandRegex", "exec", "type", "argsStr", "args", "map", "arg", "index", "value", "findCircleCenter", "x1", "y1", "x2", "y2", "r", "largeArcFlag", "sweepFlag", "cx", "cy", "dx", "dy", "d", "Math", "sqrt", "dist", "vx", "vy", "sign", "centerX", "centerY", "clipImagePath", "ctx", "cmds", "beginPath", "currentPoint", "for<PERSON>ach", "command", "moveTo", "lineTo", "rx", "ry", "rotation", "startX", "startY", "center", "startAngle", "atan2", "endAngle", "ellipse", "PI", "closePath", "console", "warn", "clip", "clipImagePolygon", "config", "point", "formatClipPath", "canvasSize", "startsWith", "startIndex", "length", "endIndex", "pathPart", "slice", "parts", "newParts", "part", "test", "percentage", "newValue", "toString", "join"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/common/Util.js"], "sourcesContent": ["\nfunction clipPathPolygonToPoints(clipPathStr, width, height) {\n    if (clipPathStr === '') {\n        clipPathStr = 'polygon(0 0, 0% 100%, 100% 100%, 100% 0)';\n    }\n    // 去除多余的前缀和后缀，提取出实际的点信息\n    const polygonStr = clipPathStr.replace('polygon(', '').replace(');', '').trim();\n    // 按逗号分割每个点的信息\n    const pointsStr = polygonStr.split(',');\n    const points = [];\n\n    for (let pointStr of pointsStr) {\n        // 去除首尾空格\n        pointStr = pointStr.trim();\n        // 按空格分割出 x 和 y 坐标\n        const [xStr, yStr] = pointStr.split(' ');\n\n        let x, y;\n        if (xStr.includes('%')) {\n            // 如果 x 坐标是百分比，将其转换为像素值\n            x = parseFloat(xStr.replace('%', '')) / 100 * width;\n        } else {\n            x = parseFloat(xStr);\n        }\n\n        if (yStr.includes('%')) {\n            // 如果 y 坐标是百分比，将其转换为像素值\n            y = parseFloat(yStr.replace('%', '')) / 100 * height;\n        } else {\n            y = parseFloat(yStr);\n        }\n\n        points.push([x, y]);\n    }\n\n    return points;\n}\n\n\nfunction extractPathCommands(clipPath, width, height) {\n    // 提取 path 括号内的路径指令字符串\n    const pathCommandsStr = clipPath.match(/path\\([\"']([^\"']+)[\"']\\)/)[1];\n    const commands = [];\n    const commandRegex = /([MLHVCSQTAZ])([^MLHVCSQTAZ]*)/g;\n    let match;\n\n    while ((match = commandRegex.exec(pathCommandsStr)) !== null) {\n        const type = match[1];\n        const argsStr = match[2].trim();\n        let args = argsStr ? argsStr.split(/\\s+/) : [];\n\n        // 处理参数，将百分比转换为像素值\n        args = args.map((arg, index) => {\n            if (arg.includes('%')) {\n                const value = parseFloat(arg.replace('%', ''));\n                // 根据索引判断是 x 坐标还是 y 坐标，以确定使用宽度还是高度进行转换\n                return (index % 2 === 0 ? value / 100 * width : value / 100 * height);\n            }\n            return parseFloat(arg);\n        });\n\n        commands.push({\n            type,\n            args\n        });\n    }\n\n    return commands;\n}\n\nfunction findCircleCenter(x1, y1, x2, y2, r, largeArcFlag, sweepFlag) {\n    // 计算两点之间的中点\n    const cx = (x1 + x2) / 2;\n    const cy = (y1 + y2) / 2;\n\n    // 计算两点之间的距离\n    const dx = x2 - x1;\n    const dy = y2 - y1;\n    const d = Math.sqrt(dx * dx + dy * dy);\n\n    // 如果两点之间的距离大于直径，无法构成圆弧\n    if (d > 2 * r) {\n        r = d / 2\n    }\n\n    // 计算圆心到中点的距离\n    const dist = Math.sqrt(r * r - (d / 2) * (d / 2));\n\n    // 计算从 (x1, y1) 到 (x2, y2) 的向量的垂直向量\n    const vx = -dy / d;\n    const vy = dx / d;\n\n    // 根据 largeArcFlag 确定圆心在中垂线的哪一侧\n    let sign = 1;\n    if ((largeArcFlag && sweepFlag) || (!largeArcFlag && !sweepFlag)) {\n        sign = -1;\n    }\n\n    // 计算圆心的坐标\n    const centerX = cx + sign * dist * vx;\n    const centerY = cy + sign * dist * vy;\n\n    return [centerX, centerY];\n}\n\nfunction clipImagePath(ctx, cmds) {\n    ctx.beginPath();\n    let currentPoint = {\n        x: 0,\n        y: 0\n    }\n    cmds.forEach((command) => {\n        const {\n            type,\n            args\n        } = command;\n        switch (type) {\n            case 'M':\n                ctx.moveTo(args[0], args[1]);\n                currentPoint = {\n                    x: args[0],\n                    y: args[1]\n                }\n                break;\n            case 'L':\n                ctx.lineTo(args[0], args[1]);\n                currentPoint = {\n                    x: args[0],\n                    y: args[1]\n                }\n                break;\n            case 'A':\n                const [rx, ry, rotation, largeArcFlag, sweepFlag, x, y] = args;\n                const startX = currentPoint.x;\n                const startY = currentPoint.y;\n                const center = findCircleCenter(startX, startY, x, y, rx, largeArcFlag, sweepFlag)\n                const centerX = center[0];\n                const centerY = center[1];\n                const startAngle = Math.atan2(startY - centerY, startX - centerX);\n                const endAngle = Math.atan2(y - centerY, x - centerX);\n                ctx.ellipse(centerX, centerY, rx, ry, rotation * (Math.PI / 180), startAngle, endAngle, sweepFlag === 0);\n                currentPoint = {\n                    x: args[5],\n                    y: args[6]\n                }\n                break;\n            case 'Z':\n                ctx.closePath();\n                break;\n            default:\n                console.warn(`Unsupported command type: ${type}`);\n        }\n    });\n    ctx.closePath();\n    ctx.clip();\n}\n\nfunction clipImagePolygon(ctx, config) {\n    ctx.beginPath();\n    config.forEach((point, index) => {\n        index === 0 ? ctx.moveTo(...point) : ctx.lineTo(...point);\n    });\n    ctx.closePath();\n    ctx.clip();\n}\n\n\nfunction formatClipPath(canvasSize, clipPath) {\n    if (!clipPath.startsWith('path(')) {\n        return clipPath;\n    }\n\n    // 提取路径部分\n    const startIndex = 'path('.length;\n    const endIndex = clipPath.length - 1;\n    const pathPart = clipPath.slice(startIndex, endIndex);\n\n    // 按空格分割路径部分\n    const parts = pathPart.split(' ');\n\n    // 处理每个部分\n    const newParts = parts.map(part => {\n        // 检查是否为百分数\n        if (/^\\d+(\\.\\d+)?%$/.test(part)) {\n            // 去掉百分号并转换为数字\n            const percentage = parseFloat(part.slice(0, -1));\n            // 计算新值\n            const newValue = canvasSize * (percentage / 100);\n            return newValue.toString();\n        }\n        return part;\n    });\n    // 重新组合路径部分\n    return 'path(' + newParts.join(' ') + ')';\n}\n\nexport default {\n    clipPathPolygonToPoints,\n    extractPathCommands,\n    clipImagePath,\n    clipImagePolygon,\n    formatClipPath\n};"], "mappings": "AACA,SAASA,uBAAuBA,CAACC,WAAW,EAAEC,KAAK,EAAEC,MAAM,EAAE;EACzD,IAAIF,WAAW,KAAK,EAAE,EAAE;IACpBA,WAAW,GAAG,0CAA0C;EAC5D;EACA;EACA,MAAMG,UAAU,GAAGH,WAAW,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;EAC/E;EACA,MAAMC,SAAS,GAAGH,UAAU,CAACI,KAAK,CAAC,GAAG,CAAC;EACvC,MAAMC,MAAM,GAAG,EAAE;EAEjB,KAAK,IAAIC,QAAQ,IAAIH,SAAS,EAAE;IAC5B;IACAG,QAAQ,GAAGA,QAAQ,CAACJ,IAAI,CAAC,CAAC;IAC1B;IACA,MAAM,CAACK,IAAI,EAAEC,IAAI,CAAC,GAAGF,QAAQ,CAACF,KAAK,CAAC,GAAG,CAAC;IAExC,IAAIK,CAAC,EAAEC,CAAC;IACR,IAAIH,IAAI,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpB;MACAF,CAAC,GAAGG,UAAU,CAACL,IAAI,CAACN,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGH,KAAK;IACvD,CAAC,MAAM;MACHW,CAAC,GAAGG,UAAU,CAACL,IAAI,CAAC;IACxB;IAEA,IAAIC,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpB;MACAD,CAAC,GAAGE,UAAU,CAACJ,IAAI,CAACP,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGF,MAAM;IACxD,CAAC,MAAM;MACHW,CAAC,GAAGE,UAAU,CAACJ,IAAI,CAAC;IACxB;IAEAH,MAAM,CAACQ,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,CAAC,CAAC;EACvB;EAEA,OAAOL,MAAM;AACjB;AAGA,SAASS,mBAAmBA,CAACC,QAAQ,EAAEjB,KAAK,EAAEC,MAAM,EAAE;EAClD;EACA,MAAMiB,eAAe,GAAGD,QAAQ,CAACE,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;EACrE,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,YAAY,GAAG,iCAAiC;EACtD,IAAIF,KAAK;EAET,OAAO,CAACA,KAAK,GAAGE,YAAY,CAACC,IAAI,CAACJ,eAAe,CAAC,MAAM,IAAI,EAAE;IAC1D,MAAMK,IAAI,GAAGJ,KAAK,CAAC,CAAC,CAAC;IACrB,MAAMK,OAAO,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC;IAC/B,IAAIqB,IAAI,GAAGD,OAAO,GAAGA,OAAO,CAAClB,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;;IAE9C;IACAmB,IAAI,GAAGA,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;MAC5B,IAAID,GAAG,CAACd,QAAQ,CAAC,GAAG,CAAC,EAAE;QACnB,MAAMgB,KAAK,GAAGf,UAAU,CAACa,GAAG,CAACxB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAC9C;QACA,OAAQyB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGC,KAAK,GAAG,GAAG,GAAG7B,KAAK,GAAG6B,KAAK,GAAG,GAAG,GAAG5B,MAAM;MACxE;MACA,OAAOa,UAAU,CAACa,GAAG,CAAC;IAC1B,CAAC,CAAC;IAEFP,QAAQ,CAACL,IAAI,CAAC;MACVQ,IAAI;MACJE;IACJ,CAAC,CAAC;EACN;EAEA,OAAOL,QAAQ;AACnB;AAEA,SAASU,gBAAgBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,YAAY,EAAEC,SAAS,EAAE;EAClE;EACA,MAAMC,EAAE,GAAG,CAACP,EAAE,GAAGE,EAAE,IAAI,CAAC;EACxB,MAAMM,EAAE,GAAG,CAACP,EAAE,GAAGE,EAAE,IAAI,CAAC;;EAExB;EACA,MAAMM,EAAE,GAAGP,EAAE,GAAGF,EAAE;EAClB,MAAMU,EAAE,GAAGP,EAAE,GAAGF,EAAE;EAClB,MAAMU,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;;EAEtC;EACA,IAAIC,CAAC,GAAG,CAAC,GAAGP,CAAC,EAAE;IACXA,CAAC,GAAGO,CAAC,GAAG,CAAC;EACb;;EAEA;EACA,MAAMG,IAAI,GAAGF,IAAI,CAACC,IAAI,CAACT,CAAC,GAAGA,CAAC,GAAIO,CAAC,GAAG,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAMI,EAAE,GAAG,CAACL,EAAE,GAAGC,CAAC;EAClB,MAAMK,EAAE,GAAGP,EAAE,GAAGE,CAAC;;EAEjB;EACA,IAAIM,IAAI,GAAG,CAAC;EACZ,IAAKZ,YAAY,IAAIC,SAAS,IAAM,CAACD,YAAY,IAAI,CAACC,SAAU,EAAE;IAC9DW,IAAI,GAAG,CAAC,CAAC;EACb;;EAEA;EACA,MAAMC,OAAO,GAAGX,EAAE,GAAGU,IAAI,GAAGH,IAAI,GAAGC,EAAE;EACrC,MAAMI,OAAO,GAAGX,EAAE,GAAGS,IAAI,GAAGH,IAAI,GAAGE,EAAE;EAErC,OAAO,CAACE,OAAO,EAAEC,OAAO,CAAC;AAC7B;AAEA,SAASC,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC9BD,GAAG,CAACE,SAAS,CAAC,CAAC;EACf,IAAIC,YAAY,GAAG;IACf5C,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACP,CAAC;EACDyC,IAAI,CAACG,OAAO,CAAEC,OAAO,IAAK;IACtB,MAAM;MACFlC,IAAI;MACJE;IACJ,CAAC,GAAGgC,OAAO;IACX,QAAQlC,IAAI;MACR,KAAK,GAAG;QACJ6B,GAAG,CAACM,MAAM,CAACjC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B8B,YAAY,GAAG;UACX5C,CAAC,EAAEc,IAAI,CAAC,CAAC,CAAC;UACVb,CAAC,EAAEa,IAAI,CAAC,CAAC;QACb,CAAC;QACD;MACJ,KAAK,GAAG;QACJ2B,GAAG,CAACO,MAAM,CAAClC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B8B,YAAY,GAAG;UACX5C,CAAC,EAAEc,IAAI,CAAC,CAAC,CAAC;UACVb,CAAC,EAAEa,IAAI,CAAC,CAAC;QACb,CAAC;QACD;MACJ,KAAK,GAAG;QACJ,MAAM,CAACmC,EAAE,EAAEC,EAAE,EAAEC,QAAQ,EAAE1B,YAAY,EAAEC,SAAS,EAAE1B,CAAC,EAAEC,CAAC,CAAC,GAAGa,IAAI;QAC9D,MAAMsC,MAAM,GAAGR,YAAY,CAAC5C,CAAC;QAC7B,MAAMqD,MAAM,GAAGT,YAAY,CAAC3C,CAAC;QAC7B,MAAMqD,MAAM,GAAGnC,gBAAgB,CAACiC,MAAM,EAAEC,MAAM,EAAErD,CAAC,EAAEC,CAAC,EAAEgD,EAAE,EAAExB,YAAY,EAAEC,SAAS,CAAC;QAClF,MAAMY,OAAO,GAAGgB,MAAM,CAAC,CAAC,CAAC;QACzB,MAAMf,OAAO,GAAGe,MAAM,CAAC,CAAC,CAAC;QACzB,MAAMC,UAAU,GAAGvB,IAAI,CAACwB,KAAK,CAACH,MAAM,GAAGd,OAAO,EAAEa,MAAM,GAAGd,OAAO,CAAC;QACjE,MAAMmB,QAAQ,GAAGzB,IAAI,CAACwB,KAAK,CAACvD,CAAC,GAAGsC,OAAO,EAAEvC,CAAC,GAAGsC,OAAO,CAAC;QACrDG,GAAG,CAACiB,OAAO,CAACpB,OAAO,EAAEC,OAAO,EAAEU,EAAE,EAAEC,EAAE,EAAEC,QAAQ,IAAInB,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEJ,UAAU,EAAEE,QAAQ,EAAE/B,SAAS,KAAK,CAAC,CAAC;QACxGkB,YAAY,GAAG;UACX5C,CAAC,EAAEc,IAAI,CAAC,CAAC,CAAC;UACVb,CAAC,EAAEa,IAAI,CAAC,CAAC;QACb,CAAC;QACD;MACJ,KAAK,GAAG;QACJ2B,GAAG,CAACmB,SAAS,CAAC,CAAC;QACf;MACJ;QACIC,OAAO,CAACC,IAAI,CAAC,6BAA6BlD,IAAI,EAAE,CAAC;IACzD;EACJ,CAAC,CAAC;EACF6B,GAAG,CAACmB,SAAS,CAAC,CAAC;EACfnB,GAAG,CAACsB,IAAI,CAAC,CAAC;AACd;AAEA,SAASC,gBAAgBA,CAACvB,GAAG,EAAEwB,MAAM,EAAE;EACnCxB,GAAG,CAACE,SAAS,CAAC,CAAC;EACfsB,MAAM,CAACpB,OAAO,CAAC,CAACqB,KAAK,EAAEjD,KAAK,KAAK;IAC7BA,KAAK,KAAK,CAAC,GAAGwB,GAAG,CAACM,MAAM,CAAC,GAAGmB,KAAK,CAAC,GAAGzB,GAAG,CAACO,MAAM,CAAC,GAAGkB,KAAK,CAAC;EAC7D,CAAC,CAAC;EACFzB,GAAG,CAACmB,SAAS,CAAC,CAAC;EACfnB,GAAG,CAACsB,IAAI,CAAC,CAAC;AACd;AAGA,SAASI,cAAcA,CAACC,UAAU,EAAE9D,QAAQ,EAAE;EAC1C,IAAI,CAACA,QAAQ,CAAC+D,UAAU,CAAC,OAAO,CAAC,EAAE;IAC/B,OAAO/D,QAAQ;EACnB;;EAEA;EACA,MAAMgE,UAAU,GAAG,OAAO,CAACC,MAAM;EACjC,MAAMC,QAAQ,GAAGlE,QAAQ,CAACiE,MAAM,GAAG,CAAC;EACpC,MAAME,QAAQ,GAAGnE,QAAQ,CAACoE,KAAK,CAACJ,UAAU,EAAEE,QAAQ,CAAC;;EAErD;EACA,MAAMG,KAAK,GAAGF,QAAQ,CAAC9E,KAAK,CAAC,GAAG,CAAC;;EAEjC;EACA,MAAMiF,QAAQ,GAAGD,KAAK,CAAC5D,GAAG,CAAC8D,IAAI,IAAI;IAC/B;IACA,IAAI,gBAAgB,CAACC,IAAI,CAACD,IAAI,CAAC,EAAE;MAC7B;MACA,MAAME,UAAU,GAAG5E,UAAU,CAAC0E,IAAI,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChD;MACA,MAAMM,QAAQ,GAAGZ,UAAU,IAAIW,UAAU,GAAG,GAAG,CAAC;MAChD,OAAOC,QAAQ,CAACC,QAAQ,CAAC,CAAC;IAC9B;IACA,OAAOJ,IAAI;EACf,CAAC,CAAC;EACF;EACA,OAAO,OAAO,GAAGD,QAAQ,CAACM,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;AAC7C;AAEA,eAAe;EACX/F,uBAAuB;EACvBkB,mBAAmB;EACnBmC,aAAa;EACbwB,gBAAgB;EAChBG;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}