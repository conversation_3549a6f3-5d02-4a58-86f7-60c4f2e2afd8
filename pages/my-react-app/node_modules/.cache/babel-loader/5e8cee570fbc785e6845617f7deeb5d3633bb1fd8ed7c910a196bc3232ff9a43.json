{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './App.css';\nimport logo from './images/logo.png';\nimport Util from './common/Util.js';\nimport { woodImages, empty, tang, song, yuan, ming, qing, changgui } from './components/Source.js';\nimport Drawer from './components/Drawer';\nimport SplashScreen from './components/SplashScreen';\nimport WoodTooltip from './components/WoodTooltip';\nimport PrivacyPolicy from './components/PrivacyPolicy';\nimport TermsOfUse from './components/TermsOfUse';\nimport WoodSpeciesIntro from './components/WoodSpeciesIntro';\n\n// 获取 electron API\n// @ts-ignore\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  ipcRenderer\n} = window.electron || {};\nfunction App() {\n  _s();\n  // 添加splash screen状态\n  const [showSplash, setShowSplash] = useState(true);\n\n  // 添加页面路由状态\n  const [currentPage, setCurrentPage] = useState('main');\n\n  // 添加license信息状态\n  const [licenseInfo, setLicenseInfo] = useState({\n    isValid: false,\n    expireDate: '',\n    remainingDays: 0\n  });\n  const [canvasSize, setCanvasSize] = useState(0);\n  const canvasRef = useRef(null);\n  const listContentRef = useRef(null);\n  const [selectedAreas, setSeletedAreas] = useState([]);\n  const [hoverArea, setHoverArea] = useState(null);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [drawerItems, setDrawerItems] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(empty);\n  const [history, setHistory] = useState([]);\n  const hoverTimerRef = useRef(null);\n  const lastPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  const [areaImages, setAreaImages] = useState(selectedSource.clipPath.map(() => null));\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveError, setSaveError] = useState(null);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n  const saveSuccessTimerRef = useRef(null);\n  const tooltipTimerRef = useRef(null); // 添加tooltip延迟显示的定时器引用\n\n  // 添加木种提示框状态\n  const [tooltipInfo, setTooltipInfo] = useState({\n    visible: false,\n    wood: null,\n    position: 'bottom',\n    x: 0,\n    y: 0\n  });\n\n  // 处理画布大小调整\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const resizeObserver = new ResizeObserver(entries => {\n      for (let entry of entries) {\n        const {\n          width\n        } = entry.contentRect;\n        setCanvasSize(width);\n      }\n    });\n    resizeObserver.observe(canvas);\n    return () => {\n      resizeObserver.disconnect();\n    };\n  }, []);\n\n  // 定义 handleUndo 函数\n  const handleUndo = () => {\n    if (history.length > 0) {\n      const newHistory = [...history];\n      setAreaImages(newHistory.pop());\n      setHistory(newHistory);\n    }\n  };\n\n  // 处理键盘快捷键\n  useEffect(() => {\n    const handleKeyDown = e => {\n      // 检查是否按下了 Command/Ctrl + Z\n      if ((e.metaKey || e.ctrlKey) && e.key === 'z') {\n        e.preventDefault(); // 阻止默认行为\n        handleUndo();\n      }\n    };\n\n    // 添加键盘事件监听器\n    window.addEventListener('keydown', handleKeyDown);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [history, handleUndo]);\n\n  // 获取 license 信息\n  useEffect(() => {\n    // 通过 IPC 获取 license 信息\n    ipcRenderer === null || ipcRenderer === void 0 ? void 0 : ipcRenderer.send('get-license-info');\n\n    // 监听 license 信息返回\n    ipcRenderer === null || ipcRenderer === void 0 ? void 0 : ipcRenderer.on('license-info', data => {\n      if (data) {\n        // 格式化日期为 yyyy-MM-dd\n        const expireDate = new Date(data.expireTime);\n        const year = expireDate.getFullYear();\n        const month = String(expireDate.getMonth() + 1).padStart(2, '0');\n        const day = String(expireDate.getDate()).padStart(2, '0');\n        const formattedDate = `${year}-${month}-${day}`;\n        setLicenseInfo({\n          isValid: data.isValid,\n          expireDate: formattedDate,\n          remainingDays: data.remainingDays\n        });\n      }\n    });\n\n    // 清理函数\n    return () => {\n      ipcRenderer === null || ipcRenderer === void 0 ? void 0 : ipcRenderer.removeAllListeners('license-info');\n    };\n  }, []);\n\n  // 清理定时器\n  useEffect(() => {\n    return () => {\n      // 清理悬停定时器\n      if (hoverTimerRef.current) {\n        clearTimeout(hoverTimerRef.current);\n      }\n\n      // 清理成功提示定时器\n      if (saveSuccessTimerRef.current) {\n        clearTimeout(saveSuccessTimerRef.current);\n      }\n\n      // 清理tooltip显示定时器\n      if (tooltipTimerRef.current) {\n        clearTimeout(tooltipTimerRef.current);\n      }\n    };\n  }, []);\n\n  // 直接显示主应用界面\n\n  const handleWoodSelect = woodId => {\n    if (selectedAreas.length === 0) {\n      return;\n    }\n    let newAreaImages = [...areaImages];\n    setHistory([...history, areaImages]);\n    const selectedWood = woodImages.find(t => t.id === woodId);\n    selectedAreas.forEach(t => {\n      newAreaImages[t] = selectedWood;\n    });\n    setAreaImages(newAreaImages);\n    setSeletedAreas([]);\n  };\n  const handleDynastyClick = dynasty => {\n    let items = [];\n    switch (dynasty) {\n      case '唐':\n        items = tang;\n        break;\n      case '宋':\n        items = song;\n        break;\n      case '元':\n        items = yuan;\n        break;\n      case '明':\n        items = ming;\n        break;\n      case '清':\n        items = qing;\n        break;\n    }\n    setDrawerItems(items);\n    setIsDrawerOpen(true);\n  };\n  const handleDrawerSelect = item => {\n    setSeletedAreas([]);\n    setAreaImages(item.clipPath.map(() => null));\n    setHistory([]);\n    setSelectedSource(item);\n    setIsDrawerOpen(false);\n  };\n  const scrollToIndex = index => {\n    if (index === null) {\n      return;\n    }\n    if (listContentRef.current) {\n      const itemWidth = 70;\n      const gap = 10;\n      const scrollPosition = index * (itemWidth + gap);\n      listContentRef.current.scrollTo({\n        left: scrollPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n  const handleListContentWheel = e => {\n    if (listContentRef.current) {\n      e.preventDefault();\n      listContentRef.current.scrollLeft += e.deltaY;\n    }\n  };\n  const handleAreaImageClick = index => {\n    if (selectedAreas.includes(index)) {\n      setSeletedAreas(prev => prev.filter(t => t !== index));\n    } else {\n      setSeletedAreas([...selectedAreas, index]);\n    }\n  };\n  function checkElementPos(clientX, clientY, left, top, width, height) {\n    let hitElement = null;\n    selectedSource.clipPath.forEach((path, index) => {\n      const canvas = document.createElement('canvas');\n      canvas.width = width;\n      canvas.height = height;\n      const ctx = canvas.getContext('2d');\n      ctx.beginPath();\n      if (path == '' || path.startsWith('polygon(')) {\n        const points = Util.clipPathPolygonToPoints(path, width, height);\n        Util.clipImagePolygon(ctx, points);\n      } else if (path.startsWith('path(')) {\n        const cmds = Util.extractPathCommands(path, width, height);\n        Util.clipImagePath(ctx, cmds);\n      }\n      ctx.clip();\n      if (ctx.isPointInPath(clientX - left, clientY - top)) {\n        hitElement = index;\n      }\n    });\n    return hitElement;\n  }\n  function clear() {\n    setSeletedAreas([]);\n    setAreaImages(selectedSource.clipPath.map(() => null));\n    setHistory([]);\n  }\n  async function drawCanvas(canvas, ctx, size) {\n    // 异步加载图片\n    function loadImage(src) {\n      return new Promise(resolve => {\n        const img = new Image();\n        img.src = src;\n        img.onload = () => resolve(img);\n      });\n    }\n    async function drawImage(ctx, imgPath) {\n      if (!imgPath) {\n        ctx.fillStyle = 'white';\n        ctx.fillRect(0, 0, size, size);\n        return;\n      }\n      const img = await loadImage(imgPath);\n      const targetWidth = 1500;\n      const scale = targetWidth / img.width;\n      const targetHeight = Math.floor(img.height * scale);\n      for (let y = 0; y < canvas.height; y += targetHeight) {\n        for (let x = 0; x < canvas.width; x += targetWidth) {\n          ctx.drawImage(img, x, y, targetWidth, targetHeight);\n        }\n      }\n    }\n    for (let i = 0; i < selectedSource.clipPath.length; i++) {\n      var _areaImages$i;\n      const clipPath = selectedSource.clipPath[i];\n      ctx.save();\n      if (clipPath == '' || clipPath.startsWith('polygon(')) {\n        const points = Util.clipPathPolygonToPoints(clipPath, size, size);\n        Util.clipImagePolygon(ctx, points);\n      } else if (clipPath.startsWith('path(')) {\n        const cmds = Util.extractPathCommands(clipPath, size, size);\n        Util.clipImagePath(ctx, cmds);\n      }\n      await drawImage(ctx, (_areaImages$i = areaImages[i]) === null || _areaImages$i === void 0 ? void 0 : _areaImages$i.src);\n      ctx.strokeStyle = 'black';\n      ctx.lineWidth = 3;\n      ctx.stroke();\n      ctx.restore();\n    }\n    // if (selectedSource.bg) {\n    //     ctx.save();\n    //     const points = Util.clipPathPolygonToPoints('', size, size)\n    //     Util.clipImagePolygon(ctx, points)\n    //     const img = await loadImage(selectedSource.bg);\n    //     ctx.drawImage(img, 0, 0, size, size);\n    //     ctx.restore();\n    // }\n  }\n\n  // 生成雪花算法ID（机器码+时间戳+随机数，6位字符）\n  function generateSnowflakeId() {\n    // 字符集：大写字母、数字、小写字母\n    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n\n    // 获取机器码（通过IPC从主进程获取）\n    let machineCode = 0;\n    try {\n      // 在React环境中，通过IPC获取机器ID\n      // @ts-ignore\n      if (window.electron && window.electron.ipcRenderer) {\n        // 同步获取机器ID的hash值\n        // @ts-ignore\n        const machineId = window.electron.ipcRenderer.sendSync('get-machine-id');\n        if (machineId) {\n          // 将机器ID转换为数字hash\n          machineCode = machineId.split('').reduce((hash, char) => {\n            return (hash << 5) - hash + char.charCodeAt(0) & 0xffffffff;\n          }, 0);\n        }\n      }\n    } catch (error) {\n      console.warn('无法获取机器ID，使用备用方案');\n      // 备用方案：使用navigator.userAgent\n      machineCode = navigator.userAgent.split('').reduce((hash, char) => {\n        return (hash << 5) - hash + char.charCodeAt(0) & 0xffffffff;\n      }, 0);\n    }\n\n    // 获取时间戳\n    const timestamp = Date.now();\n\n    // 生成随机数\n    const random = Math.floor(Math.random() * 1000000);\n\n    // 组合所有因子\n    const combined = Math.abs(machineCode) + timestamp + random;\n\n    // 转换为6位字符\n    let result = '';\n    let num = combined;\n    for (let i = 0; i < 6; i++) {\n      result = charset[num % charset.length] + result;\n      num = Math.floor(num / charset.length);\n    }\n\n    // 如果结果不足6位，用随机字符补齐\n    while (result.length < 6) {\n      result = charset[Math.floor(Math.random() * charset.length)] + result;\n    }\n    return result.substring(0, 6); // 确保只返回6位\n  }\n\n  // 获取当前选择的系列名称\n  function getCurrentSeries() {\n    if (!selectedSource || !selectedSource.path || selectedSource.path === 'empty') {\n      return '';\n    }\n\n    // 从路径中提取系列名称，如 \"tang/1\" -> \"唐\"\n    const pathParts = selectedSource.path.split('/');\n    const seriesKey = pathParts[0];\n    const seriesMap = {\n      'tang': '唐',\n      'song': '宋',\n      'yuan': '元',\n      'ming': '明',\n      'qing': '清'\n    };\n    return {\n      name: seriesMap[seriesKey] || seriesKey,\n      shortcut: seriesKey.charAt(0).toUpperCase()\n    };\n  }\n\n  // 获取选用的木种信息\n  function getSelectedWoodNames() {\n    const selectedWoods = areaImages.filter(wood => wood !== null);\n    if (selectedWoods.length === 0) {\n      return '未选择';\n    }\n\n    // 去重并获取木种名称\n    const uniqueWoodNames = [...new Set(selectedWoods.map(wood => wood.name))];\n    return uniqueWoodNames.join('、');\n  }\n\n  // 格式化当前日期\n  function formatCurrentDate() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = now.getMonth() + 1; // 月份从0开始，需要+1\n    const day = now.getDate();\n    return `${year}.${month}.${day}`;\n  }\n  function formatCurrentDateToYYYYMMDD() {\n    const today = new Date();\n    const year = today.getFullYear();\n    // getMonth() 返回0-11，需+1得到实际月份，并补零到2位\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    // getDate() 返回1-31，直接补零到2位\n    const day = String(today.getDate()).padStart(2, '0');\n    return `${year}${month}${day}`;\n  }\n  function buildProjectInfo() {\n    const currentSeries = getCurrentSeries();\n    const currentDate = formatCurrentDate();\n    const currentYYYYMMDD = formatCurrentDateToYYYYMMDD();\n    const snowflakeId = generateSnowflakeId();\n    const seriesPrefix = currentSeries ? currentSeries.shortcut : 'X'; // 取系列名称首字母，如果没有系列则用X\n\n    return {\n      id: `BOER-${currentYYYYMMDD}-${snowflakeId}-GDPH`,\n      code: `${seriesPrefix}-${currentYYYYMMDD}-${snowflakeId}`,\n      // 系列-日期-雪花算法id\n      date: currentDate,\n      // 保存时的日期\n      woodCombined: getSelectedWoodNames(),\n      // 选用了哪些木种\n      unitPrice: calculateTotalPrice().toFixed(0),\n      series: currentSeries.name // 选择时的目录名\n    };\n  }\n\n  // 文本换行辅助函数\n  const wrapText = (ctx, text, maxWidth) => {\n    const words = text.split('');\n    const lines = [];\n    let currentLine = '';\n    for (let i = 0; i < words.length; i++) {\n      const testLine = currentLine + words[i];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      if (testWidth > maxWidth && currentLine !== '') {\n        lines.push(currentLine);\n        currentLine = words[i];\n      } else {\n        currentLine = testLine;\n      }\n    }\n    lines.push(currentLine);\n    return lines;\n  };\n\n  // 绘制多行文本\n  const drawMultilineText = (ctx, text, x, y, maxWidth, lineHeight, align = 'center') => {\n    const lines = wrapText(ctx, text, maxWidth);\n    const totalHeight = lines.length * lineHeight;\n    const startY = y - totalHeight / 2 + lineHeight / 2;\n    lines.forEach((line, index) => {\n      const lineY = startY + index * lineHeight;\n      ctx.textAlign = align;\n      ctx.fillText(line, x, lineY);\n    });\n    return totalHeight;\n  };\n\n  // 绘制表格到画布\n  const drawTableToCanvas = (ctx, x, y, width, title, headers, data) => {\n    const baseRowHeight = 40;\n    const headerHeight = 50;\n    const titleHeight = 40;\n    const lineHeight = 18;\n    const cellPadding = 10;\n\n    // 设置字体\n    ctx.font = '16px Arial, sans-serif';\n    ctx.fillStyle = '#333';\n\n    // 绘制标题\n    ctx.fillStyle = '#000';\n    ctx.font = 'bold 18px Arial, sans-serif';\n    ctx.textAlign = 'left';\n    ctx.fillText(title, x, y + titleHeight - 10);\n\n    // 计算列宽\n    const colWidth = width / headers.length;\n\n    // 计算数据行需要的高度\n    ctx.font = '14px Arial, sans-serif';\n    let maxLinesInRow = 1;\n    data.forEach(cellData => {\n      const maxCellWidth = colWidth - cellPadding * 2;\n      const lines = wrapText(ctx, String(cellData), maxCellWidth);\n      maxLinesInRow = Math.max(maxLinesInRow, lines.length);\n    });\n    const actualRowHeight = Math.max(baseRowHeight, maxLinesInRow * lineHeight + cellPadding * 2);\n\n    // 绘制表头背景\n    ctx.fillStyle = '#f5f5f5';\n    ctx.fillRect(x, y + titleHeight, width, headerHeight);\n\n    // 绘制表头边框\n    ctx.strokeStyle = '#ddd';\n    ctx.lineWidth = 1;\n    ctx.strokeRect(x, y + titleHeight, width, headerHeight);\n\n    // 绘制表头文字\n    ctx.fillStyle = '#333';\n    ctx.font = 'bold 14px Arial, sans-serif';\n    headers.forEach((header, index) => {\n      const cellX = x + index * colWidth;\n      const textX = cellX + colWidth / 2;\n      const textY = y + titleHeight + headerHeight / 2 + 5;\n      ctx.textAlign = 'center';\n      ctx.fillText(header, textX, textY);\n\n      // 绘制列分隔线\n      if (index > 0) {\n        ctx.beginPath();\n        ctx.moveTo(cellX, y + titleHeight);\n        ctx.lineTo(cellX, y + titleHeight + headerHeight);\n        ctx.stroke();\n      }\n    });\n\n    // 绘制数据行背景\n    ctx.fillStyle = '#fff';\n    ctx.fillRect(x, y + titleHeight + headerHeight, width, actualRowHeight);\n\n    // 绘制数据行边框\n    ctx.strokeRect(x, y + titleHeight + headerHeight, width, actualRowHeight);\n\n    // 绘制数据\n    ctx.fillStyle = '#333';\n    ctx.font = '14px Arial, sans-serif';\n    data.forEach((cellData, index) => {\n      const cellX = x + index * colWidth;\n      const textX = cellX + colWidth / 2;\n      const textY = y + titleHeight + headerHeight + actualRowHeight / 2;\n      const maxCellWidth = colWidth - cellPadding * 2;\n      drawMultilineText(ctx, String(cellData), textX, textY, maxCellWidth, lineHeight, 'center');\n\n      // 绘制列分隔线\n      if (index > 0) {\n        ctx.beginPath();\n        ctx.moveTo(cellX, y + titleHeight + headerHeight);\n        ctx.lineTo(cellX, y + titleHeight + headerHeight + actualRowHeight);\n        ctx.stroke();\n      }\n    });\n    return titleHeight + headerHeight + actualRowHeight + 20; // 返回表格总高度\n  };\n\n  // 保存画布为图片并下载\n  const handleSaveCanvas = () => {\n    // 设置保存状态和清除错误/成功提示\n    setIsSaving(true);\n    setSaveError(null);\n    setSaveSuccess(false);\n\n    // 清除之前的成功提示定时器\n    if (saveSuccessTimerRef.current) {\n      clearTimeout(saveSuccessTimerRef.current);\n      saveSuccessTimerRef.current = null;\n    }\n    const areaCanvas = document.createElement('canvas');\n    const size = 500;\n    areaCanvas.width = size;\n    areaCanvas.height = size;\n    drawCanvas(areaCanvas, areaCanvas.getContext('2d'), size).then(() => {\n      // 创建包含图像和表格的完整画布\n      createCompleteCanvas(areaCanvas);\n    });\n  };\n\n  // 创建包含图像和表格的完整画布\n  const createCompleteCanvas = originalCanvas => {\n    const projectInfo = buildProjectInfo();\n\n    // 计算有效期（30天后）\n    const validDate = new Date();\n    validDate.setDate(validDate.getDate() + 30);\n    const validDateStr = `${validDate.getFullYear()}.${validDate.getMonth() + 1}.${validDate.getDate()}`;\n\n    // 创建新的画布，高度增加以容纳表格\n    const completeCanvas = document.createElement('canvas');\n    const originalCanvasPaddingY = 100;\n    const originalCanvasWidth = originalCanvas.width;\n    const canvasWidth = originalCanvasWidth * 2;\n    const tableHeight = 400; // 两个表格的总高度\n    const padding = 40;\n    completeCanvas.width = canvasWidth;\n    completeCanvas.height = originalCanvasWidth + originalCanvasPaddingY + tableHeight + padding;\n    const ctx = completeCanvas.getContext('2d');\n\n    // 设置白色背景\n    ctx.fillStyle = '#fff';\n    ctx.fillRect(0, 0, completeCanvas.width, completeCanvas.height);\n\n    // 绘制原始图像\n    ctx.drawImage(originalCanvas, (canvasWidth - originalCanvas.width) / 2, originalCanvasPaddingY);\n\n    // 准备表格数据\n    const quoteHeaders = ['报价单号', '报价日期', '单价（元/㎡）', '报价有效期'];\n    const quoteData = [projectInfo.id, projectInfo.date, `${projectInfo.unitPrice}`, validDateStr];\n    const productHeaders = ['系列分类', '图纸编码', '树种组合', '产品规格'];\n    const productData = [projectInfo.series || '未选择', projectInfo.code, projectInfo.woodCombined, '60×60cm'];\n\n    // 绘制第一个表格（报价概要）\n    const table1Y = originalCanvasWidth + originalCanvasPaddingY + 20;\n    const table1Height = drawTableToCanvas(ctx, 50, table1Y, canvasWidth - 100, '报价概要', quoteHeaders, quoteData);\n\n    // 绘制第二个表格（产品清单）\n    const table2Y = table1Y + table1Height + 10;\n    drawTableToCanvas(ctx, 50, table2Y, canvasWidth - 100, '产品清单', productHeaders, productData);\n    // 下载完整画布\n    downloadCanvas(completeCanvas);\n  };\n\n  // 下载 canvas 为图片\n  const downloadCanvas = canvas => {\n    try {\n      // 重置保存状态并显示成功提示\n      setIsSaving(false);\n      setSaveSuccess(true);\n\n      // 3秒后自动隐藏成功提示\n      saveSuccessTimerRef.current = setTimeout(() => {\n        setSaveSuccess(false);\n      }, 3000);\n\n      // 创建一个链接元素\n      const link = document.createElement('a');\n\n      // 将 canvas 转换为数据 URL\n      const dataUrl = canvas.toDataURL('image/png');\n\n      // 设置链接属性\n      link.href = dataUrl;\n      const projectInfo = buildProjectInfo();\n      link.download = `柏尔地板设计报价单_${projectInfo.code}.png`;\n\n      // 模拟点击链接\n      document.body.appendChild(link);\n      link.click();\n\n      // 清理\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('Error downloading canvas:', error);\n      setIsSaving(false);\n      setSaveError('保存图片时出错，请重试');\n    }\n  };\n  function calculateTotalPrice() {\n    if (!(selectedSource !== null && selectedSource !== void 0 && selectedSource.area) || areaImages.filter(t => t === null).length > 0) {\n      return 0;\n    }\n    let result = 0;\n    for (let i = 0; i < areaImages.length; i++) {\n      if (areaImages[i] && i < selectedSource.area.length) {\n        result += areaImages[i].price * selectedSource.area[i];\n      }\n    }\n    result *= 1 / 0.36;\n    if (selectedSource.profit) {\n      result += selectedSource.profit;\n    }\n    return result * 3;\n  }\n  function checkClickElement(e) {\n    const {\n      clientX,\n      clientY\n    } = e;\n    const {\n      left,\n      top,\n      width,\n      height\n    } = e.target.getBoundingClientRect();\n    let hitElement = checkElementPos(clientX, clientY, left, top, width, height);\n    if (hitElement !== null) {\n      if (selectedAreas.includes(hitElement)) {\n        setSeletedAreas(prev => prev.filter(t => t !== hitElement));\n      } else {\n        setSeletedAreas(prev => [...prev, hitElement]);\n      }\n      // alert(selectedSource.clipPath[hitElement])\n      scrollToIndex(hitElement);\n    }\n  }\n  const handleMouseMove = e => {\n    const {\n      clientX,\n      clientY\n    } = e;\n    const {\n      left,\n      top,\n      width,\n      height\n    } = e.target.getBoundingClientRect();\n    if (clientX === lastPositionRef.current.x && clientY === lastPositionRef.current.y) {\n      return;\n    }\n    lastPositionRef.current = {\n      x: clientX,\n      y: clientY\n    };\n    if (hoverTimerRef.current) {\n      clearTimeout(hoverTimerRef.current);\n    }\n    hoverTimerRef.current = setTimeout(() => {\n      const hitElement = checkElementPos(clientX, clientY, left, top, width, height);\n      setHoverArea(hitElement);\n      scrollToIndex(hitElement);\n    }, 40);\n  };\n\n  // 渲染页面内容\n  const renderPageContent = () => {\n    let display = 'block';\n    let item = '';\n    if (currentPage === 'privacy') {\n      display = 'none';\n      item = /*#__PURE__*/_jsxDEV(PrivacyPolicy, {\n        setCurrentPage: setCurrentPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 767,\n        columnNumber: 14\n      }, this);\n    } else if (currentPage === 'terms') {\n      display = 'none';\n      item = /*#__PURE__*/_jsxDEV(TermsOfUse, {\n        setCurrentPage: setCurrentPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 770,\n        columnNumber: 14\n      }, this);\n    } else if (currentPage === 'wood-species') {\n      display = 'none';\n      item = /*#__PURE__*/_jsxDEV(WoodSpeciesIntro, {\n        setCurrentPage: setCurrentPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [item, /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: display\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tool-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"group-choice\",\n              children: \"\\u62FC\\u82B1\\u82B1\\u578B\\u9009\\u62E9\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: () => handleDynastyClick('唐'),\n              children: \"\\u5510\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: () => handleDynastyClick('宋'),\n              children: \"\\u5B8B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: () => handleDynastyClick('元'),\n              children: \"\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: () => handleDynastyClick('明'),\n              children: \"\\u660E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: () => handleDynastyClick('清'),\n              children: \"\\u6E05\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tool-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: () => clear(),\n              children: \"\\u6E05\\u7A7A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: handleUndo,\n              disabled: history.length === 0,\n              children: \"\\u64A4\\u9500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"tool-button\",\n              onClick: handleSaveCanvas,\n              disabled: isSaving || selectedSource.clipPath.length === 0 || areaImages.filter(t => t === null).length > 0,\n              children: isSaving ? '正在保存...' : '保存'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 15\n            }, this), saveError && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"save-error\",\n              children: saveError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 29\n            }, this), saveSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"save-success\",\n              children: \"\\u56FE\\u7247\\u5DF2\\u4FDD\\u5B58\\u6210\\u529F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"main-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u9009\\u62E9\\u6728\\u79CD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 20\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-selection\",\n              children: woodImages.map(wood => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: e => handleWoodSelect(wood.id, e),\n                className: \"wood-item\",\n                onMouseEnter: e => {\n                  // 清除之前的定时器（如果存在）\n                  if (tooltipTimerRef.current) {\n                    clearTimeout(tooltipTimerRef.current);\n                  }\n\n                  // 立即获取元素位置\n                  const rect = e.currentTarget.getBoundingClientRect();\n                  const tooltipX = rect.right + 10;\n\n                  // 判断tooltip的垂直位置，避免被屏幕底部遮挡\n                  let tooltipY;\n                  const tooltipHeight = 510; // 估计的tooltip高度（增加70%后）\n                  const windowHeight = window.innerHeight;\n\n                  // 如果元素位置太靠下，将tooltip显示在元素上方\n                  if (rect.top + tooltipHeight > windowHeight) {\n                    tooltipY = Math.max(10, windowHeight - tooltipHeight - 20); // 确保至少有10px的上边距\n                  } else {\n                    tooltipY = rect.top - 60; // 默认位置\n                  }\n\n                  // 设置1秒延迟后显示tooltip\n                  tooltipTimerRef.current = setTimeout(() => {\n                    setTooltipInfo({\n                      visible: true,\n                      wood: wood,\n                      position: 'bottom',\n                      x: tooltipX,\n                      y: tooltipY\n                    });\n                  }, 1000); // 1秒延迟\n                },\n                onMouseLeave: () => {\n                  // 清除显示定时器\n                  if (tooltipTimerRef.current) {\n                    clearTimeout(tooltipTimerRef.current);\n                    tooltipTimerRef.current = null;\n                  }\n\n                  // 设置延迟隐藏tooltip（给用户时间移动到tooltip上）\n                  tooltipTimerRef.current = setTimeout(() => {\n                    setTooltipInfo(prev => ({\n                      ...prev,\n                      visible: false\n                    }));\n                  }, 300); // 300ms延迟\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: wood.src,\n                  alt: wood.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: wood.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 21\n                }, this)]\n              }, wood.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"canvas-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: canvasRef,\n              className: \"canvas\",\n              onClick: e => checkClickElement(e),\n              onMouseMove: handleMouseMove,\n              onMouseLeave: () => {\n                if (hoverTimerRef.current) {\n                  clearTimeout(hoverTimerRef.current);\n                }\n                setHoverArea(null);\n              },\n              children: [selectedSource.clipPath.map((path, index) => {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"clip-area\",\n                  style: {\n                    clipPath: Util.formatClipPath(canvasSize, path),\n                    position: 'absolute',\n                    width: '100%',\n                    height: '100%',\n                    backgroundSize: `${parseInt(canvasSize * 1.5)}px auto`,\n                    backgroundRepeat: 'repeat',\n                    backgroundPosition: '0 0',\n                    backgroundColor: 'white',\n                    backgroundImage: areaImages[index] && `url(${areaImages[index].src})`\n                  },\n                  children: [selectedAreas.includes(index) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overlay\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 57\n                  }, this), hoverArea === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hover-area\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 47\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  zIndex: 999\n                },\n                children: selectedSource.bg && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: selectedSource.bg,\n                  alt: \"bg\",\n                  style: {\n                    width: '100%',\n                    height: '100%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 43\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this), selectedSource.clipPath.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"area-images-list\",\n              style: {\n                width: canvasSize + 20,\n                marginLeft: '14%'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-content\",\n                ref: listContentRef,\n                onWheel: handleListContentWheel,\n                children: areaImages.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"area-image-item\",\n                  onClick: () => handleAreaImageClick(index),\n                  onMouseEnter: () => setHoverArea(index),\n                  onMouseLeave: () => setHoverArea(null),\n                  children: [image && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: image.src,\n                    style: {\n                      width: '100%',\n                      height: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 35\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"list-area-span\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"list-image-span\",\n                    children: image ? image.name : '未选择'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 25\n                  }, this), selectedAreas.includes(index) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overlay\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 59\n                  }, this), hoverArea === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hover-area\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 49\n                  }, this)]\n                }, index + 1, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 17\n            }, this), selectedSource.area &&\n            /*#__PURE__*/\n            /* 计算价格 */\n            _jsxDEV(\"div\", {\n              className: \"total-price-area\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-price-content\",\n                children: calculateTotalPrice().toFixed(0) + ' 元/㎡'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n          isOpen: isDrawerOpen,\n          onClose: () => setIsDrawerOpen(false),\n          items: drawerItems,\n          onSelect: handleDrawerSelect\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 11\n        }, this), tooltipInfo.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'fixed',\n            top: tooltipInfo.y + 'px',\n            left: tooltipInfo.x + 'px',\n            zIndex: 2000\n          },\n          onMouseEnter: () => {\n            // 鼠标进入tooltip时，清除隐藏定时器\n            if (tooltipTimerRef.current) {\n              clearTimeout(tooltipTimerRef.current);\n              tooltipTimerRef.current = null;\n            }\n          },\n          onMouseLeave: () => {\n            // 鼠标离开tooltip时，隐藏tooltip\n            setTooltipInfo(prev => ({\n              ...prev,\n              visible: false\n            }));\n          },\n          children: /*#__PURE__*/_jsxDEV(WoodTooltip, {\n            wood: tooltipInfo.wood,\n            position: tooltipInfo.position,\n            visible: tooltipInfo.visible\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [showSplash && /*#__PURE__*/_jsxDEV(SplashScreen, {\n      onFinish: () => setShowSplash(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 999,\n      columnNumber: 22\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: showSplash ? 'none' : 'block'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: logo,\n              alt: \"\\u67CF\\u5C14\\u5730\\u677F\",\n              className: \"logo\",\n              style: {\n                cursor: 'pointer'\n              },\n              onClick: () => setCurrentPage('main')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"slogan\",\n              children: \"\\u4E2D\\u56FD\\u9AD8\\u7AEF\\u5B9E\\u6728\\u5B9A\\u5236\\u5730\\u677F\\u5168\\u56FD\\u9500\\u91CF\\u7B2C\\u4E00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-section\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 9\n      }, this), renderPageContent(), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"footer\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2025 \\u67CF\\u5C14\\u6728\\u4E1A\\u6709\\u9650\\u516C\\u53F8. \\u4FDD\\u7559\\u6240\\u6709\\u6743\\u5229\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1027,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1026,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-links\",\n            children: [licenseInfo.expireDate && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"license-info\",\n              children: [\"\\u6388\\u6743\\u6709\\u6548\\u671F\\u81F3: \", licenseInfo.expireDate]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"separator\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              onClick: e => {\n                e.preventDefault();\n                setCurrentPage('wood-species');\n              },\n              children: \"\\u6728\\u79CD\\u4ECB\\u7ECD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"separator\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              onClick: e => {\n                e.preventDefault();\n                setCurrentPage('privacy');\n              },\n              children: \"\\u9690\\u79C1\\u653F\\u7B56\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"separator\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              onClick: e => {\n                e.preventDefault();\n                setCurrentPage('terms');\n              },\n              children: \"\\u4F7F\\u7528\\u6761\\u6B3E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1000,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 998,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"7/XMBrFiV2fSOXtmJZSZAIXrehw=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "logo", "<PERSON><PERSON>", "woodImages", "empty", "tang", "song", "yuan", "ming", "qing", "chang<PERSON>i", "Drawer", "SplashScreen", "WoodTooltip", "PrivacyPolicy", "TermsOfUse", "WoodSpeciesIntro", "jsxDEV", "_jsxDEV", "ip<PERSON><PERSON><PERSON><PERSON>", "window", "electron", "App", "_s", "showSplash", "setShowSplash", "currentPage", "setCurrentPage", "licenseInfo", "setLicenseInfo", "<PERSON><PERSON><PERSON><PERSON>", "expireDate", "remainingDays", "canvasSize", "setCanvasSize", "canvasRef", "listContentRef", "<PERSON><PERSON><PERSON><PERSON>", "setSeletedAreas", "hoverArea", "setHoverArea", "isDrawerOpen", "setIsDrawerOpen", "drawerItems", "setDrawerItems", "selectedSource", "setSelectedSource", "history", "setHistory", "hoverTimerRef", "lastPositionRef", "x", "y", "areaImages", "setAreaImages", "clipPath", "map", "isSaving", "setIsSaving", "saveError", "setSaveError", "saveSuccess", "setSaveSuccess", "saveSuccessTimerRef", "tooltipTimerRef", "tooltipInfo", "setTooltipInfo", "visible", "wood", "position", "canvas", "current", "resizeObserver", "ResizeObserver", "entries", "entry", "width", "contentRect", "observe", "disconnect", "handleUndo", "length", "newHistory", "pop", "handleKeyDown", "e", "metaKey", "ctrl<PERSON>ey", "key", "preventDefault", "addEventListener", "removeEventListener", "send", "on", "data", "Date", "expireTime", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "formattedDate", "removeAllListeners", "clearTimeout", "handleWoodSelect", "woodId", "newAreaImages", "<PERSON><PERSON>ood", "find", "t", "id", "for<PERSON>ach", "handleDynastyClick", "dynasty", "items", "handleDrawerSelect", "item", "scrollToIndex", "index", "itemWidth", "gap", "scrollPosition", "scrollTo", "left", "behavior", "handleListContentWheel", "scrollLeft", "deltaY", "handleAreaImageClick", "includes", "prev", "filter", "checkElementPos", "clientX", "clientY", "top", "height", "hitElement", "path", "document", "createElement", "ctx", "getContext", "beginPath", "startsWith", "points", "clipPathPolygonToPoints", "clipImagePolygon", "cmds", "extractPathCommands", "clipImagePath", "clip", "isPointInPath", "clear", "draw<PERSON><PERSON>vas", "size", "loadImage", "src", "Promise", "resolve", "img", "Image", "onload", "drawImage", "imgPath", "fillStyle", "fillRect", "targetWidth", "scale", "targetHeight", "Math", "floor", "i", "_areaImages$i", "save", "strokeStyle", "lineWidth", "stroke", "restore", "generateSnowflakeId", "charset", "machineCode", "machineId", "sendSync", "split", "reduce", "hash", "char", "charCodeAt", "error", "console", "warn", "navigator", "userAgent", "timestamp", "now", "random", "combined", "abs", "result", "num", "substring", "getCurrentSeries", "pathParts", "series<PERSON>ey", "seriesMap", "name", "shortcut", "char<PERSON>t", "toUpperCase", "getSelectedWoodNames", "selectedWoods", "uniqueWoodNames", "Set", "join", "formatCurrentDate", "formatCurrentDateToYYYYMMDD", "today", "buildProjectInfo", "currentSeries", "currentDate", "currentYYYYMMDD", "snowflakeId", "seriesPrefix", "code", "date", "woodCombined", "unitPrice", "calculateTotalPrice", "toFixed", "series", "wrapText", "text", "max<PERSON><PERSON><PERSON>", "words", "lines", "currentLine", "testLine", "metrics", "measureText", "testWidth", "push", "drawMultilineText", "lineHeight", "align", "totalHeight", "startY", "line", "lineY", "textAlign", "fillText", "drawTableToCanvas", "title", "headers", "baseRowHeight", "headerHeight", "titleHeight", "cellPadding", "font", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLinesInRow", "cellData", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max", "actualRowHeight", "strokeRect", "header", "cellX", "textX", "textY", "moveTo", "lineTo", "handleSaveCanvas", "areaCanvas", "then", "createCompleteCanvas", "originalCanvas", "projectInfo", "validDate", "setDate", "validDateStr", "completeCanvas", "originalCanvasPaddingY", "originalCanvasWidth", "canvasWidth", "tableHeight", "padding", "quoteHeaders", "quoteData", "productHeaders", "productData", "table1Y", "table1Height", "table2Y", "downloadCanvas", "setTimeout", "link", "dataUrl", "toDataURL", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "area", "price", "profit", "checkClickElement", "target", "getBoundingClientRect", "handleMouseMove", "renderPageContent", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "style", "className", "onClick", "disabled", "onMouseEnter", "rect", "currentTarget", "tooltipX", "right", "tooltipY", "tooltipHeight", "windowHeight", "innerHeight", "onMouseLeave", "alt", "ref", "onMouseMove", "formatClipPath", "backgroundSize", "parseInt", "backgroundRepeat", "backgroundPosition", "backgroundColor", "backgroundImage", "zIndex", "bg", "marginLeft", "onWheel", "image", "isOpen", "onClose", "onSelect", "onFinish", "cursor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './App.css';\nimport logo from './images/logo.png';\nimport Util from './common/Util.js';\nimport { woodImages, empty, tang, song, yuan, ming, qing, changgui } from './components/Source.js';\nimport Drawer from './components/Drawer';\nimport SplashScreen from './components/SplashScreen';\nimport WoodTooltip from './components/WoodTooltip';\nimport PrivacyPolicy from './components/PrivacyPolicy';\nimport TermsOfUse from './components/TermsOfUse';\nimport WoodSpeciesIntro from './components/WoodSpeciesIntro';\n\n// 获取 electron API\n// @ts-ignore\nconst { ipcRenderer } = window.electron || {};\n\nfunction App() {\n  // 添加splash screen状态\n  const [showSplash, setShowSplash] = useState(true);\n\n  // 添加页面路由状态\n  const [currentPage, setCurrentPage] = useState('main');\n\n  // 添加license信息状态\n  const [licenseInfo, setLicenseInfo] = useState({\n    isValid: false,\n    expireDate: '',\n    remainingDays: 0\n  });\n\n  const [canvasSize, setCanvasSize] = useState(0);\n  const canvasRef = useRef(null);\n  const listContentRef = useRef(null);\n  const [selectedAreas, setSeletedAreas] = useState([]);\n  const [hoverArea, setHoverArea] = useState(null);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [drawerItems, setDrawerItems] = useState([]);\n  const [selectedSource, setSelectedSource] = useState(empty);\n  const [history, setHistory] = useState([]);\n  const hoverTimerRef = useRef(null);\n  const lastPositionRef = useRef({ x: 0, y: 0 });\n  const [areaImages, setAreaImages] = useState(selectedSource.clipPath.map(() => null));\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveError, setSaveError] = useState(null);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n  const saveSuccessTimerRef = useRef(null);\n  const tooltipTimerRef = useRef(null); // 添加tooltip延迟显示的定时器引用\n\n  // 添加木种提示框状态\n  const [tooltipInfo, setTooltipInfo] = useState({\n    visible: false,\n    wood: null,\n    position: 'bottom',\n    x: 0,\n    y: 0\n  });\n\n  // 处理画布大小调整\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const resizeObserver = new ResizeObserver(entries => {\n      for (let entry of entries) {\n        const { width } = entry.contentRect;\n        setCanvasSize(width);\n      }\n    });\n\n    resizeObserver.observe(canvas);\n\n    return () => {\n      resizeObserver.disconnect();\n    };\n  }, []);\n\n  // 定义 handleUndo 函数\n  const handleUndo = () => {\n    if (history.length > 0) {\n      const newHistory = [...history]\n      setAreaImages(newHistory.pop());\n      setHistory(newHistory);\n    }\n  };\n\n  // 处理键盘快捷键\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      // 检查是否按下了 Command/Ctrl + Z\n      if ((e.metaKey || e.ctrlKey) && e.key === 'z') {\n        e.preventDefault(); // 阻止默认行为\n        handleUndo();\n      }\n    };\n\n    // 添加键盘事件监听器\n    window.addEventListener('keydown', handleKeyDown);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [history, handleUndo]);\n\n  // 获取 license 信息\n  useEffect(() => {\n    // 通过 IPC 获取 license 信息\n    ipcRenderer?.send('get-license-info');\n\n    // 监听 license 信息返回\n    ipcRenderer?.on('license-info', (data) => {\n      if (data) {\n        // 格式化日期为 yyyy-MM-dd\n        const expireDate = new Date(data.expireTime);\n        const year = expireDate.getFullYear();\n        const month = String(expireDate.getMonth() + 1).padStart(2, '0');\n        const day = String(expireDate.getDate()).padStart(2, '0');\n        const formattedDate = `${year}-${month}-${day}`;\n\n        setLicenseInfo({\n          isValid: data.isValid,\n          expireDate: formattedDate,\n          remainingDays: data.remainingDays\n        });\n      }\n    });\n\n    // 清理函数\n    return () => {\n      ipcRenderer?.removeAllListeners('license-info');\n    };\n  }, []);\n\n  // 清理定时器\n  useEffect(() => {\n    return () => {\n      // 清理悬停定时器\n      if (hoverTimerRef.current) {\n        clearTimeout(hoverTimerRef.current);\n      }\n\n      // 清理成功提示定时器\n      if (saveSuccessTimerRef.current) {\n        clearTimeout(saveSuccessTimerRef.current);\n      }\n\n      // 清理tooltip显示定时器\n      if (tooltipTimerRef.current) {\n        clearTimeout(tooltipTimerRef.current);\n      }\n    };\n  }, []);\n\n  // 直接显示主应用界面\n\n  const handleWoodSelect = (woodId) => {\n    if (selectedAreas.length === 0) {\n      return;\n    }\n    let newAreaImages = [...areaImages]\n    setHistory([...history, areaImages]);\n    const selectedWood = woodImages.find(t => t.id === woodId)\n    selectedAreas.forEach(t => {\n      newAreaImages[t] = selectedWood\n    })\n    setAreaImages(newAreaImages)\n    setSeletedAreas([])\n  };\n\n\n\n  const handleDynastyClick = (dynasty) => {\n    let items = [];\n    switch (dynasty) {\n      case '唐':\n        items = tang;\n        break;\n      case '宋':\n        items = song;\n        break;\n      case '元':\n        items = yuan;\n        break;\n      case '明':\n        items = ming;\n        break;\n      case '清':\n        items = qing;\n        break;\n    }\n    setDrawerItems(items);\n    setIsDrawerOpen(true);\n  };\n\n  const handleDrawerSelect = (item) => {\n    setSeletedAreas([]);\n    setAreaImages(item.clipPath.map(() => null));\n    setHistory([])\n    setSelectedSource(item);\n    setIsDrawerOpen(false);\n  };\n\n  const scrollToIndex = (index) => {\n    if (index === null) {\n      return;\n    }\n    if (listContentRef.current) {\n      const itemWidth = 70;\n      const gap = 10;\n      const scrollPosition = index * (itemWidth + gap);\n      listContentRef.current.scrollTo({\n        left: scrollPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n\n  const handleListContentWheel = (e) => {\n    if (listContentRef.current) {\n      e.preventDefault();\n      listContentRef.current.scrollLeft += e.deltaY;\n    }\n  };\n\n  const handleAreaImageClick = (index) => {\n    if (selectedAreas.includes(index)) {\n      setSeletedAreas(prev => prev.filter(t => t !== index));\n    } else {\n      setSeletedAreas([...selectedAreas, index]);\n    }\n  };\n\n  function checkElementPos(clientX, clientY, left, top, width, height) {\n    let hitElement = null\n    selectedSource.clipPath.forEach((path, index) => {\n      const canvas = document.createElement('canvas');\n      canvas.width = width;\n      canvas.height = height;\n      const ctx = canvas.getContext('2d');\n\n      ctx.beginPath();\n      if (path == '' || path.startsWith('polygon(')) {\n        const points = Util.clipPathPolygonToPoints(path, width, height)\n        Util.clipImagePolygon(ctx, points)\n      } else if (path.startsWith('path(')) {\n        const cmds = Util.extractPathCommands(path, width, height)\n        Util.clipImagePath(ctx, cmds)\n      }\n      ctx.clip();\n      if (ctx.isPointInPath(clientX - left, clientY - top)) {\n        hitElement = index\n      }\n    })\n    return hitElement\n  }\n\n  function clear() {\n    setSeletedAreas([])\n    setAreaImages(selectedSource.clipPath.map(() => null))\n    setHistory([])\n  }\n\n  async function drawCanvas(canvas, ctx, size) {\n    // 异步加载图片\n    function loadImage(src) {\n      return new Promise((resolve) => {\n        const img = new Image();\n        img.src = src;\n        img.onload = () => resolve(img);\n      });\n    }\n\n    async function drawImage(ctx, imgPath) {\n      if (!imgPath) {\n        ctx.fillStyle = 'white';\n        ctx.fillRect(0, 0, size, size);\n        return;\n      }\n      const img = await loadImage(imgPath);\n      const targetWidth = 1500;\n      const scale = targetWidth / img.width;\n      const targetHeight = Math.floor(img.height * scale);\n\n      for (let y = 0; y < canvas.height; y += targetHeight) {\n        for (let x = 0; x < canvas.width; x += targetWidth) {\n          ctx.drawImage(img, x, y, targetWidth, targetHeight);\n        }\n      }\n    }\n\n    for (let i = 0; i < selectedSource.clipPath.length; i++) {\n      const clipPath = selectedSource.clipPath[i]\n      ctx.save();\n      if (clipPath == '' || clipPath.startsWith('polygon(')) {\n        const points = Util.clipPathPolygonToPoints(clipPath, size, size)\n        Util.clipImagePolygon(ctx, points)\n      } else if (clipPath.startsWith('path(')) {\n        const cmds = Util.extractPathCommands(clipPath, size, size)\n        Util.clipImagePath(ctx, cmds)\n      }\n      await drawImage(ctx, areaImages[i]?.src)\n      ctx.strokeStyle = 'black';\n      ctx.lineWidth = 3;\n      ctx.stroke();\n      ctx.restore();\n    }\n    // if (selectedSource.bg) {\n    //     ctx.save();\n    //     const points = Util.clipPathPolygonToPoints('', size, size)\n    //     Util.clipImagePolygon(ctx, points)\n    //     const img = await loadImage(selectedSource.bg);\n    //     ctx.drawImage(img, 0, 0, size, size);\n    //     ctx.restore();\n    // }\n  }\n\n  // 生成雪花算法ID（机器码+时间戳+随机数，6位字符）\n  function generateSnowflakeId() {\n    // 字符集：大写字母、数字、小写字母\n    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n\n    // 获取机器码（通过IPC从主进程获取）\n    let machineCode = 0;\n    try {\n      // 在React环境中，通过IPC获取机器ID\n      // @ts-ignore\n      if (window.electron && window.electron.ipcRenderer) {\n        // 同步获取机器ID的hash值\n        // @ts-ignore\n        const machineId = window.electron.ipcRenderer.sendSync('get-machine-id');\n        if (machineId) {\n          // 将机器ID转换为数字hash\n          machineCode = machineId.split('').reduce((hash, char) => {\n            return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff;\n          }, 0);\n        }\n      }\n    } catch (error) {\n      console.warn('无法获取机器ID，使用备用方案');\n      // 备用方案：使用navigator.userAgent\n      machineCode = navigator.userAgent.split('').reduce((hash, char) => {\n        return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff;\n      }, 0);\n    }\n\n    // 获取时间戳\n    const timestamp = Date.now();\n\n    // 生成随机数\n    const random = Math.floor(Math.random() * 1000000);\n\n    // 组合所有因子\n    const combined = Math.abs(machineCode) + timestamp + random;\n\n    // 转换为6位字符\n    let result = '';\n    let num = combined;\n\n    for (let i = 0; i < 6; i++) {\n      result = charset[num % charset.length] + result;\n      num = Math.floor(num / charset.length);\n    }\n\n    // 如果结果不足6位，用随机字符补齐\n    while (result.length < 6) {\n      result = charset[Math.floor(Math.random() * charset.length)] + result;\n    }\n\n    return result.substring(0, 6); // 确保只返回6位\n  }\n\n  // 获取当前选择的系列名称\n  function getCurrentSeries() {\n    if (!selectedSource || !selectedSource.path || selectedSource.path === 'empty') {\n      return '';\n    }\n\n    // 从路径中提取系列名称，如 \"tang/1\" -> \"唐\"\n    const pathParts = selectedSource.path.split('/');\n    const seriesKey = pathParts[0];\n\n    const seriesMap = {\n      'tang': '唐',\n      'song': '宋',\n      'yuan': '元',\n      'ming': '明',\n      'qing': '清'\n    };\n\n    return {\n      name: seriesMap[seriesKey] || seriesKey,\n      shortcut: seriesKey.charAt(0).toUpperCase()\n    };\n  }\n\n  // 获取选用的木种信息\n  function getSelectedWoodNames() {\n    const selectedWoods = areaImages.filter(wood => wood !== null);\n    if (selectedWoods.length === 0) {\n      return '未选择';\n    }\n\n    // 去重并获取木种名称\n    const uniqueWoodNames = [...new Set(selectedWoods.map(wood => wood.name))];\n    return uniqueWoodNames.join('、');\n  }\n\n  // 格式化当前日期\n  function formatCurrentDate() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = now.getMonth() + 1; // 月份从0开始，需要+1\n    const day = now.getDate();\n    return `${year}.${month}.${day}`;\n  }\n\n  function formatCurrentDateToYYYYMMDD() {\n    const today = new Date();\n    const year = today.getFullYear();\n    // getMonth() 返回0-11，需+1得到实际月份，并补零到2位\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    // getDate() 返回1-31，直接补零到2位\n    const day = String(today.getDate()).padStart(2, '0');\n    return `${year}${month}${day}`;\n}\n\n\n  function buildProjectInfo() {\n    const currentSeries = getCurrentSeries();\n    const currentDate = formatCurrentDate();\n    const currentYYYYMMDD = formatCurrentDateToYYYYMMDD();\n    const snowflakeId = generateSnowflakeId();\n    const seriesPrefix = currentSeries ? currentSeries.shortcut : 'X'; // 取系列名称首字母，如果没有系列则用X\n\n    return {\n      id: `BOER-${currentYYYYMMDD}-${snowflakeId}-GDPH`,\n      code: `${seriesPrefix}-${currentYYYYMMDD}-${snowflakeId}`, // 系列-日期-雪花算法id\n      date: currentDate, // 保存时的日期\n      woodCombined: getSelectedWoodNames(), // 选用了哪些木种\n      unitPrice: calculateTotalPrice().toFixed(0),\n      series: currentSeries.name // 选择时的目录名\n    }\n  }\n\n\n  // 文本换行辅助函数\n  const wrapText = (ctx, text, maxWidth) => {\n    const words = text.split('');\n    const lines = [];\n    let currentLine = '';\n\n    for (let i = 0; i < words.length; i++) {\n      const testLine = currentLine + words[i];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n\n      if (testWidth > maxWidth && currentLine !== '') {\n        lines.push(currentLine);\n        currentLine = words[i];\n      } else {\n        currentLine = testLine;\n      }\n    }\n    lines.push(currentLine);\n    return lines;\n  };\n\n  // 绘制多行文本\n  const drawMultilineText = (ctx, text, x, y, maxWidth, lineHeight, align = 'center') => {\n    const lines = wrapText(ctx, text, maxWidth);\n    const totalHeight = lines.length * lineHeight;\n    const startY = y - (totalHeight / 2) + (lineHeight / 2);\n\n    lines.forEach((line, index) => {\n      const lineY = startY + (index * lineHeight);\n      ctx.textAlign = align;\n      ctx.fillText(line, x, lineY);\n    });\n\n    return totalHeight;\n  };\n\n  // 绘制表格到画布\n  const drawTableToCanvas = (ctx, x, y, width, title, headers, data) => {\n    const baseRowHeight = 40;\n    const headerHeight = 50;\n    const titleHeight = 40;\n    const lineHeight = 18;\n    const cellPadding = 10;\n\n    // 设置字体\n    ctx.font = '16px Arial, sans-serif';\n    ctx.fillStyle = '#333';\n\n    // 绘制标题\n    ctx.fillStyle = '#000';\n    ctx.font = 'bold 18px Arial, sans-serif';\n    ctx.textAlign = 'left';\n    ctx.fillText(title, x, y + titleHeight - 10);\n\n    // 计算列宽\n    const colWidth = width / headers.length;\n\n    // 计算数据行需要的高度\n    ctx.font = '14px Arial, sans-serif';\n    let maxLinesInRow = 1;\n    data.forEach((cellData) => {\n      const maxCellWidth = colWidth - cellPadding * 2;\n      const lines = wrapText(ctx, String(cellData), maxCellWidth);\n      maxLinesInRow = Math.max(maxLinesInRow, lines.length);\n    });\n\n    const actualRowHeight = Math.max(baseRowHeight, maxLinesInRow * lineHeight + cellPadding * 2);\n\n    // 绘制表头背景\n    ctx.fillStyle = '#f5f5f5';\n    ctx.fillRect(x, y + titleHeight, width, headerHeight);\n\n    // 绘制表头边框\n    ctx.strokeStyle = '#ddd';\n    ctx.lineWidth = 1;\n    ctx.strokeRect(x, y + titleHeight, width, headerHeight);\n\n    // 绘制表头文字\n    ctx.fillStyle = '#333';\n    ctx.font = 'bold 14px Arial, sans-serif';\n    headers.forEach((header, index) => {\n      const cellX = x + index * colWidth;\n      const textX = cellX + colWidth / 2;\n      const textY = y + titleHeight + headerHeight / 2 + 5;\n\n      ctx.textAlign = 'center';\n      ctx.fillText(header, textX, textY);\n\n      // 绘制列分隔线\n      if (index > 0) {\n        ctx.beginPath();\n        ctx.moveTo(cellX, y + titleHeight);\n        ctx.lineTo(cellX, y + titleHeight + headerHeight);\n        ctx.stroke();\n      }\n    });\n\n    // 绘制数据行背景\n    ctx.fillStyle = '#fff';\n    ctx.fillRect(x, y + titleHeight + headerHeight, width, actualRowHeight);\n\n    // 绘制数据行边框\n    ctx.strokeRect(x, y + titleHeight + headerHeight, width, actualRowHeight);\n\n    // 绘制数据\n    ctx.fillStyle = '#333';\n    ctx.font = '14px Arial, sans-serif';\n    data.forEach((cellData, index) => {\n      const cellX = x + index * colWidth;\n      const textX = cellX + colWidth / 2;\n      const textY = y + titleHeight + headerHeight + actualRowHeight / 2;\n      const maxCellWidth = colWidth - cellPadding * 2;\n\n      drawMultilineText(ctx, String(cellData), textX, textY, maxCellWidth, lineHeight, 'center');\n\n      // 绘制列分隔线\n      if (index > 0) {\n        ctx.beginPath();\n        ctx.moveTo(cellX, y + titleHeight + headerHeight);\n        ctx.lineTo(cellX, y + titleHeight + headerHeight + actualRowHeight);\n        ctx.stroke();\n      }\n    });\n\n    return titleHeight + headerHeight + actualRowHeight + 20; // 返回表格总高度\n  };\n\n  // 保存画布为图片并下载\n  const handleSaveCanvas = () => {\n    // 设置保存状态和清除错误/成功提示\n    setIsSaving(true);\n    setSaveError(null);\n    setSaveSuccess(false);\n\n    // 清除之前的成功提示定时器\n    if (saveSuccessTimerRef.current) {\n      clearTimeout(saveSuccessTimerRef.current);\n      saveSuccessTimerRef.current = null;\n    }\n\n    const areaCanvas = document.createElement('canvas');\n    const size = 500;\n    areaCanvas.width = size;\n    areaCanvas.height = size;\n\n    drawCanvas(areaCanvas, areaCanvas.getContext('2d'), size).then(() => {\n      // 创建包含图像和表格的完整画布\n      createCompleteCanvas(areaCanvas);\n    });\n  };\n\n  // 创建包含图像和表格的完整画布\n  const createCompleteCanvas = (originalCanvas) => {\n    const projectInfo = buildProjectInfo();\n\n    // 计算有效期（30天后）\n    const validDate = new Date();\n    validDate.setDate(validDate.getDate() + 30);\n    const validDateStr = `${validDate.getFullYear()}.${validDate.getMonth() + 1}.${validDate.getDate()}`;\n\n    // 创建新的画布，高度增加以容纳表格\n    const completeCanvas = document.createElement('canvas');\n    const originalCanvasPaddingY = 100;\n    const originalCanvasWidth = originalCanvas.width;\n    const canvasWidth = originalCanvasWidth * 2;\n    const tableHeight = 400; // 两个表格的总高度\n    const padding = 40;\n\n    completeCanvas.width = canvasWidth;\n    completeCanvas.height = originalCanvasWidth + originalCanvasPaddingY + tableHeight + padding;\n\n    const ctx = completeCanvas.getContext('2d');\n\n    // 设置白色背景\n    ctx.fillStyle = '#fff';\n    ctx.fillRect(0, 0, completeCanvas.width, completeCanvas.height);\n\n    // 绘制原始图像\n    ctx.drawImage(originalCanvas, (canvasWidth - originalCanvas.width) / 2, originalCanvasPaddingY);\n\n    // 准备表格数据\n    const quoteHeaders = ['报价单号', '报价日期', '单价（元/㎡）', '报价有效期'];\n    const quoteData = [\n      projectInfo.id,\n      projectInfo.date,\n      `${projectInfo.unitPrice}`,\n      validDateStr\n    ];\n\n    const productHeaders = ['系列分类', '图纸编码', '树种组合', '产品规格'];\n    const productData = [\n      projectInfo.series || '未选择',\n      projectInfo.code,\n      projectInfo.woodCombined,\n      '60×60cm'\n    ];\n\n    // 绘制第一个表格（报价概要）\n    const table1Y = originalCanvasWidth + originalCanvasPaddingY + 20;\n    const table1Height = drawTableToCanvas(\n      ctx,\n      50,\n      table1Y,\n      canvasWidth - 100,\n      '报价概要',\n      quoteHeaders,\n      quoteData\n    );\n\n    // 绘制第二个表格（产品清单）\n    const table2Y = table1Y + table1Height + 10;\n    drawTableToCanvas(\n      ctx,\n      50,\n      table2Y,\n      canvasWidth - 100,\n      '产品清单',\n      productHeaders,\n      productData\n    );\n    // 下载完整画布\n    downloadCanvas(completeCanvas);\n  };\n\n  // 下载 canvas 为图片\n  const downloadCanvas = (canvas) => {\n    try {\n      // 重置保存状态并显示成功提示\n      setIsSaving(false);\n      setSaveSuccess(true);\n\n      // 3秒后自动隐藏成功提示\n      saveSuccessTimerRef.current = setTimeout(() => {\n        setSaveSuccess(false);\n      }, 3000);\n\n      // 创建一个链接元素\n      const link = document.createElement('a');\n\n      // 将 canvas 转换为数据 URL\n      const dataUrl = canvas.toDataURL('image/png');\n\n      // 设置链接属性\n      link.href = dataUrl;\n      const projectInfo = buildProjectInfo();\n      link.download = `柏尔地板设计报价单_${projectInfo.code}.png`;\n\n      // 模拟点击链接\n      document.body.appendChild(link);\n      link.click();\n\n      // 清理\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('Error downloading canvas:', error);\n      setIsSaving(false);\n      setSaveError('保存图片时出错，请重试');\n    }\n  };\n\n  function calculateTotalPrice() {\n    if (!selectedSource?.area || areaImages.filter(t => t === null).length > 0) {\n      return 0;\n    }\n    let result = 0;\n    for (let i = 0; i < areaImages.length; i++) {\n      if (areaImages[i] && i < selectedSource.area.length) {\n        result += areaImages[i].price * selectedSource.area[i];\n      }\n    }\n    result *= (1 / 0.36)\n    if (selectedSource.profit) {\n      result += selectedSource.profit;\n    }\n    return result * 3;\n  }\n\n  function checkClickElement(e) {\n    const { clientX, clientY } = e\n    const { left, top, width, height } = e.target.getBoundingClientRect()\n    let hitElement = checkElementPos(clientX, clientY, left, top, width, height)\n    if (hitElement !== null) {\n      if (selectedAreas.includes(hitElement)) {\n        setSeletedAreas(prev => prev.filter(t => t !== hitElement))\n      } else {\n        setSeletedAreas(prev => [...prev, hitElement])\n      }\n      // alert(selectedSource.clipPath[hitElement])\n      scrollToIndex(hitElement);\n    }\n  }\n\n  const handleMouseMove = (e) => {\n    const { clientX, clientY } = e;\n    const { left, top, width, height } = e.target.getBoundingClientRect();\n\n    if (clientX === lastPositionRef.current.x && clientY === lastPositionRef.current.y) {\n      return;\n    }\n\n    lastPositionRef.current = { x: clientX, y: clientY };\n\n    if (hoverTimerRef.current) {\n      clearTimeout(hoverTimerRef.current);\n    }\n\n    hoverTimerRef.current = setTimeout(() => {\n      const hitElement = checkElementPos(clientX, clientY, left, top, width, height);\n      setHoverArea(hitElement);\n      scrollToIndex(hitElement);\n    }, 40);\n  };\n\n  // 渲染页面内容\n  const renderPageContent = () => {\n    let display = 'block';\n    let item = '';\n    if (currentPage === 'privacy') {\n      display = 'none';\n      item = <PrivacyPolicy setCurrentPage={setCurrentPage} />;\n    } else if (currentPage === 'terms') {\n      display = 'none';\n      item = <TermsOfUse setCurrentPage={setCurrentPage} />;\n    } else if (currentPage === 'wood-species') {\n      display = 'none';\n      item = <WoodSpeciesIntro setCurrentPage={setCurrentPage} />;\n    }\n    return (\n      <div>\n        {item}\n        <div style={{ display: display }}>\n          {/* 顶部工具栏 */}\n          <div className=\"toolbar\">\n            <div className=\"tool-group\">\n              <span className=\"group-choice\">拼花花型选择：</span>\n              <button className=\"tool-button\" onClick={() => handleDynastyClick('唐')}>唐</button>\n              <button className=\"tool-button\" onClick={() => handleDynastyClick('宋')}>宋</button>\n              <button className=\"tool-button\" onClick={() => handleDynastyClick('元')}>元</button>\n              <button className=\"tool-button\" onClick={() => handleDynastyClick('明')}>明</button>\n              <button className=\"tool-button\" onClick={() => handleDynastyClick('清')}>清</button>\n            </div>\n            <div className=\"tool-group\">\n              <button className=\"tool-button\" onClick={() => clear()}>清空</button>\n              <button className=\"tool-button\" onClick={handleUndo} disabled={history.length === 0}>撤销</button>\n              <button className=\"tool-button\" onClick={handleSaveCanvas} disabled={isSaving || selectedSource.clipPath.length === 0 || areaImages.filter(t => t === null).length > 0}>\n                {isSaving ? '正在保存...' : '保存'}\n              </button>\n              {saveError && <div className=\"save-error\">{saveError}</div>}\n              {saveSuccess && <div className=\"save-success\">图片已保存成功</div>}\n            </div>\n          </div>\n\n          {/* 主内容区域 */}\n          <div className=\"main-content\">\n            {/* 左侧属性面板 */}\n            <div className=\"property-panel\">\n              <div><h3>选择木种</h3></div>\n              <div className=\"wood-selection\">\n                {woodImages.map((wood) => (\n                  <div\n                    key={wood.id}\n                    onClick={(e) => handleWoodSelect(wood.id, e)}\n                    className='wood-item'\n                    onMouseEnter={(e) => {\n                      // 清除之前的定时器（如果存在）\n                      if (tooltipTimerRef.current) {\n                        clearTimeout(tooltipTimerRef.current);\n                      }\n\n                      // 立即获取元素位置\n                      const rect = e.currentTarget.getBoundingClientRect();\n                      const tooltipX = rect.right + 10;\n\n                      // 判断tooltip的垂直位置，避免被屏幕底部遮挡\n                      let tooltipY;\n                      const tooltipHeight = 510; // 估计的tooltip高度（增加70%后）\n                      const windowHeight = window.innerHeight;\n\n                      // 如果元素位置太靠下，将tooltip显示在元素上方\n                      if (rect.top + tooltipHeight > windowHeight) {\n                        tooltipY = Math.max(10, windowHeight - tooltipHeight - 20); // 确保至少有10px的上边距\n                      } else {\n                        tooltipY = rect.top - 60; // 默认位置\n                      }\n\n                      // 设置1秒延迟后显示tooltip\n                      tooltipTimerRef.current = setTimeout(() => {\n                        setTooltipInfo({\n                          visible: true,\n                          wood: wood,\n                          position: 'bottom',\n                          x: tooltipX,\n                          y: tooltipY\n                        });\n                      }, 1000); // 1秒延迟\n                    }}\n                    onMouseLeave={() => {\n                      // 清除显示定时器\n                      if (tooltipTimerRef.current) {\n                        clearTimeout(tooltipTimerRef.current);\n                        tooltipTimerRef.current = null;\n                      }\n\n                      // 设置延迟隐藏tooltip（给用户时间移动到tooltip上）\n                      tooltipTimerRef.current = setTimeout(() => {\n                        setTooltipInfo(prev => ({\n                          ...prev,\n                          visible: false\n                        }));\n                      }, 300); // 300ms延迟\n                    }}\n                  >\n                    <img src={wood.src} alt={wood.name} />\n                    <span>{wood.name}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* 画布区域 */}\n            <div className=\"canvas-container\">\n              <div\n                ref={canvasRef}\n                className=\"canvas\"\n                onClick={e => checkClickElement(e)}\n                onMouseMove={handleMouseMove}\n                onMouseLeave={() => {\n                  if (hoverTimerRef.current) {\n                    clearTimeout(hoverTimerRef.current);\n                  }\n                  setHoverArea(null);\n                }}\n              >\n                {/* 预定义的裁剪区域 */}\n                {selectedSource.clipPath.map((path, index) => {\n                  return (\n                    <div\n                      key={index}\n                      className=\"clip-area\"\n                      style={{\n                        clipPath: Util.formatClipPath(canvasSize, path),\n                        position: 'absolute',\n                        width: '100%',\n                        height: '100%',\n                        backgroundSize: `${parseInt(canvasSize * 1.5)}px auto`,\n                        backgroundRepeat: 'repeat',\n                        backgroundPosition: '0 0',\n                        backgroundColor: 'white',\n                        backgroundImage: areaImages[index] && `url(${areaImages[index].src})`\n                      }}\n                    >\n                      {selectedAreas.includes(index) && <div className=\"overlay\"></div>}\n                      {hoverArea === index && <div className=\"hover-area\"></div>}\n                    </div>\n                  )\n                })}\n                {\n                  <div\n                    style={{\n                      width: '100%',\n                      height: '100%',\n                      zIndex: 999\n                    }}\n                  >\n                    {selectedSource.bg && <img src={selectedSource.bg} alt=\"bg\" style={{ width: '100%', height: '100%' }} />}\n                  </div>\n                }\n              </div>\n\n              {/* 添加横向滚动列表 */}\n              {\n                selectedSource.clipPath.length > 0 &&\n                <div className=\"area-images-list\" style={{ width: canvasSize + 20, marginLeft: '14%' }}>\n                  <div className=\"list-content\" ref={listContentRef} onWheel={handleListContentWheel}>\n                    {areaImages.map((image, index) => (\n                      <div\n                        key={index + 1}\n                        className=\"area-image-item\"\n                        onClick={() => handleAreaImageClick(index)}\n                        onMouseEnter={() => setHoverArea(index)}\n                        onMouseLeave={() => setHoverArea(null)}\n                      >\n                        {image && <img src={image.src} style={{ width: '100%', height: '100%' }}></img>}\n                        <div className='list-area-span'>{index + 1}</div>\n                        <div className='list-image-span'>{image ? image.name : '未选择'}</div>\n                        {selectedAreas.includes(index) && <div className=\"overlay\"></div>}\n                        {hoverArea === index && <div className=\"hover-area\"></div>}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              }\n\n              {\n                selectedSource.area && (\n                  /* 计算价格 */\n                  <div className=\"total-price-area\">\n                    <div className=\"total-price-content\">\n                      {calculateTotalPrice().toFixed(0) + ' 元/㎡'}\n                    </div>\n                  </div>)\n              }\n            </div>\n          </div>\n\n          {/* 抽屉组件 */}\n          <Drawer\n            isOpen={isDrawerOpen}\n            onClose={() => setIsDrawerOpen(false)}\n            items={drawerItems}\n            onSelect={handleDrawerSelect}\n          />\n\n          {/* 木种提示框 */}\n          {tooltipInfo.visible && (\n            <div\n              style={{\n                position: 'fixed',\n                top: tooltipInfo.y + 'px',\n                left: tooltipInfo.x + 'px',\n                zIndex: 2000\n              }}\n              onMouseEnter={() => {\n                // 鼠标进入tooltip时，清除隐藏定时器\n                if (tooltipTimerRef.current) {\n                  clearTimeout(tooltipTimerRef.current);\n                  tooltipTimerRef.current = null;\n                }\n              }}\n              onMouseLeave={() => {\n                // 鼠标离开tooltip时，隐藏tooltip\n                setTooltipInfo(prev => ({\n                  ...prev,\n                  visible: false\n                }));\n              }}\n            >\n              <WoodTooltip\n                wood={tooltipInfo.wood}\n                position={tooltipInfo.position}\n                visible={tooltipInfo.visible}\n              />\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"app-container\">\n      {showSplash && <SplashScreen onFinish={() => setShowSplash(false)} />}\n      <div style={{ display: showSplash ? 'none' : 'block' }}>\n        {/* 顶部导航栏 */}\n        <header className=\"header\">\n          <div className=\"header-content\">\n            <div className=\"logo-section\">\n              <img\n                src={logo}\n                alt=\"柏尔地板\"\n                className=\"logo\"\n                style={{ cursor: 'pointer' }}\n                onClick={() => setCurrentPage('main')}\n              />\n              <span className=\"slogan\">中国高端实木定制地板全国销量第一</span>\n            </div>\n            <div className=\"user-section\">\n              {/* 登录相关按钮已移除 */}\n            </div>\n          </div>\n        </header>\n\n        {/* 根据当前页面状态渲染不同内容 */}\n        {renderPageContent()}\n\n        {/* 底部公司信息 */}\n        <footer className=\"footer\">\n          <div className=\"footer-content\">\n            <div className=\"footer-info\">\n              <p>© 2025 柏尔木业有限公司. 保留所有权利。</p>\n            </div>\n            <div className=\"footer-links\">\n              {licenseInfo.expireDate && (\n                <span className=\"license-info\">授权有效期至: {licenseInfo.expireDate}</span>\n              )}\n              <span className=\"separator\">|</span>\n              <a href=\"#\" onClick={(e) => { e.preventDefault(); setCurrentPage('wood-species'); }}>木种介绍</a>\n              <span className=\"separator\">|</span>\n              <a href=\"#\" onClick={(e) => { e.preventDefault(); setCurrentPage('privacy'); }}>隐私政策</a>\n              <span className=\"separator\">|</span>\n              <a href=\"#\" onClick={(e) => { e.preventDefault(); setCurrentPage('terms'); }}>使用条款</a>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </div>\n  );\n}\n\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,SAASC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,wBAAwB;AAClG,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,gBAAgB,MAAM,+BAA+B;;AAE5D;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAM;EAAEC;AAAY,CAAC,GAAGC,MAAM,CAACC,QAAQ,IAAI,CAAC,CAAC;AAE7C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,MAAM,CAAC;;EAEtD;EACA,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC;IAC7CgC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMqC,SAAS,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoC,cAAc,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACqC,aAAa,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACrD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAACM,KAAK,CAAC;EAC3D,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMmD,aAAa,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkD,eAAe,GAAGlD,MAAM,CAAC;IAAEmD,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC9C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC+C,cAAc,CAACU,QAAQ,CAACC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;EACrF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMiE,mBAAmB,GAAG/D,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMgE,eAAe,GAAGhE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtC;EACA,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC;IAC7CqE,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,QAAQ;IAClBlB,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC,CAAC;;EAEF;EACArD,SAAS,CAAC,MAAM;IACd,MAAMuE,MAAM,GAAGnC,SAAS,CAACoC,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,cAAc,GAAG,IAAIC,cAAc,CAACC,OAAO,IAAI;MACnD,KAAK,IAAIC,KAAK,IAAID,OAAO,EAAE;QACzB,MAAM;UAAEE;QAAM,CAAC,GAAGD,KAAK,CAACE,WAAW;QACnC3C,aAAa,CAAC0C,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;IAEFJ,cAAc,CAACM,OAAO,CAACR,MAAM,CAAC;IAE9B,OAAO,MAAM;MACXE,cAAc,CAACO,UAAU,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIjC,OAAO,CAACkC,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAG,CAAC,GAAGnC,OAAO,CAAC;MAC/BO,aAAa,CAAC4B,UAAU,CAACC,GAAG,CAAC,CAAC,CAAC;MAC/BnC,UAAU,CAACkC,UAAU,CAAC;IACxB;EACF,CAAC;;EAED;EACAnF,SAAS,CAAC,MAAM;IACd,MAAMqF,aAAa,GAAIC,CAAC,IAAK;MAC3B;MACA,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC;QACpBT,UAAU,CAAC,CAAC;MACd;IACF,CAAC;;IAED;IACA5D,MAAM,CAACsE,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;;IAEjD;IACA,OAAO,MAAM;MACXhE,MAAM,CAACuE,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACrC,OAAO,EAAEiC,UAAU,CAAC,CAAC;;EAEzB;EACAjF,SAAS,CAAC,MAAM;IACd;IACAoB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyE,IAAI,CAAC,kBAAkB,CAAC;;IAErC;IACAzE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0E,EAAE,CAAC,cAAc,EAAGC,IAAI,IAAK;MACxC,IAAIA,IAAI,EAAE;QACR;QACA,MAAM/D,UAAU,GAAG,IAAIgE,IAAI,CAACD,IAAI,CAACE,UAAU,CAAC;QAC5C,MAAMC,IAAI,GAAGlE,UAAU,CAACmE,WAAW,CAAC,CAAC;QACrC,MAAMC,KAAK,GAAGC,MAAM,CAACrE,UAAU,CAACsE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAChE,MAAMC,GAAG,GAAGH,MAAM,CAACrE,UAAU,CAACyE,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACzD,MAAMG,aAAa,GAAG,GAAGR,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;QAE/C1E,cAAc,CAAC;UACbC,OAAO,EAAEgE,IAAI,CAAChE,OAAO;UACrBC,UAAU,EAAE0E,aAAa;UACzBzE,aAAa,EAAE8D,IAAI,CAAC9D;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXb,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuF,kBAAkB,CAAC,cAAc,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3G,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIkD,aAAa,CAACsB,OAAO,EAAE;QACzBoC,YAAY,CAAC1D,aAAa,CAACsB,OAAO,CAAC;MACrC;;MAEA;MACA,IAAIR,mBAAmB,CAACQ,OAAO,EAAE;QAC/BoC,YAAY,CAAC5C,mBAAmB,CAACQ,OAAO,CAAC;MAC3C;;MAEA;MACA,IAAIP,eAAe,CAACO,OAAO,EAAE;QAC3BoC,YAAY,CAAC3C,eAAe,CAACO,OAAO,CAAC;MACvC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA,MAAMqC,gBAAgB,GAAIC,MAAM,IAAK;IACnC,IAAIxE,aAAa,CAAC4C,MAAM,KAAK,CAAC,EAAE;MAC9B;IACF;IACA,IAAI6B,aAAa,GAAG,CAAC,GAAGzD,UAAU,CAAC;IACnCL,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAEM,UAAU,CAAC,CAAC;IACpC,MAAM0D,YAAY,GAAG5G,UAAU,CAAC6G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,MAAM,CAAC;IAC1DxE,aAAa,CAAC8E,OAAO,CAACF,CAAC,IAAI;MACzBH,aAAa,CAACG,CAAC,CAAC,GAAGF,YAAY;IACjC,CAAC,CAAC;IACFzD,aAAa,CAACwD,aAAa,CAAC;IAC5BxE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAID,MAAM8E,kBAAkB,GAAIC,OAAO,IAAK;IACtC,IAAIC,KAAK,GAAG,EAAE;IACd,QAAQD,OAAO;MACb,KAAK,GAAG;QACNC,KAAK,GAAGjH,IAAI;QACZ;MACF,KAAK,GAAG;QACNiH,KAAK,GAAGhH,IAAI;QACZ;MACF,KAAK,GAAG;QACNgH,KAAK,GAAG/G,IAAI;QACZ;MACF,KAAK,GAAG;QACN+G,KAAK,GAAG9G,IAAI;QACZ;MACF,KAAK,GAAG;QACN8G,KAAK,GAAG7G,IAAI;QACZ;IACJ;IACAmC,cAAc,CAAC0E,KAAK,CAAC;IACrB5E,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM6E,kBAAkB,GAAIC,IAAI,IAAK;IACnClF,eAAe,CAAC,EAAE,CAAC;IACnBgB,aAAa,CAACkE,IAAI,CAACjE,QAAQ,CAACC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IAC5CR,UAAU,CAAC,EAAE,CAAC;IACdF,iBAAiB,CAAC0E,IAAI,CAAC;IACvB9E,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM+E,aAAa,GAAIC,KAAK,IAAK;IAC/B,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClB;IACF;IACA,IAAItF,cAAc,CAACmC,OAAO,EAAE;MAC1B,MAAMoD,SAAS,GAAG,EAAE;MACpB,MAAMC,GAAG,GAAG,EAAE;MACd,MAAMC,cAAc,GAAGH,KAAK,IAAIC,SAAS,GAAGC,GAAG,CAAC;MAChDxF,cAAc,CAACmC,OAAO,CAACuD,QAAQ,CAAC;QAC9BC,IAAI,EAAEF,cAAc;QACpBG,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAGD,MAAMC,sBAAsB,GAAI5C,CAAC,IAAK;IACpC,IAAIjD,cAAc,CAACmC,OAAO,EAAE;MAC1Bc,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBrD,cAAc,CAACmC,OAAO,CAAC2D,UAAU,IAAI7C,CAAC,CAAC8C,MAAM;IAC/C;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAIV,KAAK,IAAK;IACtC,IAAIrF,aAAa,CAACgG,QAAQ,CAACX,KAAK,CAAC,EAAE;MACjCpF,eAAe,CAACgG,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACtB,CAAC,IAAIA,CAAC,KAAKS,KAAK,CAAC,CAAC;IACxD,CAAC,MAAM;MACLpF,eAAe,CAAC,CAAC,GAAGD,aAAa,EAAEqF,KAAK,CAAC,CAAC;IAC5C;EACF,CAAC;EAED,SAASc,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAEX,IAAI,EAAEY,GAAG,EAAE/D,KAAK,EAAEgE,MAAM,EAAE;IACnE,IAAIC,UAAU,GAAG,IAAI;IACrBhG,cAAc,CAACU,QAAQ,CAAC4D,OAAO,CAAC,CAAC2B,IAAI,EAAEpB,KAAK,KAAK;MAC/C,MAAMpD,MAAM,GAAGyE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C1E,MAAM,CAACM,KAAK,GAAGA,KAAK;MACpBN,MAAM,CAACsE,MAAM,GAAGA,MAAM;MACtB,MAAMK,GAAG,GAAG3E,MAAM,CAAC4E,UAAU,CAAC,IAAI,CAAC;MAEnCD,GAAG,CAACE,SAAS,CAAC,CAAC;MACf,IAAIL,IAAI,IAAI,EAAE,IAAIA,IAAI,CAACM,UAAU,CAAC,UAAU,CAAC,EAAE;QAC7C,MAAMC,MAAM,GAAGnJ,IAAI,CAACoJ,uBAAuB,CAACR,IAAI,EAAElE,KAAK,EAAEgE,MAAM,CAAC;QAChE1I,IAAI,CAACqJ,gBAAgB,CAACN,GAAG,EAAEI,MAAM,CAAC;MACpC,CAAC,MAAM,IAAIP,IAAI,CAACM,UAAU,CAAC,OAAO,CAAC,EAAE;QACnC,MAAMI,IAAI,GAAGtJ,IAAI,CAACuJ,mBAAmB,CAACX,IAAI,EAAElE,KAAK,EAAEgE,MAAM,CAAC;QAC1D1I,IAAI,CAACwJ,aAAa,CAACT,GAAG,EAAEO,IAAI,CAAC;MAC/B;MACAP,GAAG,CAACU,IAAI,CAAC,CAAC;MACV,IAAIV,GAAG,CAACW,aAAa,CAACnB,OAAO,GAAGV,IAAI,EAAEW,OAAO,GAAGC,GAAG,CAAC,EAAE;QACpDE,UAAU,GAAGnB,KAAK;MACpB;IACF,CAAC,CAAC;IACF,OAAOmB,UAAU;EACnB;EAEA,SAASgB,KAAKA,CAAA,EAAG;IACfvH,eAAe,CAAC,EAAE,CAAC;IACnBgB,aAAa,CAACT,cAAc,CAACU,QAAQ,CAACC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IACtDR,UAAU,CAAC,EAAE,CAAC;EAChB;EAEA,eAAe8G,UAAUA,CAACxF,MAAM,EAAE2E,GAAG,EAAEc,IAAI,EAAE;IAC3C;IACA,SAASC,SAASA,CAACC,GAAG,EAAE;MACtB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC9B,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACH,GAAG,GAAGA,GAAG;QACbG,GAAG,CAACE,MAAM,GAAG,MAAMH,OAAO,CAACC,GAAG,CAAC;MACjC,CAAC,CAAC;IACJ;IAEA,eAAeG,SAASA,CAACtB,GAAG,EAAEuB,OAAO,EAAE;MACrC,IAAI,CAACA,OAAO,EAAE;QACZvB,GAAG,CAACwB,SAAS,GAAG,OAAO;QACvBxB,GAAG,CAACyB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEX,IAAI,EAAEA,IAAI,CAAC;QAC9B;MACF;MACA,MAAMK,GAAG,GAAG,MAAMJ,SAAS,CAACQ,OAAO,CAAC;MACpC,MAAMG,WAAW,GAAG,IAAI;MACxB,MAAMC,KAAK,GAAGD,WAAW,GAAGP,GAAG,CAACxF,KAAK;MACrC,MAAMiG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACX,GAAG,CAACxB,MAAM,GAAGgC,KAAK,CAAC;MAEnD,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,MAAM,CAACsE,MAAM,EAAExF,CAAC,IAAIyH,YAAY,EAAE;QACpD,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,MAAM,CAACM,KAAK,EAAEzB,CAAC,IAAIwH,WAAW,EAAE;UAClD1B,GAAG,CAACsB,SAAS,CAACH,GAAG,EAAEjH,CAAC,EAAEC,CAAC,EAAEuH,WAAW,EAAEE,YAAY,CAAC;QACrD;MACF;IACF;IAEA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnI,cAAc,CAACU,QAAQ,CAAC0B,MAAM,EAAE+F,CAAC,EAAE,EAAE;MAAA,IAAAC,aAAA;MACvD,MAAM1H,QAAQ,GAAGV,cAAc,CAACU,QAAQ,CAACyH,CAAC,CAAC;MAC3C/B,GAAG,CAACiC,IAAI,CAAC,CAAC;MACV,IAAI3H,QAAQ,IAAI,EAAE,IAAIA,QAAQ,CAAC6F,UAAU,CAAC,UAAU,CAAC,EAAE;QACrD,MAAMC,MAAM,GAAGnJ,IAAI,CAACoJ,uBAAuB,CAAC/F,QAAQ,EAAEwG,IAAI,EAAEA,IAAI,CAAC;QACjE7J,IAAI,CAACqJ,gBAAgB,CAACN,GAAG,EAAEI,MAAM,CAAC;MACpC,CAAC,MAAM,IAAI9F,QAAQ,CAAC6F,UAAU,CAAC,OAAO,CAAC,EAAE;QACvC,MAAMI,IAAI,GAAGtJ,IAAI,CAACuJ,mBAAmB,CAAClG,QAAQ,EAAEwG,IAAI,EAAEA,IAAI,CAAC;QAC3D7J,IAAI,CAACwJ,aAAa,CAACT,GAAG,EAAEO,IAAI,CAAC;MAC/B;MACA,MAAMe,SAAS,CAACtB,GAAG,GAAAgC,aAAA,GAAE5H,UAAU,CAAC2H,CAAC,CAAC,cAAAC,aAAA,uBAAbA,aAAA,CAAehB,GAAG,CAAC;MACxChB,GAAG,CAACkC,WAAW,GAAG,OAAO;MACzBlC,GAAG,CAACmC,SAAS,GAAG,CAAC;MACjBnC,GAAG,CAACoC,MAAM,CAAC,CAAC;MACZpC,GAAG,CAACqC,OAAO,CAAC,CAAC;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEA;EACA,SAASC,mBAAmBA,CAAA,EAAG;IAC7B;IACA,MAAMC,OAAO,GAAG,sCAAsC;;IAEtD;IACA,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAI;MACF;MACA;MACA,IAAIrK,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACF,WAAW,EAAE;QAClD;QACA;QACA,MAAMuK,SAAS,GAAGtK,MAAM,CAACC,QAAQ,CAACF,WAAW,CAACwK,QAAQ,CAAC,gBAAgB,CAAC;QACxE,IAAID,SAAS,EAAE;UACb;UACAD,WAAW,GAAGC,SAAS,CAACE,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;YACvD,OAAQ,CAACD,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,GAAI,UAAU;UAC/D,CAAC,EAAE,CAAC,CAAC;QACP;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,iBAAiB,CAAC;MAC/B;MACAV,WAAW,GAAGW,SAAS,CAACC,SAAS,CAACT,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;QACjE,OAAQ,CAACD,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,GAAI,UAAU;MAC/D,CAAC,EAAE,CAAC,CAAC;IACP;;IAEA;IACA,MAAMM,SAAS,GAAGvG,IAAI,CAACwG,GAAG,CAAC,CAAC;;IAE5B;IACA,MAAMC,MAAM,GAAG1B,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC0B,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;;IAElD;IACA,MAAMC,QAAQ,GAAG3B,IAAI,CAAC4B,GAAG,CAACjB,WAAW,CAAC,GAAGa,SAAS,GAAGE,MAAM;;IAE3D;IACA,IAAIG,MAAM,GAAG,EAAE;IACf,IAAIC,GAAG,GAAGH,QAAQ;IAElB,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B2B,MAAM,GAAGnB,OAAO,CAACoB,GAAG,GAAGpB,OAAO,CAACvG,MAAM,CAAC,GAAG0H,MAAM;MAC/CC,GAAG,GAAG9B,IAAI,CAACC,KAAK,CAAC6B,GAAG,GAAGpB,OAAO,CAACvG,MAAM,CAAC;IACxC;;IAEA;IACA,OAAO0H,MAAM,CAAC1H,MAAM,GAAG,CAAC,EAAE;MACxB0H,MAAM,GAAGnB,OAAO,CAACV,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC0B,MAAM,CAAC,CAAC,GAAGhB,OAAO,CAACvG,MAAM,CAAC,CAAC,GAAG0H,MAAM;IACvE;IAEA,OAAOA,MAAM,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC;;EAEA;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAACjK,cAAc,IAAI,CAACA,cAAc,CAACiG,IAAI,IAAIjG,cAAc,CAACiG,IAAI,KAAK,OAAO,EAAE;MAC9E,OAAO,EAAE;IACX;;IAEA;IACA,MAAMiE,SAAS,GAAGlK,cAAc,CAACiG,IAAI,CAAC8C,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMoB,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC;IAE9B,MAAME,SAAS,GAAG;MAChB,MAAM,EAAE,GAAG;MACX,MAAM,EAAE,GAAG;MACX,MAAM,EAAE,GAAG;MACX,MAAM,EAAE,GAAG;MACX,MAAM,EAAE;IACV,CAAC;IAED,OAAO;MACLC,IAAI,EAAED,SAAS,CAACD,SAAS,CAAC,IAAIA,SAAS;MACvCG,QAAQ,EAAEH,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;IAC5C,CAAC;EACH;;EAEA;EACA,SAASC,oBAAoBA,CAAA,EAAG;IAC9B,MAAMC,aAAa,GAAGlK,UAAU,CAACkF,MAAM,CAACnE,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;IAC9D,IAAImJ,aAAa,CAACtI,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,KAAK;IACd;;IAEA;IACA,MAAMuI,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACF,aAAa,CAAC/J,GAAG,CAACY,IAAI,IAAIA,IAAI,CAAC8I,IAAI,CAAC,CAAC,CAAC;IAC1E,OAAOM,eAAe,CAACE,IAAI,CAAC,GAAG,CAAC;EAClC;;EAEA;EACA,SAASC,iBAAiBA,CAAA,EAAG;IAC3B,MAAMpB,GAAG,GAAG,IAAIxG,IAAI,CAAC,CAAC;IACtB,MAAME,IAAI,GAAGsG,GAAG,CAACrG,WAAW,CAAC,CAAC;IAC9B,MAAMC,KAAK,GAAGoG,GAAG,CAAClG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC,MAAME,GAAG,GAAGgG,GAAG,CAAC/F,OAAO,CAAC,CAAC;IACzB,OAAO,GAAGP,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEA,SAASqH,2BAA2BA,CAAA,EAAG;IACrC,MAAMC,KAAK,GAAG,IAAI9H,IAAI,CAAC,CAAC;IACxB,MAAME,IAAI,GAAG4H,KAAK,CAAC3H,WAAW,CAAC,CAAC;IAChC;IACA,MAAMC,KAAK,GAAGC,MAAM,CAACyH,KAAK,CAACxH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC3D;IACA,MAAMC,GAAG,GAAGH,MAAM,CAACyH,KAAK,CAACrH,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,OAAO,GAAGL,IAAI,GAAGE,KAAK,GAAGI,GAAG,EAAE;EAClC;EAGE,SAASuH,gBAAgBA,CAAA,EAAG;IAC1B,MAAMC,aAAa,GAAGjB,gBAAgB,CAAC,CAAC;IACxC,MAAMkB,WAAW,GAAGL,iBAAiB,CAAC,CAAC;IACvC,MAAMM,eAAe,GAAGL,2BAA2B,CAAC,CAAC;IACrD,MAAMM,WAAW,GAAG3C,mBAAmB,CAAC,CAAC;IACzC,MAAM4C,YAAY,GAAGJ,aAAa,GAAGA,aAAa,CAACZ,QAAQ,GAAG,GAAG,CAAC,CAAC;;IAEnE,OAAO;MACLjG,EAAE,EAAE,QAAQ+G,eAAe,IAAIC,WAAW,OAAO;MACjDE,IAAI,EAAE,GAAGD,YAAY,IAAIF,eAAe,IAAIC,WAAW,EAAE;MAAE;MAC3DG,IAAI,EAAEL,WAAW;MAAE;MACnBM,YAAY,EAAEhB,oBAAoB,CAAC,CAAC;MAAE;MACtCiB,SAAS,EAAEC,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;MAC3CC,MAAM,EAAEX,aAAa,CAACb,IAAI,CAAC;IAC7B,CAAC;EACH;;EAGA;EACA,MAAMyB,QAAQ,GAAGA,CAAC1F,GAAG,EAAE2F,IAAI,EAAEC,QAAQ,KAAK;IACxC,MAAMC,KAAK,GAAGF,IAAI,CAAChD,KAAK,CAAC,EAAE,CAAC;IAC5B,MAAMmD,KAAK,GAAG,EAAE;IAChB,IAAIC,WAAW,GAAG,EAAE;IAEpB,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,KAAK,CAAC7J,MAAM,EAAE+F,CAAC,EAAE,EAAE;MACrC,MAAMiE,QAAQ,GAAGD,WAAW,GAAGF,KAAK,CAAC9D,CAAC,CAAC;MACvC,MAAMkE,OAAO,GAAGjG,GAAG,CAACkG,WAAW,CAACF,QAAQ,CAAC;MACzC,MAAMG,SAAS,GAAGF,OAAO,CAACtK,KAAK;MAE/B,IAAIwK,SAAS,GAAGP,QAAQ,IAAIG,WAAW,KAAK,EAAE,EAAE;QAC9CD,KAAK,CAACM,IAAI,CAACL,WAAW,CAAC;QACvBA,WAAW,GAAGF,KAAK,CAAC9D,CAAC,CAAC;MACxB,CAAC,MAAM;QACLgE,WAAW,GAAGC,QAAQ;MACxB;IACF;IACAF,KAAK,CAACM,IAAI,CAACL,WAAW,CAAC;IACvB,OAAOD,KAAK;EACd,CAAC;;EAED;EACA,MAAMO,iBAAiB,GAAGA,CAACrG,GAAG,EAAE2F,IAAI,EAAEzL,CAAC,EAAEC,CAAC,EAAEyL,QAAQ,EAAEU,UAAU,EAAEC,KAAK,GAAG,QAAQ,KAAK;IACrF,MAAMT,KAAK,GAAGJ,QAAQ,CAAC1F,GAAG,EAAE2F,IAAI,EAAEC,QAAQ,CAAC;IAC3C,MAAMY,WAAW,GAAGV,KAAK,CAAC9J,MAAM,GAAGsK,UAAU;IAC7C,MAAMG,MAAM,GAAGtM,CAAC,GAAIqM,WAAW,GAAG,CAAE,GAAIF,UAAU,GAAG,CAAE;IAEvDR,KAAK,CAAC5H,OAAO,CAAC,CAACwI,IAAI,EAAEjI,KAAK,KAAK;MAC7B,MAAMkI,KAAK,GAAGF,MAAM,GAAIhI,KAAK,GAAG6H,UAAW;MAC3CtG,GAAG,CAAC4G,SAAS,GAAGL,KAAK;MACrBvG,GAAG,CAAC6G,QAAQ,CAACH,IAAI,EAAExM,CAAC,EAAEyM,KAAK,CAAC;IAC9B,CAAC,CAAC;IAEF,OAAOH,WAAW;EACpB,CAAC;;EAED;EACA,MAAMM,iBAAiB,GAAGA,CAAC9G,GAAG,EAAE9F,CAAC,EAAEC,CAAC,EAAEwB,KAAK,EAAEoL,KAAK,EAAEC,OAAO,EAAEnK,IAAI,KAAK;IACpE,MAAMoK,aAAa,GAAG,EAAE;IACxB,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMb,UAAU,GAAG,EAAE;IACrB,MAAMc,WAAW,GAAG,EAAE;;IAEtB;IACApH,GAAG,CAACqH,IAAI,GAAG,wBAAwB;IACnCrH,GAAG,CAACwB,SAAS,GAAG,MAAM;;IAEtB;IACAxB,GAAG,CAACwB,SAAS,GAAG,MAAM;IACtBxB,GAAG,CAACqH,IAAI,GAAG,6BAA6B;IACxCrH,GAAG,CAAC4G,SAAS,GAAG,MAAM;IACtB5G,GAAG,CAAC6G,QAAQ,CAACE,KAAK,EAAE7M,CAAC,EAAEC,CAAC,GAAGgN,WAAW,GAAG,EAAE,CAAC;;IAE5C;IACA,MAAMG,QAAQ,GAAG3L,KAAK,GAAGqL,OAAO,CAAChL,MAAM;;IAEvC;IACAgE,GAAG,CAACqH,IAAI,GAAG,wBAAwB;IACnC,IAAIE,aAAa,GAAG,CAAC;IACrB1K,IAAI,CAACqB,OAAO,CAAEsJ,QAAQ,IAAK;MACzB,MAAMC,YAAY,GAAGH,QAAQ,GAAGF,WAAW,GAAG,CAAC;MAC/C,MAAMtB,KAAK,GAAGJ,QAAQ,CAAC1F,GAAG,EAAE7C,MAAM,CAACqK,QAAQ,CAAC,EAAEC,YAAY,CAAC;MAC3DF,aAAa,GAAG1F,IAAI,CAAC6F,GAAG,CAACH,aAAa,EAAEzB,KAAK,CAAC9J,MAAM,CAAC;IACvD,CAAC,CAAC;IAEF,MAAM2L,eAAe,GAAG9F,IAAI,CAAC6F,GAAG,CAACT,aAAa,EAAEM,aAAa,GAAGjB,UAAU,GAAGc,WAAW,GAAG,CAAC,CAAC;;IAE7F;IACApH,GAAG,CAACwB,SAAS,GAAG,SAAS;IACzBxB,GAAG,CAACyB,QAAQ,CAACvH,CAAC,EAAEC,CAAC,GAAGgN,WAAW,EAAExL,KAAK,EAAEuL,YAAY,CAAC;;IAErD;IACAlH,GAAG,CAACkC,WAAW,GAAG,MAAM;IACxBlC,GAAG,CAACmC,SAAS,GAAG,CAAC;IACjBnC,GAAG,CAAC4H,UAAU,CAAC1N,CAAC,EAAEC,CAAC,GAAGgN,WAAW,EAAExL,KAAK,EAAEuL,YAAY,CAAC;;IAEvD;IACAlH,GAAG,CAACwB,SAAS,GAAG,MAAM;IACtBxB,GAAG,CAACqH,IAAI,GAAG,6BAA6B;IACxCL,OAAO,CAAC9I,OAAO,CAAC,CAAC2J,MAAM,EAAEpJ,KAAK,KAAK;MACjC,MAAMqJ,KAAK,GAAG5N,CAAC,GAAGuE,KAAK,GAAG6I,QAAQ;MAClC,MAAMS,KAAK,GAAGD,KAAK,GAAGR,QAAQ,GAAG,CAAC;MAClC,MAAMU,KAAK,GAAG7N,CAAC,GAAGgN,WAAW,GAAGD,YAAY,GAAG,CAAC,GAAG,CAAC;MAEpDlH,GAAG,CAAC4G,SAAS,GAAG,QAAQ;MACxB5G,GAAG,CAAC6G,QAAQ,CAACgB,MAAM,EAAEE,KAAK,EAAEC,KAAK,CAAC;;MAElC;MACA,IAAIvJ,KAAK,GAAG,CAAC,EAAE;QACbuB,GAAG,CAACE,SAAS,CAAC,CAAC;QACfF,GAAG,CAACiI,MAAM,CAACH,KAAK,EAAE3N,CAAC,GAAGgN,WAAW,CAAC;QAClCnH,GAAG,CAACkI,MAAM,CAACJ,KAAK,EAAE3N,CAAC,GAAGgN,WAAW,GAAGD,YAAY,CAAC;QACjDlH,GAAG,CAACoC,MAAM,CAAC,CAAC;MACd;IACF,CAAC,CAAC;;IAEF;IACApC,GAAG,CAACwB,SAAS,GAAG,MAAM;IACtBxB,GAAG,CAACyB,QAAQ,CAACvH,CAAC,EAAEC,CAAC,GAAGgN,WAAW,GAAGD,YAAY,EAAEvL,KAAK,EAAEgM,eAAe,CAAC;;IAEvE;IACA3H,GAAG,CAAC4H,UAAU,CAAC1N,CAAC,EAAEC,CAAC,GAAGgN,WAAW,GAAGD,YAAY,EAAEvL,KAAK,EAAEgM,eAAe,CAAC;;IAEzE;IACA3H,GAAG,CAACwB,SAAS,GAAG,MAAM;IACtBxB,GAAG,CAACqH,IAAI,GAAG,wBAAwB;IACnCxK,IAAI,CAACqB,OAAO,CAAC,CAACsJ,QAAQ,EAAE/I,KAAK,KAAK;MAChC,MAAMqJ,KAAK,GAAG5N,CAAC,GAAGuE,KAAK,GAAG6I,QAAQ;MAClC,MAAMS,KAAK,GAAGD,KAAK,GAAGR,QAAQ,GAAG,CAAC;MAClC,MAAMU,KAAK,GAAG7N,CAAC,GAAGgN,WAAW,GAAGD,YAAY,GAAGS,eAAe,GAAG,CAAC;MAClE,MAAMF,YAAY,GAAGH,QAAQ,GAAGF,WAAW,GAAG,CAAC;MAE/Cf,iBAAiB,CAACrG,GAAG,EAAE7C,MAAM,CAACqK,QAAQ,CAAC,EAAEO,KAAK,EAAEC,KAAK,EAAEP,YAAY,EAAEnB,UAAU,EAAE,QAAQ,CAAC;;MAE1F;MACA,IAAI7H,KAAK,GAAG,CAAC,EAAE;QACbuB,GAAG,CAACE,SAAS,CAAC,CAAC;QACfF,GAAG,CAACiI,MAAM,CAACH,KAAK,EAAE3N,CAAC,GAAGgN,WAAW,GAAGD,YAAY,CAAC;QACjDlH,GAAG,CAACkI,MAAM,CAACJ,KAAK,EAAE3N,CAAC,GAAGgN,WAAW,GAAGD,YAAY,GAAGS,eAAe,CAAC;QACnE3H,GAAG,CAACoC,MAAM,CAAC,CAAC;MACd;IACF,CAAC,CAAC;IAEF,OAAO+E,WAAW,GAAGD,YAAY,GAAGS,eAAe,GAAG,EAAE,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA1N,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,IAAIC,mBAAmB,CAACQ,OAAO,EAAE;MAC/BoC,YAAY,CAAC5C,mBAAmB,CAACQ,OAAO,CAAC;MACzCR,mBAAmB,CAACQ,OAAO,GAAG,IAAI;IACpC;IAEA,MAAM8M,UAAU,GAAGtI,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACnD,MAAMe,IAAI,GAAG,GAAG;IAChBsH,UAAU,CAACzM,KAAK,GAAGmF,IAAI;IACvBsH,UAAU,CAACzI,MAAM,GAAGmB,IAAI;IAExBD,UAAU,CAACuH,UAAU,EAAEA,UAAU,CAACnI,UAAU,CAAC,IAAI,CAAC,EAAEa,IAAI,CAAC,CAACuH,IAAI,CAAC,MAAM;MACnE;MACAC,oBAAoB,CAACF,UAAU,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIC,cAAc,IAAK;IAC/C,MAAMC,WAAW,GAAG3D,gBAAgB,CAAC,CAAC;;IAEtC;IACA,MAAM4D,SAAS,GAAG,IAAI3L,IAAI,CAAC,CAAC;IAC5B2L,SAAS,CAACC,OAAO,CAACD,SAAS,CAAClL,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAC3C,MAAMoL,YAAY,GAAG,GAAGF,SAAS,CAACxL,WAAW,CAAC,CAAC,IAAIwL,SAAS,CAACrL,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAIqL,SAAS,CAAClL,OAAO,CAAC,CAAC,EAAE;;IAEpG;IACA,MAAMqL,cAAc,GAAG9I,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACvD,MAAM8I,sBAAsB,GAAG,GAAG;IAClC,MAAMC,mBAAmB,GAAGP,cAAc,CAAC5M,KAAK;IAChD,MAAMoN,WAAW,GAAGD,mBAAmB,GAAG,CAAC;IAC3C,MAAME,WAAW,GAAG,GAAG,CAAC,CAAC;IACzB,MAAMC,OAAO,GAAG,EAAE;IAElBL,cAAc,CAACjN,KAAK,GAAGoN,WAAW;IAClCH,cAAc,CAACjJ,MAAM,GAAGmJ,mBAAmB,GAAGD,sBAAsB,GAAGG,WAAW,GAAGC,OAAO;IAE5F,MAAMjJ,GAAG,GAAG4I,cAAc,CAAC3I,UAAU,CAAC,IAAI,CAAC;;IAE3C;IACAD,GAAG,CAACwB,SAAS,GAAG,MAAM;IACtBxB,GAAG,CAACyB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEmH,cAAc,CAACjN,KAAK,EAAEiN,cAAc,CAACjJ,MAAM,CAAC;;IAE/D;IACAK,GAAG,CAACsB,SAAS,CAACiH,cAAc,EAAE,CAACQ,WAAW,GAAGR,cAAc,CAAC5M,KAAK,IAAI,CAAC,EAAEkN,sBAAsB,CAAC;;IAE/F;IACA,MAAMK,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;IACzD,MAAMC,SAAS,GAAG,CAChBX,WAAW,CAACvK,EAAE,EACduK,WAAW,CAACpD,IAAI,EAChB,GAAGoD,WAAW,CAAClD,SAAS,EAAE,EAC1BqD,YAAY,CACb;IAED,MAAMS,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACvD,MAAMC,WAAW,GAAG,CAClBb,WAAW,CAAC/C,MAAM,IAAI,KAAK,EAC3B+C,WAAW,CAACrD,IAAI,EAChBqD,WAAW,CAACnD,YAAY,EACxB,SAAS,CACV;;IAED;IACA,MAAMiE,OAAO,GAAGR,mBAAmB,GAAGD,sBAAsB,GAAG,EAAE;IACjE,MAAMU,YAAY,GAAGzC,iBAAiB,CACpC9G,GAAG,EACH,EAAE,EACFsJ,OAAO,EACPP,WAAW,GAAG,GAAG,EACjB,MAAM,EACNG,YAAY,EACZC,SACF,CAAC;;IAED;IACA,MAAMK,OAAO,GAAGF,OAAO,GAAGC,YAAY,GAAG,EAAE;IAC3CzC,iBAAiB,CACf9G,GAAG,EACH,EAAE,EACFwJ,OAAO,EACPT,WAAW,GAAG,GAAG,EACjB,MAAM,EACNK,cAAc,EACdC,WACF,CAAC;IACD;IACAI,cAAc,CAACb,cAAc,CAAC;EAChC,CAAC;;EAED;EACA,MAAMa,cAAc,GAAIpO,MAAM,IAAK;IACjC,IAAI;MACF;MACAZ,WAAW,CAAC,KAAK,CAAC;MAClBI,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACAC,mBAAmB,CAACQ,OAAO,GAAGoO,UAAU,CAAC,MAAM;QAC7C7O,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAM8O,IAAI,GAAG7J,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;;MAExC;MACA,MAAM6J,OAAO,GAAGvO,MAAM,CAACwO,SAAS,CAAC,WAAW,CAAC;;MAE7C;MACAF,IAAI,CAACG,IAAI,GAAGF,OAAO;MACnB,MAAMpB,WAAW,GAAG3D,gBAAgB,CAAC,CAAC;MACtC8E,IAAI,CAACI,QAAQ,GAAG,aAAavB,WAAW,CAACrD,IAAI,MAAM;;MAEnD;MACArF,QAAQ,CAACkK,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;;MAEZ;MACApK,QAAQ,CAACkK,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC,CAAC,CAAC,OAAO3G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDvI,WAAW,CAAC,KAAK,CAAC;MAClBE,YAAY,CAAC,aAAa,CAAC;IAC7B;EACF,CAAC;EAED,SAAS4K,mBAAmBA,CAAA,EAAG;IAC7B,IAAI,EAAC3L,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEwQ,IAAI,KAAIhQ,UAAU,CAACkF,MAAM,CAACtB,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC,CAAChC,MAAM,GAAG,CAAC,EAAE;MAC1E,OAAO,CAAC;IACV;IACA,IAAI0H,MAAM,GAAG,CAAC;IACd,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3H,UAAU,CAAC4B,MAAM,EAAE+F,CAAC,EAAE,EAAE;MAC1C,IAAI3H,UAAU,CAAC2H,CAAC,CAAC,IAAIA,CAAC,GAAGnI,cAAc,CAACwQ,IAAI,CAACpO,MAAM,EAAE;QACnD0H,MAAM,IAAItJ,UAAU,CAAC2H,CAAC,CAAC,CAACsI,KAAK,GAAGzQ,cAAc,CAACwQ,IAAI,CAACrI,CAAC,CAAC;MACxD;IACF;IACA2B,MAAM,IAAK,CAAC,GAAG,IAAK;IACpB,IAAI9J,cAAc,CAAC0Q,MAAM,EAAE;MACzB5G,MAAM,IAAI9J,cAAc,CAAC0Q,MAAM;IACjC;IACA,OAAO5G,MAAM,GAAG,CAAC;EACnB;EAEA,SAAS6G,iBAAiBA,CAACnO,CAAC,EAAE;IAC5B,MAAM;MAAEoD,OAAO;MAAEC;IAAQ,CAAC,GAAGrD,CAAC;IAC9B,MAAM;MAAE0C,IAAI;MAAEY,GAAG;MAAE/D,KAAK;MAAEgE;IAAO,CAAC,GAAGvD,CAAC,CAACoO,MAAM,CAACC,qBAAqB,CAAC,CAAC;IACrE,IAAI7K,UAAU,GAAGL,eAAe,CAACC,OAAO,EAAEC,OAAO,EAAEX,IAAI,EAAEY,GAAG,EAAE/D,KAAK,EAAEgE,MAAM,CAAC;IAC5E,IAAIC,UAAU,KAAK,IAAI,EAAE;MACvB,IAAIxG,aAAa,CAACgG,QAAQ,CAACQ,UAAU,CAAC,EAAE;QACtCvG,eAAe,CAACgG,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACtB,CAAC,IAAIA,CAAC,KAAK4B,UAAU,CAAC,CAAC;MAC7D,CAAC,MAAM;QACLvG,eAAe,CAACgG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,UAAU,CAAC,CAAC;MAChD;MACA;MACApB,aAAa,CAACoB,UAAU,CAAC;IAC3B;EACF;EAEA,MAAM8K,eAAe,GAAItO,CAAC,IAAK;IAC7B,MAAM;MAAEoD,OAAO;MAAEC;IAAQ,CAAC,GAAGrD,CAAC;IAC9B,MAAM;MAAE0C,IAAI;MAAEY,GAAG;MAAE/D,KAAK;MAAEgE;IAAO,CAAC,GAAGvD,CAAC,CAACoO,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAErE,IAAIjL,OAAO,KAAKvF,eAAe,CAACqB,OAAO,CAACpB,CAAC,IAAIuF,OAAO,KAAKxF,eAAe,CAACqB,OAAO,CAACnB,CAAC,EAAE;MAClF;IACF;IAEAF,eAAe,CAACqB,OAAO,GAAG;MAAEpB,CAAC,EAAEsF,OAAO;MAAErF,CAAC,EAAEsF;IAAQ,CAAC;IAEpD,IAAIzF,aAAa,CAACsB,OAAO,EAAE;MACzBoC,YAAY,CAAC1D,aAAa,CAACsB,OAAO,CAAC;IACrC;IAEAtB,aAAa,CAACsB,OAAO,GAAGoO,UAAU,CAAC,MAAM;MACvC,MAAM9J,UAAU,GAAGL,eAAe,CAACC,OAAO,EAAEC,OAAO,EAAEX,IAAI,EAAEY,GAAG,EAAE/D,KAAK,EAAEgE,MAAM,CAAC;MAC9EpG,YAAY,CAACqG,UAAU,CAAC;MACxBpB,aAAa,CAACoB,UAAU,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAM+K,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,OAAO,GAAG,OAAO;IACrB,IAAIrM,IAAI,GAAG,EAAE;IACb,IAAI9F,WAAW,KAAK,SAAS,EAAE;MAC7BmS,OAAO,GAAG,MAAM;MAChBrM,IAAI,gBAAGtG,OAAA,CAACJ,aAAa;QAACa,cAAc,EAAEA;MAAe;QAAAmS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC1D,CAAC,MAAM,IAAIvS,WAAW,KAAK,OAAO,EAAE;MAClCmS,OAAO,GAAG,MAAM;MAChBrM,IAAI,gBAAGtG,OAAA,CAACH,UAAU;QAACY,cAAc,EAAEA;MAAe;QAAAmS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACvD,CAAC,MAAM,IAAIvS,WAAW,KAAK,cAAc,EAAE;MACzCmS,OAAO,GAAG,MAAM;MAChBrM,IAAI,gBAAGtG,OAAA,CAACF,gBAAgB;QAACW,cAAc,EAAEA;MAAe;QAAAmS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7D;IACA,oBACE/S,OAAA;MAAAgT,QAAA,GACG1M,IAAI,eACLtG,OAAA;QAAKiT,KAAK,EAAE;UAAEN,OAAO,EAAEA;QAAQ,CAAE;QAAAK,QAAA,gBAE/BhT,OAAA;UAAKkT,SAAS,EAAC,SAAS;UAAAF,QAAA,gBACtBhT,OAAA;YAAKkT,SAAS,EAAC,YAAY;YAAAF,QAAA,gBACzBhT,OAAA;cAAMkT,SAAS,EAAC,cAAc;cAAAF,QAAA,EAAC;YAAO;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEA,CAAA,KAAMjN,kBAAkB,CAAC,GAAG,CAAE;cAAA8M,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClF/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEA,CAAA,KAAMjN,kBAAkB,CAAC,GAAG,CAAE;cAAA8M,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClF/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEA,CAAA,KAAMjN,kBAAkB,CAAC,GAAG,CAAE;cAAA8M,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClF/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEA,CAAA,KAAMjN,kBAAkB,CAAC,GAAG,CAAE;cAAA8M,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClF/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEA,CAAA,KAAMjN,kBAAkB,CAAC,GAAG,CAAE;cAAA8M,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACN/S,OAAA;YAAKkT,SAAS,EAAC,YAAY;YAAAF,QAAA,gBACzBhT,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEA,CAAA,KAAMxK,KAAK,CAAC,CAAE;cAAAqK,QAAA,EAAC;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnE/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAErP,UAAW;cAACsP,QAAQ,EAAEvR,OAAO,CAACkC,MAAM,KAAK,CAAE;cAAAiP,QAAA,EAAC;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChG/S,OAAA;cAAQkT,SAAS,EAAC,aAAa;cAACC,OAAO,EAAEjD,gBAAiB;cAACkD,QAAQ,EAAE7Q,QAAQ,IAAIZ,cAAc,CAACU,QAAQ,CAAC0B,MAAM,KAAK,CAAC,IAAI5B,UAAU,CAACkF,MAAM,CAACtB,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC,CAAChC,MAAM,GAAG,CAAE;cAAAiP,QAAA,EACpKzQ,QAAQ,GAAG,SAAS,GAAG;YAAI;cAAAqQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,EACRtQ,SAAS,iBAAIzC,OAAA;cAAKkT,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAEvQ;YAAS;cAAAmQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC1DpQ,WAAW,iBAAI3C,OAAA;cAAKkT,SAAS,EAAC,cAAc;cAAAF,QAAA,EAAC;YAAO;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/S,OAAA;UAAKkT,SAAS,EAAC,cAAc;UAAAF,QAAA,gBAE3BhT,OAAA;YAAKkT,SAAS,EAAC,gBAAgB;YAAAF,QAAA,gBAC7BhT,OAAA;cAAAgT,QAAA,eAAKhT,OAAA;gBAAAgT,QAAA,EAAI;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB/S,OAAA;cAAKkT,SAAS,EAAC,gBAAgB;cAAAF,QAAA,EAC5B/T,UAAU,CAACqD,GAAG,CAAEY,IAAI,iBACnBlD,OAAA;gBAEEmT,OAAO,EAAGhP,CAAC,IAAKuB,gBAAgB,CAACxC,IAAI,CAAC8C,EAAE,EAAE7B,CAAC,CAAE;gBAC7C+O,SAAS,EAAC,WAAW;gBACrBG,YAAY,EAAGlP,CAAC,IAAK;kBACnB;kBACA,IAAIrB,eAAe,CAACO,OAAO,EAAE;oBAC3BoC,YAAY,CAAC3C,eAAe,CAACO,OAAO,CAAC;kBACvC;;kBAEA;kBACA,MAAMiQ,IAAI,GAAGnP,CAAC,CAACoP,aAAa,CAACf,qBAAqB,CAAC,CAAC;kBACpD,MAAMgB,QAAQ,GAAGF,IAAI,CAACG,KAAK,GAAG,EAAE;;kBAEhC;kBACA,IAAIC,QAAQ;kBACZ,MAAMC,aAAa,GAAG,GAAG,CAAC,CAAC;kBAC3B,MAAMC,YAAY,GAAG1T,MAAM,CAAC2T,WAAW;;kBAEvC;kBACA,IAAIP,IAAI,CAAC7L,GAAG,GAAGkM,aAAa,GAAGC,YAAY,EAAE;oBAC3CF,QAAQ,GAAG9J,IAAI,CAAC6F,GAAG,CAAC,EAAE,EAAEmE,YAAY,GAAGD,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC;kBAC9D,CAAC,MAAM;oBACLD,QAAQ,GAAGJ,IAAI,CAAC7L,GAAG,GAAG,EAAE,CAAC,CAAC;kBAC5B;;kBAEA;kBACA3E,eAAe,CAACO,OAAO,GAAGoO,UAAU,CAAC,MAAM;oBACzCzO,cAAc,CAAC;sBACbC,OAAO,EAAE,IAAI;sBACbC,IAAI,EAAEA,IAAI;sBACVC,QAAQ,EAAE,QAAQ;sBAClBlB,CAAC,EAAEuR,QAAQ;sBACXtR,CAAC,EAAEwR;oBACL,CAAC,CAAC;kBACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;gBACZ,CAAE;gBACFI,YAAY,EAAEA,CAAA,KAAM;kBAClB;kBACA,IAAIhR,eAAe,CAACO,OAAO,EAAE;oBAC3BoC,YAAY,CAAC3C,eAAe,CAACO,OAAO,CAAC;oBACrCP,eAAe,CAACO,OAAO,GAAG,IAAI;kBAChC;;kBAEA;kBACAP,eAAe,CAACO,OAAO,GAAGoO,UAAU,CAAC,MAAM;oBACzCzO,cAAc,CAACoE,IAAI,KAAK;sBACtB,GAAGA,IAAI;sBACPnE,OAAO,EAAE;oBACX,CAAC,CAAC,CAAC;kBACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBACX,CAAE;gBAAA+P,QAAA,gBAEFhT,OAAA;kBAAK+I,GAAG,EAAE7F,IAAI,CAAC6F,GAAI;kBAACgL,GAAG,EAAE7Q,IAAI,CAAC8I;gBAAK;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtC/S,OAAA;kBAAAgT,QAAA,EAAO9P,IAAI,CAAC8I;gBAAI;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GArDnB7P,IAAI,CAAC8C,EAAE;gBAAA4M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsDT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/S,OAAA;YAAKkT,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/BhT,OAAA;cACEgU,GAAG,EAAE/S,SAAU;cACfiS,SAAS,EAAC,QAAQ;cAClBC,OAAO,EAAEhP,CAAC,IAAImO,iBAAiB,CAACnO,CAAC,CAAE;cACnC8P,WAAW,EAAExB,eAAgB;cAC7BqB,YAAY,EAAEA,CAAA,KAAM;gBAClB,IAAI/R,aAAa,CAACsB,OAAO,EAAE;kBACzBoC,YAAY,CAAC1D,aAAa,CAACsB,OAAO,CAAC;gBACrC;gBACA/B,YAAY,CAAC,IAAI,CAAC;cACpB,CAAE;cAAA0R,QAAA,GAGDrR,cAAc,CAACU,QAAQ,CAACC,GAAG,CAAC,CAACsF,IAAI,EAAEpB,KAAK,KAAK;gBAC5C,oBACExG,OAAA;kBAEEkT,SAAS,EAAC,WAAW;kBACrBD,KAAK,EAAE;oBACL5Q,QAAQ,EAAErD,IAAI,CAACkV,cAAc,CAACnT,UAAU,EAAE6G,IAAI,CAAC;oBAC/CzE,QAAQ,EAAE,UAAU;oBACpBO,KAAK,EAAE,MAAM;oBACbgE,MAAM,EAAE,MAAM;oBACdyM,cAAc,EAAE,GAAGC,QAAQ,CAACrT,UAAU,GAAG,GAAG,CAAC,SAAS;oBACtDsT,gBAAgB,EAAE,QAAQ;oBAC1BC,kBAAkB,EAAE,KAAK;oBACzBC,eAAe,EAAE,OAAO;oBACxBC,eAAe,EAAErS,UAAU,CAACqE,KAAK,CAAC,IAAI,OAAOrE,UAAU,CAACqE,KAAK,CAAC,CAACuC,GAAG;kBACpE,CAAE;kBAAAiK,QAAA,GAED7R,aAAa,CAACgG,QAAQ,CAACX,KAAK,CAAC,iBAAIxG,OAAA;oBAAKkT,SAAS,EAAC;kBAAS;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAChE1R,SAAS,KAAKmF,KAAK,iBAAIxG,OAAA;oBAAKkT,SAAS,EAAC;kBAAY;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAfrDvM,KAAK;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgBP,CAAC;cAEV,CAAC,CAAC,eAEA/S,OAAA;gBACEiT,KAAK,EAAE;kBACLvP,KAAK,EAAE,MAAM;kBACbgE,MAAM,EAAE,MAAM;kBACd+M,MAAM,EAAE;gBACV,CAAE;gBAAAzB,QAAA,EAEDrR,cAAc,CAAC+S,EAAE,iBAAI1U,OAAA;kBAAK+I,GAAG,EAAEpH,cAAc,CAAC+S,EAAG;kBAACX,GAAG,EAAC,IAAI;kBAACd,KAAK,EAAE;oBAAEvP,KAAK,EAAE,MAAM;oBAAEgE,MAAM,EAAE;kBAAO;gBAAE;kBAAAkL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEL,CAAC,EAIJpR,cAAc,CAACU,QAAQ,CAAC0B,MAAM,GAAG,CAAC,iBAClC/D,OAAA;cAAKkT,SAAS,EAAC,kBAAkB;cAACD,KAAK,EAAE;gBAAEvP,KAAK,EAAE3C,UAAU,GAAG,EAAE;gBAAE4T,UAAU,EAAE;cAAM,CAAE;cAAA3B,QAAA,eACrFhT,OAAA;gBAAKkT,SAAS,EAAC,cAAc;gBAACc,GAAG,EAAE9S,cAAe;gBAAC0T,OAAO,EAAE7N,sBAAuB;gBAAAiM,QAAA,EAChF7Q,UAAU,CAACG,GAAG,CAAC,CAACuS,KAAK,EAAErO,KAAK,kBAC3BxG,OAAA;kBAEEkT,SAAS,EAAC,iBAAiB;kBAC3BC,OAAO,EAAEA,CAAA,KAAMjM,oBAAoB,CAACV,KAAK,CAAE;kBAC3C6M,YAAY,EAAEA,CAAA,KAAM/R,YAAY,CAACkF,KAAK,CAAE;kBACxCsN,YAAY,EAAEA,CAAA,KAAMxS,YAAY,CAAC,IAAI,CAAE;kBAAA0R,QAAA,GAEtC6B,KAAK,iBAAI7U,OAAA;oBAAK+I,GAAG,EAAE8L,KAAK,CAAC9L,GAAI;oBAACkK,KAAK,EAAE;sBAAEvP,KAAK,EAAE,MAAM;sBAAEgE,MAAM,EAAE;oBAAO;kBAAE;oBAAAkL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/E/S,OAAA;oBAAKkT,SAAS,EAAC,gBAAgB;oBAAAF,QAAA,EAAExM,KAAK,GAAG;kBAAC;oBAAAoM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjD/S,OAAA;oBAAKkT,SAAS,EAAC,iBAAiB;oBAAAF,QAAA,EAAE6B,KAAK,GAAGA,KAAK,CAAC7I,IAAI,GAAG;kBAAK;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClE5R,aAAa,CAACgG,QAAQ,CAACX,KAAK,CAAC,iBAAIxG,OAAA;oBAAKkT,SAAS,EAAC;kBAAS;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAChE1R,SAAS,KAAKmF,KAAK,iBAAIxG,OAAA;oBAAKkT,SAAS,EAAC;kBAAY;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAVrDvM,KAAK,GAAG,CAAC;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWX,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAINpR,cAAc,CAACwQ,IAAI;YAAA;YACjB;YACAnS,OAAA;cAAKkT,SAAS,EAAC,kBAAkB;cAAAF,QAAA,eAC/BhT,OAAA;gBAAKkT,SAAS,EAAC,qBAAqB;gBAAAF,QAAA,EACjC1F,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;cAAM;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/S,OAAA,CAACP,MAAM;UACLqV,MAAM,EAAEvT,YAAa;UACrBwT,OAAO,EAAEA,CAAA,KAAMvT,eAAe,CAAC,KAAK,CAAE;UACtC4E,KAAK,EAAE3E,WAAY;UACnBuT,QAAQ,EAAE3O;QAAmB;UAAAuM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EAGDhQ,WAAW,CAACE,OAAO,iBAClBjD,OAAA;UACEiT,KAAK,EAAE;YACL9P,QAAQ,EAAE,OAAO;YACjBsE,GAAG,EAAE1E,WAAW,CAACb,CAAC,GAAG,IAAI;YACzB2E,IAAI,EAAE9D,WAAW,CAACd,CAAC,GAAG,IAAI;YAC1BwS,MAAM,EAAE;UACV,CAAE;UACFpB,YAAY,EAAEA,CAAA,KAAM;YAClB;YACA,IAAIvQ,eAAe,CAACO,OAAO,EAAE;cAC3BoC,YAAY,CAAC3C,eAAe,CAACO,OAAO,CAAC;cACrCP,eAAe,CAACO,OAAO,GAAG,IAAI;YAChC;UACF,CAAE;UACFyQ,YAAY,EAAEA,CAAA,KAAM;YAClB;YACA9Q,cAAc,CAACoE,IAAI,KAAK;cACtB,GAAGA,IAAI;cACPnE,OAAO,EAAE;YACX,CAAC,CAAC,CAAC;UACL,CAAE;UAAA+P,QAAA,eAEFhT,OAAA,CAACL,WAAW;YACVuD,IAAI,EAAEH,WAAW,CAACG,IAAK;YACvBC,QAAQ,EAAEJ,WAAW,CAACI,QAAS;YAC/BF,OAAO,EAAEF,WAAW,CAACE;UAAQ;YAAA2P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE/S,OAAA;IAAKkT,SAAS,EAAC,eAAe;IAAAF,QAAA,GAC3B1S,UAAU,iBAAIN,OAAA,CAACN,YAAY;MAACuV,QAAQ,EAAEA,CAAA,KAAM1U,aAAa,CAAC,KAAK;IAAE;MAAAqS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrE/S,OAAA;MAAKiT,KAAK,EAAE;QAAEN,OAAO,EAAErS,UAAU,GAAG,MAAM,GAAG;MAAQ,CAAE;MAAA0S,QAAA,gBAErDhT,OAAA;QAAQkT,SAAS,EAAC,QAAQ;QAAAF,QAAA,eACxBhT,OAAA;UAAKkT,SAAS,EAAC,gBAAgB;UAAAF,QAAA,gBAC7BhT,OAAA;YAAKkT,SAAS,EAAC,cAAc;YAAAF,QAAA,gBAC3BhT,OAAA;cACE+I,GAAG,EAAEhK,IAAK;cACVgV,GAAG,EAAC,0BAAM;cACVb,SAAS,EAAC,MAAM;cAChBD,KAAK,EAAE;gBAAEiC,MAAM,EAAE;cAAU,CAAE;cAC7B/B,OAAO,EAAEA,CAAA,KAAM1S,cAAc,CAAC,MAAM;YAAE;cAAAmS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACF/S,OAAA;cAAMkT,SAAS,EAAC,QAAQ;cAAAF,QAAA,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN/S,OAAA;YAAKkT,SAAS,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAExB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGRL,iBAAiB,CAAC,CAAC,eAGpB1S,OAAA;QAAQkT,SAAS,EAAC,QAAQ;QAAAF,QAAA,eACxBhT,OAAA;UAAKkT,SAAS,EAAC,gBAAgB;UAAAF,QAAA,gBAC7BhT,OAAA;YAAKkT,SAAS,EAAC,aAAa;YAAAF,QAAA,eAC1BhT,OAAA;cAAAgT,QAAA,EAAG;YAAwB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACN/S,OAAA;YAAKkT,SAAS,EAAC,cAAc;YAAAF,QAAA,GAC1BtS,WAAW,CAACG,UAAU,iBACrBb,OAAA;cAAMkT,SAAS,EAAC,cAAc;cAAAF,QAAA,GAAC,wCAAQ,EAACtS,WAAW,CAACG,UAAU;YAAA;cAAA+R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACtE,eACD/S,OAAA;cAAMkT,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC/S,OAAA;cAAG6R,IAAI,EAAC,GAAG;cAACsB,OAAO,EAAGhP,CAAC,IAAK;gBAAEA,CAAC,CAACI,cAAc,CAAC,CAAC;gBAAE9D,cAAc,CAAC,cAAc,CAAC;cAAE,CAAE;cAAAuS,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7F/S,OAAA;cAAMkT,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC/S,OAAA;cAAG6R,IAAI,EAAC,GAAG;cAACsB,OAAO,EAAGhP,CAAC,IAAK;gBAAEA,CAAC,CAACI,cAAc,CAAC,CAAC;gBAAE9D,cAAc,CAAC,SAAS,CAAC;cAAE,CAAE;cAAAuS,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxF/S,OAAA;cAAMkT,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC/S,OAAA;cAAG6R,IAAI,EAAC,GAAG;cAACsB,OAAO,EAAGhP,CAAC,IAAK;gBAAEA,CAAC,CAACI,cAAc,CAAC,CAAC;gBAAE9D,cAAc,CAAC,OAAO,CAAC;cAAE,CAAE;cAAAuS,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1S,EAAA,CApgCQD,GAAG;AAAA+U,EAAA,GAAH/U,GAAG;AAugCZ,eAAeA,GAAG;AAAC,IAAA+U,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}