{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodSpeciesIntro.js\";\nimport React from 'react';\nimport { woodImages } from './Source';\nimport './PolicyPages.css';\nimport './WoodSpeciesIntro.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction WoodSpeciesIntro({\n  setCurrentPage\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"policy-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"policy-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u6728\\u79CD\\u4ECB\\u7ECD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"wood-species-intro\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"intro-text\",\n          children: \"\\u67CF\\u5C14\\u5730\\u677F\\u7CBE\\u9009\\u5168\\u7403\\u4F18\\u8D28\\u6728\\u6750\\uFF0C\\u6BCF\\u4E00\\u79CD\\u6728\\u79CD\\u90FD\\u7ECF\\u8FC7\\u4E25\\u683C\\u7B5B\\u9009\\uFF0C\\u786E\\u4FDD\\u54C1\\u8D28\\u5353\\u8D8A\\u3002 \\u4EE5\\u4E0B\\u662F\\u6211\\u4EEC\\u4E3B\\u8981\\u4F7F\\u7528\\u7684\\u6728\\u79CD\\u8BE6\\u7EC6\\u4ECB\\u7ECD\\uFF0C\\u4E86\\u89E3\\u5B83\\u4EEC\\u7684\\u7279\\u6027\\u6709\\u52A9\\u4E8E\\u60A8\\u505A\\u51FA\\u6700\\u4F73\\u9009\\u62E9\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"wood-species-grid\",\n          children: woodImages.map(wood => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wood-species-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-card-image\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: wood.src,\n                alt: wood.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"wood-price\",\n                children: [\"\\xA5\", wood.price, \"/\\u33A1\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"wood-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"wood-card-title\",\n                children: wood.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"wood-detail-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"wood-detail-image\",\n                  children: wood.desc.img && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: wood.desc.img,\n                    alt: `${wood.name}详情`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 31,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"wood-detail-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"wood-detail-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-label\",\n                      children: \"\\u5B66\\u540D:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 36,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-value\",\n                      children: wood.desc.scientificName || '暂无数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 37,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"wood-detail-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-label\",\n                      children: \"\\u4EA7\\u5730:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 41,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-value\",\n                      children: wood.desc.origin || '暂无数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 42,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"wood-detail-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-label\",\n                      children: \"\\u6750\\u6027:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 46,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-value\",\n                      children: wood.desc.woodProperty || '暂无数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 47,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 45,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"wood-detail-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-label\",\n                      children: \"\\u7528\\u9014:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 51,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wood-detail-value\",\n                      children: wood.desc.purpose || '暂无数据'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 52,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this)]\n          }, wood.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"policy-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u4E86\\u89E3\\u66F4\\u591A\\u6728\\u79CD\\u4FE1\\u606F\\uFF0C\\u8BF7\\u8054\\u7CFB\\u6211\\u4EEC\\u7684\\u4E13\\u4E1A\\u987E\\u95EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"return-button\",\n          onClick: () => setCurrentPage('main'),\n          children: \"\\u8FD4\\u56DE\\u4E3B\\u9875\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_c = WoodSpeciesIntro;\nexport default WoodSpeciesIntro;\nvar _c;\n$RefreshReg$(_c, \"WoodSpeciesIntro\");", "map": {"version": 3, "names": ["React", "woodImages", "jsxDEV", "_jsxDEV", "WoodSpeciesIntro", "setCurrentPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "wood", "src", "alt", "name", "price", "desc", "img", "scientificName", "origin", "woodProperty", "purpose", "id", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/WoodSpeciesIntro.js"], "sourcesContent": ["import React from 'react';\nimport { woodImages } from './Source';\nimport './PolicyPages.css';\nimport './WoodSpeciesIntro.css';\n\nfunction WoodSpeciesIntro({ setCurrentPage }) {\n  return (\n    <div className=\"policy-container\">\n      <div className=\"policy-content\">\n        <h1>木种介绍</h1>\n        \n        <div className=\"wood-species-intro\">\n          <p className=\"intro-text\">\n            柏尔地板精选全球优质木材，每一种木种都经过严格筛选，确保品质卓越。\n            以下是我们主要使用的木种详细介绍，了解它们的特性有助于您做出最佳选择。\n          </p>\n          \n          <div className=\"wood-species-grid\">\n            {woodImages.map((wood) => (\n              <div key={wood.id} className=\"wood-species-card\">\n                <div className=\"wood-card-image\">\n                  <img src={wood.src} alt={wood.name} />\n                  <div className=\"wood-price\">¥{wood.price}/㎡</div>\n                </div>\n                \n                <div className=\"wood-card-content\">\n                  <h3 className=\"wood-card-title\">{wood.name}</h3>\n                  \n                  <div className=\"wood-detail-section\">\n                    <div className=\"wood-detail-image\">\n                      {wood.desc.img && <img src={wood.desc.img} alt={`${wood.name}详情`} />}\n                    </div>\n                    \n                    <div className=\"wood-detail-info\">\n                      <div className=\"wood-detail-row\">\n                        <span className=\"wood-detail-label\">学名:</span>\n                        <span className=\"wood-detail-value\">{wood.desc.scientificName || '暂无数据'}</span>\n                      </div>\n                      \n                      <div className=\"wood-detail-row\">\n                        <span className=\"wood-detail-label\">产地:</span>\n                        <span className=\"wood-detail-value\">{wood.desc.origin || '暂无数据'}</span>\n                      </div>\n                      \n                      <div className=\"wood-detail-row\">\n                        <span className=\"wood-detail-label\">材性:</span>\n                        <span className=\"wood-detail-value\">{wood.desc.woodProperty || '暂无数据'}</span>\n                      </div>\n                      \n                      <div className=\"wood-detail-row\">\n                        <span className=\"wood-detail-label\">用途:</span>\n                        <span className=\"wood-detail-value\">{wood.desc.purpose || '暂无数据'}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"policy-footer\">\n          <p>了解更多木种信息，请联系我们的专业顾问</p>\n          <button className=\"return-button\" onClick={() => setCurrentPage('main')}>\n            返回主页\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default WoodSpeciesIntro;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAO,mBAAmB;AAC1B,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,SAASC,gBAAgBA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAC5C,oBACEF,OAAA;IAAKG,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/BJ,OAAA;MAAKG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BJ,OAAA;QAAAI,QAAA,EAAI;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEbR,OAAA;QAAKG,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCJ,OAAA;UAAGG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAG1B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA;UAAKG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/BN,UAAU,CAACW,GAAG,CAAEC,IAAI,iBACnBV,OAAA;YAAmBG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9CJ,OAAA;cAAKG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BJ,OAAA;gBAAKW,GAAG,EAAED,IAAI,CAACC,GAAI;gBAACC,GAAG,EAAEF,IAAI,CAACG;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtCR,OAAA;gBAAKG,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,MAAC,EAACM,IAAI,CAACI,KAAK,EAAC,SAAE;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eAENR,OAAA;cAAKG,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCJ,OAAA;gBAAIG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEM,IAAI,CAACG;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEhDR,OAAA;gBAAKG,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCJ,OAAA;kBAAKG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAC/BM,IAAI,CAACK,IAAI,CAACC,GAAG,iBAAIhB,OAAA;oBAAKW,GAAG,EAAED,IAAI,CAACK,IAAI,CAACC,GAAI;oBAACJ,GAAG,EAAE,GAAGF,IAAI,CAACG,IAAI;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eAENR,OAAA;kBAAKG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BJ,OAAA;oBAAKG,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BJ,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9CR,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAEM,IAAI,CAACK,IAAI,CAACE,cAAc,IAAI;oBAAM;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,eAENR,OAAA;oBAAKG,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BJ,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9CR,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAEM,IAAI,CAACK,IAAI,CAACG,MAAM,IAAI;oBAAM;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eAENR,OAAA;oBAAKG,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BJ,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9CR,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAEM,IAAI,CAACK,IAAI,CAACI,YAAY,IAAI;oBAAM;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eAENR,OAAA;oBAAKG,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BJ,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9CR,OAAA;sBAAMG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAEM,IAAI,CAACK,IAAI,CAACK,OAAO,IAAI;oBAAM;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GApCEE,IAAI,CAACW,EAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqCZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENR,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BJ,OAAA;UAAAI,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1BR,OAAA;UAAQG,SAAS,EAAC,eAAe;UAACmB,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,MAAM,CAAE;UAAAE,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACe,EAAA,GAjEQtB,gBAAgB;AAmEzB,eAAeA,gBAAgB;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}