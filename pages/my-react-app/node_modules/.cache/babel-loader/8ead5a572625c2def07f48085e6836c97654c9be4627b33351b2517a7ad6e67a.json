{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/TermsOfUse.js\";\nimport React from 'react';\nimport './PolicyPages.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TermsOfUse({\n  setCurrentPage\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"policy-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"policy-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u4F7F\\u7528\\u6761\\u6B3E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"1. \\u63A5\\u53D7\\u6761\\u6B3E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6B22\\u8FCE\\u8BBF\\u95EE\\u67CF\\u5C14\\u6728\\u4E1A\\u6709\\u9650\\u516C\\u53F8\\uFF08\\u4EE5\\u4E0B\\u7B80\\u79F0\\\"\\u67CF\\u5C14\\u5730\\u677F\\\"\\u3001\\\"\\u6211\\u4EEC\\\"\\u6216\\\"\\u6211\\u4EEC\\u7684\\\"\\uFF09\\u7684\\u7F51\\u7AD9\\u3002\\u901A\\u8FC7\\u8BBF\\u95EE\\u548C\\u4F7F\\u7528\\u672C\\u7F51\\u7AD9\\uFF0C\\u60A8\\u540C\\u610F\\u53D7\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u7684\\u7EA6\\u675F\\u3002\\u5982\\u679C\\u60A8\\u4E0D\\u540C\\u610F\\u8FD9\\u4E9B\\u6761\\u6B3E\\uFF0C\\u8BF7\\u4E0D\\u8981\\u4F7F\\u7528\\u672C\\u7F51\\u7AD9\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"2. \\u4F7F\\u7528\\u8BB8\\u53EF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5728\\u60A8\\u9075\\u5B88\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u7684\\u524D\\u63D0\\u4E0B\\uFF0C\\u67CF\\u5C14\\u5730\\u677F\\u6388\\u4E88\\u60A8\\u8BBF\\u95EE\\u548C\\u4E2A\\u4EBA\\u4F7F\\u7528\\u672C\\u7F51\\u7AD9\\u7684\\u6709\\u9650\\u3001\\u975E\\u6392\\u4ED6\\u6027\\u3001\\u4E0D\\u53EF\\u8F6C\\u8BA9\\u7684\\u8BB8\\u53EF\\u3002\\u60A8\\u4E0D\\u5F97\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u4FEE\\u6539\\u3001\\u590D\\u5236\\u3001\\u5206\\u53D1\\u3001\\u4F20\\u8F93\\u3001\\u5C55\\u793A\\u3001\\u6267\\u884C\\u3001\\u590D\\u5236\\u3001\\u51FA\\u7248\\u3001\\u8BB8\\u53EF\\u3001\\u521B\\u5EFA\\u884D\\u751F\\u4F5C\\u54C1\\u3001\\u8F6C\\u8BA9\\u6216\\u9500\\u552E\\u672C\\u7F51\\u7AD9\\u4E0A\\u7684\\u4EFB\\u4F55\\u4FE1\\u606F\\u3001\\u8F6F\\u4EF6\\u3001\\u4EA7\\u54C1\\u6216\\u670D\\u52A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u4F7F\\u7528\\u672C\\u7F51\\u7AD9\\u8FDB\\u884C\\u4EFB\\u4F55\\u975E\\u6CD5\\u6D3B\\u52A8\\u6216\\u53EF\\u80FD\\u635F\\u5BB3\\u4ED6\\u4EBA\\u6743\\u5229\\u7684\\u6D3B\\u52A8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5E72\\u6270\\u6216\\u7834\\u574F\\u672C\\u7F51\\u7AD9\\u7684\\u5B89\\u5168\\u529F\\u80FD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"3. \\u77E5\\u8BC6\\u4EA7\\u6743\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u672C\\u7F51\\u7AD9\\u53CA\\u5176\\u6240\\u6709\\u5185\\u5BB9\\u3001\\u529F\\u80FD\\u548C\\u8BBE\\u8BA1\\u5143\\u7D20\\uFF0C\\u5305\\u62EC\\u4F46\\u4E0D\\u9650\\u4E8E\\u6587\\u672C\\u3001\\u56FE\\u5F62\\u3001\\u5FBD\\u6807\\u3001\\u56FE\\u6807\\u3001\\u56FE\\u50CF\\u3001\\u97F3\\u9891\\u526A\\u8F91\\u3001\\u4E0B\\u8F7D\\u3001\\u6570\\u636E\\u7F16\\u8BD1\\u3001\\u8F6F\\u4EF6\\u548C\\u4EE3\\u7801\\uFF0C\\u5747\\u4E3A\\u67CF\\u5C14\\u5730\\u677F\\u6216\\u5176\\u8BB8\\u53EF\\u65B9\\u7684\\u8D22\\u4EA7\\uFF0C\\u53D7\\u4E2D\\u56FD\\u548C\\u56FD\\u9645\\u7248\\u6743\\u3001\\u5546\\u6807\\u3001\\u4E13\\u5229\\u548C\\u5176\\u4ED6\\u77E5\\u8BC6\\u4EA7\\u6743\\u6CD5\\u5F8B\\u7684\\u4FDD\\u62A4\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u672A\\u7ECF\\u67CF\\u5C14\\u5730\\u677F\\u660E\\u786E\\u4E66\\u9762\\u8BB8\\u53EF\\uFF0C\\u4E0D\\u5F97\\u590D\\u5236\\u3001\\u4FEE\\u6539\\u3001\\u521B\\u5EFA\\u884D\\u751F\\u4F5C\\u54C1\\u3001\\u516C\\u5F00\\u5C55\\u793A\\u3001\\u516C\\u5F00\\u6267\\u884C\\u3001\\u91CD\\u65B0\\u53D1\\u5E03\\u3001\\u4E0B\\u8F7D\\u3001\\u5B58\\u50A8\\u6216\\u4F20\\u8F93\\u672C\\u7F51\\u7AD9\\u7684\\u4EFB\\u4F55\\u6750\\u6599\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"4. \\u7528\\u6237\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5982\\u679C\\u60A8\\u5411\\u672C\\u7F51\\u7AD9\\u63D0\\u4EA4\\u3001\\u4E0A\\u4F20\\u3001\\u53D1\\u5E03\\u6216\\u4F20\\u8F93\\u4EFB\\u4F55\\u5185\\u5BB9\\uFF08\\\"\\u7528\\u6237\\u5185\\u5BB9\\\"\\uFF09\\uFF0C\\u60A8\\u4FDD\\u8BC1\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u60A8\\u62E5\\u6709\\u6216\\u6709\\u6743\\u4F7F\\u7528\\u548C\\u6388\\u6743\\u6211\\u4EEC\\u4F7F\\u7528\\u8BE5\\u7528\\u6237\\u5185\\u5BB9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u8BE5\\u7528\\u6237\\u5185\\u5BB9\\u4E0D\\u4FB5\\u72AF\\u4EFB\\u4F55\\u7B2C\\u4E09\\u65B9\\u7684\\u6743\\u5229\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u8BE5\\u7528\\u6237\\u5185\\u5BB9\\u4E0D\\u5305\\u542B\\u4EFB\\u4F55\\u975E\\u6CD5\\u3001\\u6709\\u5BB3\\u3001\\u5A01\\u80C1\\u3001\\u8FB1\\u9A82\\u3001\\u9A9A\\u6270\\u3001\\u8BFD\\u8C24\\u3001\\u6DEB\\u79FD\\u6216\\u5176\\u4ED6\\u4EE4\\u4EBA\\u53CD\\u611F\\u7684\\u6750\\u6599\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u6388\\u4E88\\u67CF\\u5C14\\u5730\\u677F\\u4F7F\\u7528\\u3001\\u590D\\u5236\\u3001\\u4FEE\\u6539\\u3001\\u6539\\u7F16\\u3001\\u51FA\\u7248\\u3001\\u7FFB\\u8BD1\\u3001\\u521B\\u5EFA\\u884D\\u751F\\u4F5C\\u54C1\\u3001\\u5206\\u53D1\\u548C\\u5C55\\u793A\\u8BE5\\u7528\\u6237\\u5185\\u5BB9\\u7684\\u5168\\u7403\\u6027\\u3001\\u975E\\u6392\\u4ED6\\u6027\\u3001\\u514D\\u7248\\u7A0E\\u3001\\u53EF\\u8F6C\\u8BA9\\u7684\\u8BB8\\u53EF\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"5. \\u514D\\u8D23\\u58F0\\u660E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u672C\\u7F51\\u7AD9\\u53CA\\u5176\\u5185\\u5BB9\\u6309\\\"\\u539F\\u6837\\\"\\u548C\\\"\\u53EF\\u7528\\\"\\u7684\\u57FA\\u7840\\u63D0\\u4F9B\\uFF0C\\u4E0D\\u9644\\u5E26\\u4EFB\\u4F55\\u5F62\\u5F0F\\u7684\\u4FDD\\u8BC1\\uFF0C\\u65E0\\u8BBA\\u662F\\u660E\\u793A\\u7684\\u8FD8\\u662F\\u6697\\u793A\\u7684\\u3002\\u67CF\\u5C14\\u5730\\u677F\\u4E0D\\u4FDD\\u8BC1\\u672C\\u7F51\\u7AD9\\u5C06\\u65E0\\u9519\\u8BEF\\u6216\\u4E0D\\u95F4\\u65AD\\u8FD0\\u884C\\uFF0C\\u4E5F\\u4E0D\\u4FDD\\u8BC1\\u7F3A\\u9677\\u5C06\\u88AB\\u7EA0\\u6B63\\uFF0C\\u6216\\u8005\\u672C\\u7F51\\u7AD9\\u6216\\u63D0\\u4F9B\\u5B83\\u7684\\u670D\\u52A1\\u5668\\u6CA1\\u6709\\u75C5\\u6BD2\\u6216\\u5176\\u4ED6\\u6709\\u5BB3\\u6210\\u5206\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u67CF\\u5C14\\u5730\\u677F\\u4E0D\\u5BF9\\u4EFB\\u4F55\\u7528\\u6237\\u5185\\u5BB9\\u6216\\u4EFB\\u4F55\\u7B2C\\u4E09\\u65B9\\u7F51\\u7AD9\\u7684\\u5185\\u5BB9\\u3001\\u51C6\\u786E\\u6027\\u6216\\u53EF\\u9760\\u6027\\u4F5C\\u51FA\\u4EFB\\u4F55\\u4FDD\\u8BC1\\u6216\\u9648\\u8FF0\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"6. \\u8D23\\u4EFB\\u9650\\u5236\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5728\\u6CD5\\u5F8B\\u5141\\u8BB8\\u7684\\u6700\\u5927\\u8303\\u56F4\\u5185\\uFF0C\\u67CF\\u5C14\\u5730\\u677F\\u53CA\\u5176\\u8463\\u4E8B\\u3001\\u5458\\u5DE5\\u3001\\u4EE3\\u7406\\u4EBA\\u3001\\u4F9B\\u5E94\\u5546\\u6216\\u8BB8\\u53EF\\u65B9\\u5728\\u4EFB\\u4F55\\u60C5\\u51B5\\u4E0B\\u5747\\u4E0D\\u5BF9\\u56E0\\u4F7F\\u7528\\u6216\\u65E0\\u6CD5\\u4F7F\\u7528\\u672C\\u7F51\\u7AD9\\u6216\\u672C\\u7F51\\u7AD9\\u4E0A\\u7684\\u4EFB\\u4F55\\u5185\\u5BB9\\u800C\\u5BFC\\u81F4\\u7684\\u4EFB\\u4F55\\u76F4\\u63A5\\u3001\\u95F4\\u63A5\\u3001\\u7279\\u6B8A\\u3001\\u60E9\\u7F5A\\u6027\\u3001\\u9644\\u5E26\\u6216\\u540E\\u679C\\u6027\\u635F\\u5BB3\\u8D1F\\u8D23\\uFF0C\\u5305\\u62EC\\u4F46\\u4E0D\\u9650\\u4E8E\\u4E2A\\u4EBA\\u4F24\\u5BB3\\u3001\\u5229\\u6DA6\\u635F\\u5931\\u3001\\u6570\\u636E\\u635F\\u5931\\u3001\\u66FF\\u4EE3\\u4EA7\\u54C1\\u6216\\u670D\\u52A1\\u7684\\u91C7\\u8D2D\\u6210\\u672C\\uFF0C\\u5373\\u4F7F\\u67CF\\u5C14\\u5730\\u677F\\u5DF2\\u88AB\\u544A\\u77E5\\u6B64\\u7C7B\\u635F\\u5BB3\\u7684\\u53EF\\u80FD\\u6027\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"7. \\u8D54\\u507F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u540C\\u610F\\u8D54\\u507F\\u3001\\u4FDD\\u62A4\\u5E76\\u4F7F\\u67CF\\u5C14\\u5730\\u677F\\u53CA\\u5176\\u5B50\\u516C\\u53F8\\u3001\\u9644\\u5C5E\\u516C\\u53F8\\u3001\\u9AD8\\u7BA1\\u3001\\u8463\\u4E8B\\u3001\\u4EE3\\u7406\\u4EBA\\u3001\\u5458\\u5DE5\\u3001\\u5408\\u4F5C\\u4F19\\u4F34\\u548C\\u8BB8\\u53EF\\u65B9\\u514D\\u53D7\\u56E0\\u60A8\\u8FDD\\u53CD\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u3001\\u60A8\\u7684\\u7528\\u6237\\u5185\\u5BB9\\u6216\\u60A8\\u8FDD\\u53CD\\u4EFB\\u4F55\\u6CD5\\u5F8B\\u6216\\u7B2C\\u4E09\\u65B9\\u6743\\u5229\\u800C\\u4EA7\\u751F\\u7684\\u4EFB\\u4F55\\u7D22\\u8D54\\u3001\\u8D23\\u4EFB\\u3001\\u635F\\u5BB3\\u3001\\u5224\\u51B3\\u3001\\u88C1\\u51B3\\u3001\\u635F\\u5931\\u3001\\u6210\\u672C\\u3001\\u8D39\\u7528\\u6216\\u503A\\u52A1\\uFF08\\u5305\\u62EC\\u5408\\u7406\\u7684\\u5F8B\\u5E08\\u8D39\\uFF09\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"8. \\u9002\\u7528\\u6CD5\\u5F8B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u53D7\\u4E2D\\u534E\\u4EBA\\u6C11\\u5171\\u548C\\u56FD\\u6CD5\\u5F8B\\u7BA1\\u8F96\\uFF0C\\u4E0D\\u8003\\u8651\\u6CD5\\u5F8B\\u51B2\\u7A81\\u539F\\u5219\\u3002\\u4E0E\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u6709\\u5173\\u7684\\u4EFB\\u4F55\\u4E89\\u8BAE\\u5E94\\u63D0\\u4EA4\\u7ED9\\u4E2D\\u56FD\\u676D\\u5DDE\\u5E02\\u6709\\u7BA1\\u8F96\\u6743\\u7684\\u6CD5\\u9662\\u89E3\\u51B3\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"9. \\u6761\\u6B3E\\u4FEE\\u6539\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u67CF\\u5C14\\u5730\\u677F\\u4FDD\\u7559\\u968F\\u65F6\\u4FEE\\u6539\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u7684\\u6743\\u5229\\u3002\\u4FEE\\u6539\\u540E\\u7684\\u6761\\u6B3E\\u5C06\\u5728\\u672C\\u7F51\\u7AD9\\u4E0A\\u53D1\\u5E03\\u65F6\\u751F\\u6548\\u3002\\u60A8\\u7EE7\\u7EED\\u4F7F\\u7528\\u672C\\u7F51\\u7AD9\\u5C06\\u88AB\\u89C6\\u4E3A\\u63A5\\u53D7\\u4FEE\\u6539\\u540E\\u7684\\u6761\\u6B3E\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"10. \\u8054\\u7CFB\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5982\\u679C\\u60A8\\u5BF9\\u8FD9\\u4E9B\\u4F7F\\u7528\\u6761\\u6B3E\\u6709\\u4EFB\\u4F55\\u7591\\u95EE\\uFF0C\\u8BF7\\u8054\\u7CFB\\u6211\\u4EEC\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u67CF\\u5C14\\u6728\\u4E1A\\u6709\\u9650\\u516C\\u53F8\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 22\n          }, this), \"\\u5730\\u5740\\uFF1A\\u6D59\\u6C5F\\u7701\\u6E56\\u5DDE\\u5E02\\u5357\\u6D54\\u533A\\u4E1C\\u9A6C\\u8DEF866\\u53F7\\u6D59\\u6C5F\\u67CF\\u5C14\\u6728\\u4E1A\\u6709\\u9650\\u516C\\u53F8\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 40\n          }, this), \"\\u7535\\u8BDD\\uFF1A400-711-3636\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"policy-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6700\\u540E\\u66F4\\u65B0\\u65E5\\u671F\\uFF1A2025\\u5E744\\u670830\\u65E5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"return-button\",\n          onClick: () => setCurrentPage('main'),\n          children: \"\\u8FD4\\u56DE\\u4E3B\\u9875\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = TermsOfUse;\nexport default TermsOfUse;\nvar _c;\n$RefreshReg$(_c, \"TermsOfUse\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TermsOfUse", "setCurrentPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/TermsOfUse.js"], "sourcesContent": ["import React from 'react';\nimport './PolicyPages.css';\n\nfunction TermsOfUse({ setCurrentPage }) {\n  return (\n    <div className=\"policy-container\">\n      <div className=\"policy-content\">\n        <h1>使用条款</h1>\n\n        <section>\n          <h2>1. 接受条款</h2>\n          <p>欢迎访问柏尔木业有限公司（以下简称\"柏尔地板\"、\"我们\"或\"我们的\"）的网站。通过访问和使用本网站，您同意受这些使用条款的约束。如果您不同意这些条款，请不要使用本网站。</p>\n        </section>\n\n        <section>\n          <h2>2. 使用许可</h2>\n          <p>在您遵守这些使用条款的前提下，柏尔地板授予您访问和个人使用本网站的有限、非排他性、不可转让的许可。您不得：</p>\n          <ul>\n            {/* <li>将本网站用于任何商业目的</li> */}\n            <li>修改、复制、分发、传输、展示、执行、复制、出版、许可、创建衍生作品、转让或销售本网站上的任何信息、软件、产品或服务</li>\n            <li>使用本网站进行任何非法活动或可能损害他人权利的活动</li>\n            <li>干扰或破坏本网站的安全功能</li>\n          </ul>\n        </section>\n\n        <section>\n          <h2>3. 知识产权</h2>\n          <p>本网站及其所有内容、功能和设计元素，包括但不限于文本、图形、徽标、图标、图像、音频剪辑、下载、数据编译、软件和代码，均为柏尔地板或其许可方的财产，受中国和国际版权、商标、专利和其他知识产权法律的保护。</p>\n          <p>未经柏尔地板明确书面许可，不得复制、修改、创建衍生作品、公开展示、公开执行、重新发布、下载、存储或传输本网站的任何材料。</p>\n        </section>\n\n        <section>\n          <h2>4. 用户内容</h2>\n          <p>如果您向本网站提交、上传、发布或传输任何内容（\"用户内容\"），您保证：</p>\n          <ul>\n            <li>您拥有或有权使用和授权我们使用该用户内容</li>\n            <li>该用户内容不侵犯任何第三方的权利</li>\n            <li>该用户内容不包含任何非法、有害、威胁、辱骂、骚扰、诽谤、淫秽或其他令人反感的材料</li>\n          </ul>\n          <p>您授予柏尔地板使用、复制、修改、改编、出版、翻译、创建衍生作品、分发和展示该用户内容的全球性、非排他性、免版税、可转让的许可。</p>\n        </section>\n\n        <section>\n          <h2>5. 免责声明</h2>\n          <p>本网站及其内容按\"原样\"和\"可用\"的基础提供，不附带任何形式的保证，无论是明示的还是暗示的。柏尔地板不保证本网站将无错误或不间断运行，也不保证缺陷将被纠正，或者本网站或提供它的服务器没有病毒或其他有害成分。</p>\n          <p>柏尔地板不对任何用户内容或任何第三方网站的内容、准确性或可靠性作出任何保证或陈述。</p>\n        </section>\n\n        <section>\n          <h2>6. 责任限制</h2>\n          <p>在法律允许的最大范围内，柏尔地板及其董事、员工、代理人、供应商或许可方在任何情况下均不对因使用或无法使用本网站或本网站上的任何内容而导致的任何直接、间接、特殊、惩罚性、附带或后果性损害负责，包括但不限于个人伤害、利润损失、数据损失、替代产品或服务的采购成本，即使柏尔地板已被告知此类损害的可能性。</p>\n        </section>\n\n        <section>\n          <h2>7. 赔偿</h2>\n          <p>您同意赔偿、保护并使柏尔地板及其子公司、附属公司、高管、董事、代理人、员工、合作伙伴和许可方免受因您违反这些使用条款、您的用户内容或您违反任何法律或第三方权利而产生的任何索赔、责任、损害、判决、裁决、损失、成本、费用或债务（包括合理的律师费）。</p>\n        </section>\n\n        <section>\n          <h2>8. 适用法律</h2>\n          <p>这些使用条款受中华人民共和国法律管辖，不考虑法律冲突原则。与这些使用条款有关的任何争议应提交给中国杭州市有管辖权的法院解决。</p>\n        </section>\n\n        <section>\n          <h2>9. 条款修改</h2>\n          <p>柏尔地板保留随时修改这些使用条款的权利。修改后的条款将在本网站上发布时生效。您继续使用本网站将被视为接受修改后的条款。</p>\n        </section>\n\n        <section>\n          <h2>10. 联系信息</h2>\n          <p>如果您对这些使用条款有任何疑问，请联系我们：</p>\n          <p>柏尔木业有限公司<br />\n          地址：浙江省湖州市南浔区东马路866号浙江柏尔木业有限公司<br />\n          电话：400-711-3636</p>\n        </section>\n\n        <div className=\"policy-footer\">\n          <p>最后更新日期：2025年4月30日</p>\n          <button className=\"return-button\" onClick={() => setCurrentPage('main')}>\n            返回主页\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default TermsOfUse;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,SAASC,UAAUA,CAAC;EAAEC;AAAe,CAAC,EAAE;EACtC,oBACEF,OAAA;IAAKG,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/BJ,OAAA;MAAKG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BJ,OAAA;QAAAI,QAAA,EAAI;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEbR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAAoF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAAqD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5DR,OAAA;UAAAI,QAAA,gBAEEJ,OAAA;YAAAI,QAAA,EAAI;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClER,OAAA;YAAAI,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCR,OAAA;YAAAI,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAAoG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3GR,OAAA;UAAAI,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1CR,OAAA;UAAAI,QAAA,gBACEJ,OAAA;YAAAI,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BR,OAAA;YAAAI,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBR,OAAA;YAAAI,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACLR,OAAA;UAAAI,QAAA,EAAG;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAAuG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9GR,OAAA;UAAAI,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAAoJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpJ,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdR,OAAA;UAAAI,QAAA,EAAG;QAA0H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1H,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBR,OAAA;UAAAI,QAAA,EAAG;QAA2D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEVR,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAAI,QAAA,EAAI;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjBR,OAAA;UAAAI,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7BR,OAAA;UAAAI,QAAA,GAAG,kDAAQ,eAAAJ,OAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,mKACY,eAAAR,OAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kCACpB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEVR,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BJ,OAAA;UAAAI,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxBR,OAAA;UAAQG,SAAS,EAAC,eAAe;UAACM,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,MAAM,CAAE;UAAAE,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACE,EAAA,GAlFQT,UAAU;AAoFnB,eAAeA,UAAU;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}