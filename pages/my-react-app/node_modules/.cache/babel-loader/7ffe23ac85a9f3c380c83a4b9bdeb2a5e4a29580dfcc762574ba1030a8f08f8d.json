{"ast": null, "code": "export const woodImages = [\n// {\n//     \"id\": 1,\n//     \"src\": require('../images/wood/1-雨树.jpg'),\n//     \"name\": \"雨树\",\n//     price: 100,\n//     desc: {\n//         img: require('../images/desc/雨树_desc.png'),\n//         scientificName: '<PERSON><PERSON><PERSON> (Jacq.) Merr.',\n//         origin: '热带美洲，广植于全世界热带地区',\n//         woodProperty: '心材褐色，耐腐、轻软、切面平滑有光泽，干燥后不翘不裂',\n//         purpose: '家具、雕刻、门、窗、箱板等用材'\n//     }\n// },\n{\n  \"id\": 2,\n  \"src\": require(\"../images/wood/2-黑核桃.jpg\"),\n  \"name\": \"黑核桃\",\n  price: 893,\n  desc: {\n    img: require('../images/desc/黑核桃_desc.jpeg'),\n    scientificName: 'BlackWalnut',\n    origin: '亚洲东部至地中海地区﹐同时亦分布在北美、中美洲及安第斯山脉一带',\n    woodProperty: '胡桃木是密度中等的结实的硬木，材质坚硬，细腻均匀，抗弯曲及抗压度中等，不易开裂；抗菌、白蚁的危害、耐腐蚀',\n    purpose: '易于用手工和机械工具加工。适于敲钉、螺钻和胶合'\n  }\n},\n// {\n//     \"id\": 3,\n//     \"src\": require(\"../images/wood/3-格木.jpg\"),\n//     \"name\": \"格木\",\n//     price: 100,\n//     desc: {\n//         img: require('../images/desc/格木_desc.jpeg'),\n//         scientificName: 'Erythrophleum fordii Oliv',\n//         origin: '非洲',\n//         woodProperty: '格木为珍贵的硬材树种，木材坚硬，被称为铁木，极耐腐',\n//         purpose: '为优良的建筑、工艺及家具用材，耐水湿，可供船板、桅插和上等家具等用材'\n//     }\n// },\n{\n  \"id\": 4,\n  \"src\": require(\"../images/wood/4-阔变豆.jpg\"),\n  \"name\": \"阔变豆\",\n  price: 593,\n  desc: {\n    img: require('../images/desc/阔变豆_desc.jpg'),\n    scientificName: 'platymis cium sp',\n    origin: '巴西、圭亚那、哥伦比亚等地',\n    woodProperty: '材质重，强度高，纹理清晰',\n    purpose: '高档家具、地板、乐器小提琴等'\n  }\n}, {\n  \"id\": 5,\n  \"src\": require(\"../images/wood/5-橡木.jpg\"),\n  \"name\": \"橡木\",\n  price: 527,\n  desc: {\n    img: require('../images/desc/橡木_desc.jpg'),\n    scientificName: 'Quercus spp',\n    origin: '欧洲',\n    woodProperty: '质地紧致，手感细腻舒适；纹理顺美，色泽温婉雅致。型材力学强度更高，结构牢固，经久耐用不变形',\n    purpose: '主要用于木门窗、地板、高档装修、高档家具等'\n  }\n}, {\n  \"id\": 6,\n  \"src\": require(\"../images/wood/6-大果紫檀.jpg\"),\n  \"name\": \"大果紫檀\",\n  price: 1343,\n  desc: {\n    img: require('../images/desc/大果紫檀_desc.jpg'),\n    scientificName: 'Padauk',\n    origin: '缅甸、老挝、泰国等地',\n    woodProperty: '密度高，材质重，耐腐性强，具有清香，纹理清晰美观',\n    purpose: '高档家具、地板、汽车装饰等'\n  }\n}, {\n  \"id\": 7,\n  \"src\": require(\"../images/wood/7-阔叶黄檀.jpg\"),\n  \"name\": \"阔叶黄檀\",\n  price: 1760,\n  desc: {\n    img: require('../images/desc/阔叶黄檀_desc.jpeg'),\n    scientificName: 'Dalbergia latifolia',\n    origin: '印度、印度尼西亚等地',\n    woodProperty: '密度高，材质重，耐腐蚀、纹理清晰美观',\n    purpose: '高档红木家具、地板、钢琴等'\n  }\n}, {\n  \"id\": 8,\n  \"src\": require(\"../images/wood/8-军刀豆.jpg\"),\n  \"name\": \"军刀豆\",\n  price: 1427,\n  desc: {\n    img: require('../images/desc/军刀豆_desc.jpg'),\n    scientificName: 'parinari campestris',\n    origin: '印度、印度尼西亚等地',\n    woodProperty: '经济价值高，木材耐腐，能抗海生动物危害，木材加工性能好，具油性感，油漆上蜡性好',\n    purpose: '家具制作用材'\n  }\n}, {\n  \"id\": 9,\n  \"src\": require(\"../images/wood/9-鸡翅木.jpg\"),\n  \"name\": \"鸡翅木\",\n  price: 627,\n  desc: {\n    img: require('../images/desc/鸡翅木_desc.jpg'),\n    scientificName: 'Millettia Laurentii',\n    origin: '非洲',\n    woodProperty: '质较硬，较平滑，易条状剥离。具光泽；有油性感。纹理直；结构粗而不均匀；质重硬；强度高；干缩甚大。加工略难，易钝锯；抛光略难；钉钉须先打孔；弯曲性能极佳。很耐腐',\n    purpose: '适用于高级家具、刨切微薄木、室内装修、地板、细木工、运动器材、雕刻等'\n  }\n}, {\n  \"id\": 10,\n  \"src\": require(\"../images/wood/10-爱里古夷苏木.jpg\"),\n  \"name\": \"爱里古夷苏木\",\n  price: 760,\n  desc: {\n    img: require('../images/desc/爱里古夷苏木_desc.jpeg'),\n    scientificName: 'Newtonia spp. I.alba',\n    origin: '喀麦隆、卢旺达、肯尼亚等',\n    woodProperty: '纹理交错，结甚细而匀；木材重至甚重；干缩中至大，强度高',\n    purpose: '用于建筑、室内装修，家具，单板，胶合板，车辆，造船，包装箱，枕木等'\n  }\n},\n// {\n//     \"id\": 11,\n//     \"src\": require(\"../images/wood/11-油楠木.jpg\"),\n//     \"name\": \"油楠木\",\n//     price: 100,\n//     desc: {\n//         img: require('../images/desc/油楠木_desc.jpeg'),\n//         scientificName: 'Sindora glabra Merr. ex de Wit',\n//         origin: '马来西亚、东南亚、海南',\n//         woodProperty: '木材结构略粗，木肌细致，光泽强，极具油性;伸缩性不大，耐久稳定，易加工，可塑性强，心材耐腐蚀',\n//         purpose: '在古筝、古琴的制作中，油楠木为主材的乐器是最高品质的，还是家具、地板的用材'\n//     }\n// },\n{\n  \"id\": 12,\n  \"src\": require(\"../images/wood/12-印茄木.jpg\"),\n  \"name\": \"印茄木\",\n  price: 527,\n  desc: {\n    img: require('../images/desc/印茄木_desc.jpeg'),\n    scientificName: 'Intsia.spp',\n    origin: '东南亚',\n    woodProperty: '结构粗；质量硬；强度高；干缩甚小。耐腐、干燥慢，但稳定性很好',\n    purpose: '常被用于制作门窗、高级家具室内装饰、门窗柜、车船桥梁、地板，是户外家具、户外地板、扶手楼梯、亭台楼阁和木质建筑的重要用材树种'\n  }\n}, {\n  \"id\": 13,\n  \"src\": require(\"../images/wood/13-柚木.jpg\"),\n  \"name\": \"柚木\",\n  price: 843,\n  desc: {\n    img: require('../images/desc/柚木_desc.jpeg'),\n    scientificName: 'Teak',\n    origin: '缅甸、印尼等地',\n    woodProperty: '油性强，耐腐耐磨、稳定性极强',\n    purpose: '地板、游艇甲板等'\n  }\n}, {\n  \"id\": 14,\n  \"src\": require(\"../images/wood/14-鲍迪豆.jpg\"),\n  \"name\": \"鲍迪豆\",\n  price: 527,\n  desc: {\n    img: require('../images/desc/鲍迪豆_desc.jpg'),\n    scientificName: 'Bowdichia spp.',\n    origin: '南美洲北部',\n    woodProperty: '鲍迪豆的木材为散孔材，心材红褐色至巧克力色，具有深浅相间的带状条纹，与边材界限明显，边材窄且为白色',\n    purpose: '由于鲍迪豆的木材重且硬，加工困难，但其耐腐、耐虫害，常用于制作高级家具、地板等'\n  }\n}\n// {\n//     \"id\": 16,\n//     \"src\": require(\"../images/wood/16-香脂木豆.jpg\"),\n//     \"name\": \"香脂木豆\",\n//     price: 100,\n//     desc: {\n//         img: require('../images/desc/香脂木豆_desc.png'),\n//         scientificName: 'Red Incienso',\n//         origin: '于巴西、秘鲁、委内瑞拉、阿根廷等地',\n//         woodProperty: '纹理交错、重硬坚韧、芳香四溢，木材甚稳定，花纹美观，耐久、耐腐耐磨',\n//         purpose: '适用于建筑、地板、家具、装饰单板、胶合板、车辆、造船、矿柱、枕木、电杆、农具、工具柄、雕刻、车旋制品等'\n//     }\n// }\n];\nexport const empty = {\n  \"path\": \"empty\",\n  \"clipPath\": []\n};\nexport const tang = [{\n  \"path\": \"tang/1\",\n  \"bg\": require('../images/bg/tang/1/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 50%, 100% 0)\", \"polygon(100% 0, 50% 50%, 100% 100%)\", \"polygon(100% 100%, 50% 50%, 0 100%)\", \"polygon(0 100%, 50% 50%, 0 0)\", \"polygon(50% 50%,60.55% 39.45%,100% 78.9%,100% 100%)\", \"polygon(0 0,21.1% 0,60.55% 39.45%,50% 50%)\", \"polygon(0 0,50% 50%,39.45% 60.55%,0 21.1%)\", \"polygon(50% 50%,100% 100%,78.9% 100%,39.45% 60.55%)\", \"polygon(0 78.9%,0 100%,100% 0,78.9% 0)\", \"polygon(0 100%,21.1% 100%,100% 21.1%,100% 0)\"],\n  \"area\": [0.02984, 0.02984, 0.02984, 0.02984, 0.02600, 0.02600, 0.02600, 0.02600, 0.07000, 0.07000],\n  \"profit\": 300\n}, {\n  \"path\": \"tang/2\",\n  \"bg\": require('../images/bg/tang/2/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 20% 0, 50% 30%, 50% 50%)\", \"polygon(0 0, 0 20%, 30% 50%, 50% 50%)\", \"polygon(100% 0, 80% 0, 50% 30%, 50% 50%)\", \"polygon(100% 0, 100% 20%, 50% 70%, 50% 50%)\", \"polygon(0 100%, 20% 100%, 50% 70%, 50% 50%)\", \"polygon(0 100%, 0 80%, 30% 50%, 50% 50%)\", \"polygon(100% 100%, 80% 100%, 50% 70%, 50% 50%)\", \"polygon(100% 100%, 100% 80%, 70% 50%, 50% 50%)\", \"polygon(20% 0, 35% 15%, 65% 15%, 80% 0)\", \"polygon(0 20%, 15% 35%, 15% 65%, 0 80%)\", \"polygon(20% 100%, 35% 85%, 65% 85%, 80% 100%)\", \"polygon(100% 20%, 85% 35%, 85% 65%, 100% 80%)\", \"polygon(35% 15%, 65% 15%, 50% 30%)\", \"polygon(85% 35%, 85% 65%, 70% 50%)\", \"polygon(35% 85%, 65% 85%, 50% 70%)\", \"polygon(15% 35%, 15% 65%, 30% 50%)\"],\n  \"area\": [0.0288, 0.0288, 0.0288, 0.0288, 0.0288, 0.0288, 0.0288, 0.0288, 0.0245, 0.0245, 0.0245, 0.0245, 0.0080, 0.0080, 0.0080, 0.0080],\n  \"profit\": 300\n}, {\n  \"path\": \"tang/3\",\n  \"bg\": require('../images/bg/tang/3/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 15% 15%, 65% 15%, 80% 0)\", \"polygon(100% 20%, 85% 35%, 85% 85%, 100% 100%)\", \"polygon(100% 100%, 85% 85%, 35% 85%, 20% 100%)\", \"polygon(0 0, 15% 15%, 15% 65%, 0 80%)\", \"polygon(15% 15%, 35% 15%, 85% 65%, 85% 85%)\", \"polygon(15% 15%, 15% 35%, 65% 85%, 85% 85%)\", \"polygon(100% 0, 80% 0, 50% 30%, 60% 40%)\", \"polygon(100% 0, 100% 20%, 70% 50%, 60% 40%)\", \"polygon(0 100%, 20% 100%, 50% 70%, 40% 60%)\", \"polygon(0 100%, 0 80%, 30% 50%, 40% 60%)\", \"polygon(35% 15%, 65% 15%, 50% 30%)\", \"polygon(15% 35%, 15% 65%, 30% 50%)\", \"polygon(35% 85%, 65% 85%, 50% 70%)\", \"polygon(85% 35%, 85% 65%, 70% 50%)\"],\n  \"area\": [0.0358, 0.0358, 0.0358, 0.0358, 0.0415, 0.0415, 0.0246, 0.0246, 0.0246, 0.0246, 0.0088, 0.0088, 0.0088, 0.0088],\n  \"profit\": 300\n}, {\n  \"path\": \"tang/8\",\n  \"bg\": require('../images/bg/tang/8/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 100% 100%, 50% 50%)\", \"polygon(0 0, 50% 50%, 0 100%)\", \"polygon(100% 0, 50% 50%, 100% 100%)\", \"polygon(34.4% 15.4%, 50% 31%, 65.6% 15.4%)\", \"polygon(15.4% 34.4%, 31% 50%, 15.4% 65.6%)\", \"polygon(34.4% 84.6%, 50% 69%, 65.6% 84.6%)\", \"polygon(84.6% 34.4%, 69% 50%, 84.6% 65.6%)\", \"polygon(15.4% 15.4%, 34.4% 15.4%, 50% 31%, 50% 50%)\", \"polygon(84.6% 84.6%, 65.6% 84.6%, 50% 69%, 50% 50%)\", \"polygon(84.6% 15.4%, 84.6% 34.4%, 69% 50%, 50% 50%)\", \"polygon(15.4% 84.6%, 15.4% 65.6%, 31% 50%, 50% 50%)\", \"polygon(84.6% 15.4%, 65.6% 15.4%, 50% 31%, 50% 50%)\", \"polygon(15.4% 84.6%, 34.4% 84.6%, 50% 69%, 50% 50%)\", \"polygon(84.6% 84.6%, 84.6% 65.6%, 69% 50%, 50% 50%)\", \"polygon(15.4% 15.4%, 15.4% 34.4%, 31% 50%, 50% 50%)\"],\n  \"area\": [0.0469, 0.0469, 0.0469, 0.0469, 0.0087, 0.0087, 0.0087, 0.0087, 0.0172, 0.0172, 0.0172, 0.0172, 0.0172, 0.0172, 0.0172, 0.0172],\n  \"profit\": 300\n}, {\n  \"path\": \"tang/10\",\n  \"bg\": require('../images/bg/tang/10/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 100% 100%, 50% 50%)\", \"polygon(0 0, 50% 50%, 0 100%)\", \"polygon(100% 0, 50% 50%, 100% 100%)\", \"polygon(34.6% 15.1%, 84.9% 65.4%, 84.9% 15.1%)\", \"polygon(15.1% 34.6%, 65.4% 84.9%, 15.1% 84.9%)\", \"polygon(15.1% 15.1%, 34.6% 15.1%, 84.9% 65.4%, 84.9% 84.9%, 65.4% 84.9%, 15.1% 34.6%)\"],\n  \"area\": [0.0451, 0.0451, 0.0451, 0.0451, 0.0476, 0.0476, 0.0854],\n  \"profit\": 300\n}, {\n  \"path\": \"tang/15\",\n  \"bg\": require('../images/bg/tang/15/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 100% 100%, 50% 50%)\", \"polygon(0 0, 50% 50%, 0 100%)\", \"polygon(100% 0, 50% 50%, 100% 100%)\", \"polygon(14.14% 14.14%, 50% 14.14%, 50% 50%, 14.14% 50%)\", \"polygon(50% 14.14%, 85.86% 14.14%, 85.86% 50%, 50% 50%)\", \"polygon(85.86% 50%, 85.86% 85.86%, 50% 85.86%, 50% 50%)\", \"polygon(14.14% 50%, 50% 50%, 50% 85.86%, 14.14% 85.86%)\"],\n  \"area\": [0.0324, 0.0324, 0.0324, 0.0324, 0.0580, 0.0580, 0.0580, 0.0580],\n  \"profit\": 300\n}, {\n  \"path\": \"tang/26\",\n  \"bg\": require('../images/bg/tang/26/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 100% 100%, 50% 50%)\", \"polygon(0 0, 50% 50%, 0 100%)\", \"polygon(100% 0, 50% 50%, 100% 100%)\", \"polygon(0 0, 17.36% 0, 50% 32.64%, 50% 50%, 32.64% 50%, 0% 17.36%)\", \"polygon(82.64% 0, 100% 0, 100% 17.36%, 67.36% 50%, 50% 50%, 50% 32.64%)\", \"polygon(0 100%, 17.36% 100%, 50% 67.36%, 50% 50%, 32.64% 50%, 0 82.64%)\", \"polygon(100% 100%, 100% 82.64%, 67.36% 50%, 50% 50%, 50% 67.36%, 82.64% 100%)\"],\n  \"area\": [0.0509, 0.0509, 0.0509, 0.0509, 0.0391, 0.0391, 0.0391, 0.0391],\n  \"profit\": 300\n}];\nexport const song = [{\n  \"path\": \"song/4\",\n  \"bg\": require('../images/bg/song/4/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(0 0, 0 50%, 50% 50%)\", \"polygon(100% 0, 50% 0, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(13% 0, 100% 0, 100% 13%, 13% 13%)\", \"polygon(87% 13%, 100% 13%, 100% 100%, 87% 100%)\", \"polygon(0 87%, 87% 87%, 87% 100%, 0 100%)\", \"polygon(0 0, 13% 0, 13% 87%, 0 87%)\", \"polygon(42.5% 13%, 57.5% 13%, 57.5% 87%, 42.5% 87%)\", \"polygon(42.5% 87%, 57.5% 87%, 57.5% 50%, 42.5% 50%)\", \"polygon(13% 42.5%, 13% 57.5%, 50% 57.5%, 50% 42.5%)\", \"polygon(87% 42.5%, 87% 57.5%, 50% 57.5%, 50% 42.5%)\", \"polygon(13% 13%, 23% 13%, 50% 40%, 50% 50%, 40% 50%, 13% 23%)\", \"polygon(13% 87%, 23% 87%, 50% 60%, 50% 50%, 40% 50%, 13% 77%)\", \"polygon(87% 13%, 77% 13%, 50% 40%, 50% 50%, 60% 50%, 87% 23%)\", \"polygon(87% 87%, 77% 87%, 50% 60%, 50% 50%, 60% 50%, 87% 77%)\"],\n  \"area\": [0.0069, 0.0069, 0.0069, 0.0069, 0.0069, 0.0069, 0.0069, 0.0069, 0.0405, 0.0405, 0.0405, 0.0405, 0.0121, 0.0121, 0.0121, 0.0121, 0.0237, 0.0237, 0.0237, 0.0237],\n  \"profit\": 350\n}, {\n  \"path\": \"song/5\",\n  \"bg\": require('../images/bg/song/5/bg.png'),\n  \"clipPath\": [\"polygon(0 0,13% 13%, 87% 13%,100% 0)\", \"polygon(0 0,13% 13%, 13% 87%,0 100%)\", \"polygon(0 100%,13% 87%, 87% 87%,100% 100%)\", \"polygon(100% 0,87% 13%, 87% 87%,100% 100%)\", \"polygon(13% 13%, 50% 13%, 50% 50%)\", \"polygon(13% 13%, 13% 50%, 50% 50%)\", \"polygon(87% 13%, 50% 13%, 50% 50%)\", \"polygon(87% 13%, 87% 50%, 50% 50%)\", \"polygon(13% 87%, 50% 87%, 50% 50%)\", \"polygon(13% 87%, 13% 50%, 50% 50%)\", \"polygon(87% 87%, 50% 87%, 50% 50%)\", \"polygon(87% 87%, 87% 50%, 50% 50%)\", \"polygon(0 0, 15.5% 0, 39% 23.5%, 39% 39%)\", \"polygon(0 0, 0 15.5%, 23.5% 39%, 39% 39%)\", \"polygon(100% 0, 84.5% 0, 61% 23.5%, 61% 39%)\", \"polygon(100% 0, 100% 15.5%, 76.5% 39%, 61% 39%)\", \"polygon(0 100%, 15.5% 100%, 39% 76.5%, 39% 61%)\", \"polygon(0 100%, 0 84.5%, 23.5% 61%, 39% 61%)\", \"polygon(100% 100%, 84.5% 100%, 61% 76.5%, 61% 61%)\", \"polygon(100% 100%, 100% 84.5%, 76.5% 61%, 61% 61%)\", \"polygon(39% 13%, 50% 13%, 50% 50%, 39% 39%)\", \"polygon(61% 13%, 50% 13%, 50% 50%, 61% 39%)\", \"polygon(39% 87%, 50% 87%, 50% 50%, 39% 61%)\", \"polygon(61% 87%, 50% 87%, 50% 50%, 61% 61%)\", \"polygon(87% 39%, 87% 50%, 50% 50%, 61% 39%)\", \"polygon(87% 61%, 87% 50%, 50% 50%, 61% 61%)\", \"polygon(13% 39%, 13% 50%, 50% 50%, 39% 39%)\", \"polygon(13% 61%, 13% 50%, 50% 50%, 39% 61%)\"],\n  \"area\": [0.0255, 0.0255, 0.0255, 0.0255, 0.0022, 0.0022, 0.0022, 0.0022, 0.0022, 0.0022, 0.0022, 0.0022, 0.0175, 0.0175, 0.0175, 0.0175, 0.0175, 0.0175, 0.0175, 0.0175, 0.0126, 0.0126, 0.0126, 0.0126, 0.0126, 0.0126, 0.0126, 0.0126],\n  \"profit\": 350\n}, {\n  \"path\": \"song/9\",\n  \"bg\": require('../images/bg/song/9/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 93% 7%, 7% 7%)\", \"polygon(0 100%, 100% 100%, 93% 93%, 7% 93%)\", \"polygon(0 0, 0 100%, 7% 93%, 7% 7%)\", \"polygon(100% 0, 100% 100%, 93% 93%, 93% 7%)\", \"polygon(18.8% 7%, 38.2% 7%, 28.5% 16.7%)\", \"polygon(7% 18.8%, 7% 38.2%, 16.7% 28.5%)\", \"polygon(7% 81.2%, 7% 61.8%, 16.7% 71.5%)\", \"polygon(18.8% 93%, 38.2% 93%, 28.5% 83.3%)\", \"polygon(81.2% 93%, 61.8% 93%, 71.5% 83.3%)\", \"polygon(93% 81.2%, 93% 61.8%, 83.3% 71.5%)\", \"polygon(93% 18.8%, 93% 38.2%, 83.3% 28.5%)\", \"polygon(81.2% 7%, 61.8% 7%, 71.5% 16.7%)\", \"polygon(50% 18.8%, 40.3% 28.5%, 50% 38.2%, 59.7% 28.5%)\", \"polygon(18.8% 50%, 28.5% 40.3%, 38.2% 50%, 28.5% 59.7%)\", \"polygon(50% 81.2%, 40.3% 71.5%, 50% 61.8%, 59.7% 71.5%)\", \"polygon(81.2% 50%, 71.5% 40.3%, 61.8% 50%, 71.5% 59.7%)\", \"polygon(38.2% 7%, 50% 7%, 28.5% 28.5%, 28.5% 16.7%)\", \"polygon(50% 7%, 50% 18.8%, 40.3% 28.5%, 28.5% 28.5%)\", \"polygon(61.8% 7%, 50% 7%, 71.5% 28.5%, 71.5% 16.7%)\", \"polygon(50% 7%, 50% 18.8%, 59.7% 28.5%, 71.5% 28.5%)\", \"polygon(7% 38.2%, 7% 50%, 28.5% 28.5%, 16.7% 28.5%)\", \"polygon(7% 50%, 18.8% 50%, 28.5% 40.3%, 28.5% 28.5%)\", \"polygon(7% 61.8%, 7% 50%, 28.5% 71.5%, 16.7% 71.5%)\", \"polygon(7% 50%, 18.8% 50%, 28.5% 59.7%, 28.5% 71.5%)\", \"polygon(38.2% 93%, 50% 93%, 28.5% 71.5%, 28.5% 83.3%)\", \"polygon(50% 93%, 50% 81.2%, 40.3% 71.5%, 28.5% 71.5%)\", \"polygon(61.8% 93%, 50% 93%, 71.5% 71.5%, 71.5% 83.3%)\", \"polygon(50% 93%, 50% 81.2%, 59.7% 71.5%, 71.5% 71.5%)\", \"polygon(93% 38.2%, 93% 50%, 71.5% 28.5%, 83.3% 28.5%)\", \"polygon(93% 50%, 81.2% 50%, 71.5% 40.3%, 71.5% 28.5%)\", \"polygon(93% 61.8%, 93% 50%, 71.5% 71.5%, 83.3% 71.5%)\", \"polygon(93% 50%, 81.2% 50%, 71.5% 59.7%, 71.5% 71.5%)\", \"polygon(7% 7%, 18.8% 7%, 50% 38.2%, 50% 50%)\", \"polygon(7% 7%, 7% 18.8%, 38.2% 50%, 50% 50%)\", \"polygon(93% 7%, 81.2% 7%, 50% 38.2%, 50% 50%)\", \"polygon(93% 7%, 93% 18.8%, 61.8% 50%, 50% 50%)\", \"polygon(7% 93%, 18.8% 93%, 50% 61.8%, 50% 50%)\", \"polygon(7% 93%, 7% 81.2%, 38.2% 50%, 50% 50%)\", \"polygon(93% 93%, 81.2% 93%, 50% 61.8%, 50% 50%)\", \"polygon(93% 93%, 93% 81.2%, 61.8% 50%, 50% 50%)\"],\n  \"area\": [0.0234, 0.0234, 0.0234, 0.0234, 0.0034, 0.0034, 0.0034, 0.0034, 0.0034, 0.0034, 0.0034, 0.0034, 0.0069, 0.0069, 0.0069, 0.0069, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0053, 0.0160, 0.0160, 0.0160, 0.0160, 0.0160, 0.0160, 0.0160, 0.0160],\n  \"profit\": 350\n}, {\n  \"path\": \"song/14\",\n  \"bg\": require('../images/bg/song/14/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 0, 0 100%, 50% 50%)\", \"polygon(100% 0, 100% 100%, 50% 50%)\", \"polygon(0 100%, 100% 100%, 50% 50%)\", \"polygon(0 0, 36.47% 0, 46.95% 10.47%, 10.47% 10.47%)\", \"polygon(0 0, 10.47% 10.47%, 10.47% 46.95%, 0 36.47%)\", \"polygon(100% 100%, 100% 63.53%, 89.53% 53.05%, 89.53% 89.53%)\", \"polygon(100% 100%, 89.53% 89.53%, 53.05% 89.53%, 63.53% 100%)\", \"polygon(46.95% 10.47%, 10.47% 10.47%, 89.53% 89.53%, 89.53% 53.05%)\", \"polygon(10.47% 10.47%, 10.47% 46.95%, 53.05% 89.53%, 89.533% 89.53%)\"],\n  \"area\": [0.0370, 0.0370, 0.0370, 0.0370, 0.0136, 0.0136, 0.0136, 0.0136, 0.0796, 0.0796],\n  \"profit\": 350\n}, {\n  \"path\": \"song/17\",\n  \"bg\": require('../images/bg/song/17/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(100% 0, 50% 0, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(0 50%, 0 0, 50% 50%)\", \"polygon(0 44.57%, 50% 44.57%, 50% 55.43%, 0 55.43%)\", \"polygon(100% 44.57%, 50% 44.57%, 50% 55.43%, 100% 55.43%)\", \"polygon(44.57% 0, 55.43% 0, 55.43% 50%, 44.57% 50%)\", \"polygon(44.57% 100%, 55.43% 100%, 55.43% 50%, 44.57% 50%)\", \"polygon(0 0, 16.38% 0, 50% 33.62%, 50% 50%)\", \"polygon(0 0, 0 16.38%, 33.62% 50%, 50% 50%)\", \"polygon(100% 0, 83.62% 0, 50% 33.62%, 50% 50%)\", \"polygon(100% 0, 100% 16.38%, 66.38% 50%, 50% 50%)\", \"polygon(0 100%, 16.38% 100%, 50% 66.38%, 50% 50%)\", \"polygon(0 100%, 0 83.62%, 33.62% 50%, 50% 50%)\", \"polygon(100% 100%, 100% 83.62%, 66.38% 50%, 50% 50%)\", \"polygon(100% 100%, 83.62% 100%, 50% 66.38%, 50% 50%)\"],\n  \"area\": [0.0148, 0.0148, 0.0148, 0.0148, 0.0148, 0.0148, 0.0148, 0.0148, 0.0125, 0.0125, 0.0125, 0.0125, 0.0480, 0.0480, 0.0480, 0.0480, 0.0480, 0.0480, 0.0480, 0.0480],\n  \"profit\": 350\n}, {\n  \"path\": \"song/20\",\n  \"bg\": require('../images/bg/song/20/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%, 0 50%)\", \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\", \"polygon(100% 50%, 100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 50%, 0 100%, 50% 100%, 50% 50%)\", \"polygon(50% 0, 25% 25%, 50% 50%, 75% 25%)\", \"polygon(50% 100%, 25% 75%, 50% 50%, 75% 75%)\", \"polygon(0 50%, 25% 25%, 50% 50%, 25% 75%)\", \"polygon(100% 50%, 75% 25%, 50% 50%, 75% 75%)\", \"polygon(55.14% 55.14%, 44.86% 44.86%, 17.03% 72.69%, 27.31% 82.97%)\", \"polygon(72.69% 17.03%, 82.97% 27.31%, 55.14% 55.14%, 44.86% 44.86%)\", \"polygon(17.03% 27.31%, 27.31% 17.03%, 82.97% 72.69%, 72.69% 82.97%)\", \"polygon(27.53% 0, 50% 0, 0 50%, 0 27.53%)\", \"polygon(72.47% 0, 50% 0, 100% 50%, 100% 27.53%)\", \"polygon(27.53% 100%, 50% 100%, 0 50%, 0 72.47%)\", \"polygon(72.47% 100%, 50% 100%, 100% 50%, 100% 72.47%)\"],\n  \"area\": [0.0135, 0.0135, 0.0135, 0.0135, 0.0282, 0.0282, 0.0282, 0.0282, 0.0148, 0.0148, 0.0374, 0.0315, 0.0315, 0.0315, 0.0315],\n  \"profit\": 350\n}, {\n  \"path\": \"song/21\",\n  \"bg\": require('../images/bg/song/21/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 0 50%)\", \"polygon(100% 0, 50% 0, 100% 50%)\", \"polygon(0 100%, 50% 100%, 0 50%)\", \"polygon(100% 100%, 50% 100%, 100% 50%)\", \"polygon(0 0, 86.29% 0, 86.29% 13.71%, 0 13.71%)\", \"polygon(86.29% 0, 100% 0, 100% 86.29%, 86.29% 86.29%)\", \"polygon(100% 86.29%, 100% 100%, 13.71% 100%, 13.71% 86.29%)\", \"polygon(13.71% 100%, 0 100%, 0% 13.71%, 13.71% 13.71%)\", \"polygon(36.11% 13.71%, 50% 13.71%, 50% 50%, 13.71% 50%, 13.71% 36.11%)\", \"polygon(36.11% 86.29%, 50% 86.29%, 50% 50%, 13.71% 50%, 13.71% 63.89%)\", \"polygon(63.89% 13.71%, 50% 13.71%, 50% 50%, 86.29% 50%, 86.29% 36.11%)\", \"polygon(63.89% 86.29%, 50% 86.29%, 50% 50%, 86.29% 50%, 86.29% 63.89%)\", \"polygon(50% 34.03%, 65.97% 50%, 50% 65.97%, 34.03% 50%)\"],\n  \"area\": [0.0090, 0.0090, 0.0090, 0.0090, 0.0426, 0.0426, 0.0426, 0.0426, 0.0338, 0.0338, 0.0338, 0.0338, 0.0184],\n  \"profit\": 350\n}, {\n  \"path\": \"song/22\",\n  \"bg\": require('../images/bg/song/22/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 0, 0 100%, 50% 50%)\", \"polygon(100% 100%, 0 100%, 50% 50%)\", \"polygon(100% 100%, 100% 0, 50% 50%)\", \"polygon(9.83% 9.83%, 25.9% 9.83%, 25.9% 90.17%, 9.83% 90.17%)\", \"polygon(25.9% 9.83%, 41.97% 9.83%, 41.97% 70%, 25.9% 70%)\", \"polygon(25.9% 70%, 41.97% 70%, 41.97% 90.17%, 25.9% 90.17%)\", \"polygon(41.97% 9.83%, 58.03% 9.83%, 58.03% 50%, 41.97% 50%)\", \"polygon(41.97% 50%, 58.03% 50%, 58.03% 90.17%, 41.97% 90.17%)\", \"polygon(58.03% 9.83%, 74.1% 9.83%, 74.1% 30%, 58.03% 30%)\", \"polygon(58.03% 30%, 74.1% 30%, 74.1% 90.17%, 58.03% 90.17%)\", \"polygon(74.1% 9.83%, 90.17% 9.83%, 90.17% 90.17%, 74.1% 90.17%)\", \"polygon(74.1% 9.83%, 90.17% 9.83%, 9.83% 90.17%, 9.83% 74.1%)\", \"polygon(90.17% 9.83%, 90.17% 25.9%, 25.9% 90.17%, 9.83% 90.17%)\"],\n  \"area\": [0.0319, 0.0319, 0.0319, 0.0319, 0.0335, 0.0225, 0.0044, 0.0135, 0.0135, 0.0044, 0.0225, 0.0335, 0.0419, 0.0419],\n  \"profit\": 350\n}, {\n  \"path\": \"song/31\",\n  \"bg\": require('../images/bg/song/31/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%, 0 50%)\", \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%, 0 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%, 100% 50%)\", \"polygon(100% 0, 91.16% 0, 100% 8.84%)\", \"polygon(0 100%, 0 91.16%, 8.84% 100%)\", \"polygon(0 0, 10.5% 0, 4.42% 95.58%, 0 91.16%)\", \"polygon(0 0, 91.16% 0, 95.58% 4.42%, 0 10.5%)\", \"polygon(95.58% 4.42%, 100% 8.84%, 100% 100%, 89.5% 100%)\", \"polygon(100% 89.5%, 100% 100%, 8.84% 100%, 4.42% 95.58%)\", \"polygon(7.23% 50%, 28.615% 28.615%, 50% 50%, 28.615% 71.385%)\", \"polygon(92.77% 50%, 71.385% 28.615%, 50% 50%, 71.385% 71.385%)\", \"polygon(50% 7.23%, 28.615% 28.615%, 50% 50%, 71.385% 28.615%)\", \"polygon(50% 92.77%, 28.615% 71.385%, 50% 50%, 71.385% 71.385%)\", \"polygon(35.7433% 21.4867%, 21.45% 35.7433%, 64% 78.6%, 78.5133% 64.2567%)\", \"polygon(9.72% 9.72%, 25.19% 8.77%, 8.77% 25.19%)\", \"polygon(90.28% 90.28%, 74.81% 91.23%, 91.23% 74.81%)\", \"polygon(5.58% 76.86%, 23.14% 94.43%, 4.42% 95.58%)\", \"polygon(76.86% 5.58%, 94.43% 23.14%, 95.58% 4.42%)\", \"polygon(33.27% 76.04%, 23.97% 66.74%, 39.74% 54.01%, 46% 60.25%)\", \"polygon(76.04% 33.27%, 66.74% 23.97%, 54.01% 39.74%, 60.25% 46%)\"],\n  \"area\": [0.0245, 0.0311, 0.0311, 0.0245, 0.0014, 0.0014, 0.0216, 0.0256, 0.0216, 0.0256, 0.0178, 0.0178, 0.0178, 0.0178, 0.0437, 0.0043, 0.0043, 0.0064, 0.0063, 0.0079, 0.0079],\n  \"profit\": 350\n}, {\n  \"path\": \"song/32\",\n  \"bg\": require('../images/bg/song/32/bg.png'),\n  \"clipPath\": [\"\", \"polygon(0 41.1%, 30% 41.1%, 30% 100%, 0 100%)\", \"polygon(100% 58.9%, 70% 58.9%, 70% 0, 100% 0)\", \"polygon(58.9% 0, 58.9% 30%, 0 30%, 0 0)\", \"polygon(41.1% 100%, 41.1% 70%, 100% 70%, 100% 100%)\", \"polygon(86.3% 0, 100% 13.7%, 63.7% 50%, 50% 36.3%)\", \"polygon(36.3% 50%, 50% 63.7%, 13.7% 100%, 0 86.3% )\", \"polygon(36.3% 50%, 50% 36.3%, 13.7% 0, 0 13.7% )\", \"polygon(63.7% 50%, 50% 63.7%, 86.3% 100%, 100% 86.3% )\", \"polygon(22.6% 36.3%, 36.3% 50%, 86.3% 0, 58.9% 0)\", \"polygon(77.4% 63.7%, 63.7% 50%, 13.7% 100%, 41.1% 100%)\", \"polygon(50% 36.3%, 63.7% 22.6%, 100% 58.9%, 100% 86.3%)\", \"polygon(50% 63.7%, 36.3% 77.4%, 0 41.1%, 0 13.7%)\", \"polygon(0 0, 13.7% 0, 0 13.7%)\", \"polygon(100% 100%, 86.3% 100%, 100% 86.3%)\", \"polygon(100% 0, 100% 86.3%, 93.15% 93.15%, 89.09% 0)\", \"polygon(100% 0, 100% 10.91%, 6.85% 6.85%, 13.7% 0)\", \"polygon(0 100%, 10.91% 100%, 6.85% 6.85%, 0 13.7%)\", \"polygon(0 89.09%, 0 100%, 86.3% 100%, 93.15% 93.15%)\"],\n  \"area\": [0.0135, 0.0064, 0.0064, 0.0074, 0.0074, 0.0157, 0.0157, 0.0184, 0.0184, 0.0333, 0.0333, 0.0344, 0.0344, 0.0036, 0.0036, 0.0250, 0.0290, 0.0250, 0.0290],\n  \"profit\": 350\n}, {\n  \"path\": \"song/37\",\n  \"bg\": require('../images/bg/song/37/bg.png'),\n  \"clipPath\": [\"\", \"polygon(0 0, 34.5% 34.5%, 63.39% 34.5%, 100% 0)\", \"polygon(0 100%, 34.5% 63.39%, 63.39% 63.39%, 100% 100%)\", \"polygon(0 0, 34.5% 34.5%, 34.5% 63.39%, 0 100%)\", \"polygon(100% 0, 63.39% 34.5%, 63.39% 63.39%, 100% 100%)\"],\n  \"area\": [0.0346, 0.0814, 0.0814, 0.0814, 0.0814],\n  \"profit\": 350\n}];\nexport const yuan = [{\n  \"path\": \"yuan/12\",\n  \"bg\": require('../images/bg/yuan/12/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 25% 0, 0 25%)\", \"polygon(0 100%, 75% 100%, 0 75%)\", \"polygon(100% 100%, 75% 100%, 100% 75%)\", \"polygon(100% 0, 75% 0, 100% 25%)\", \"polygon(25% 0, 50% 0, 0 50%, 0 25%)\", \"polygon(75% 0, 50% 0, 100% 50%, 100% 25%)\", \"polygon(25% 100%, 50% 100%, 0 50%, 0 75%)\", \"polygon(75% 100%, 50% 100%, 100% 50%, 100% 75%)\", \"polygon(30.5% 19.5%, 50% 39%, 50% 0)\", \"polygon(69.5% 19.5%, 50% 39%, 50% 0)\", \"polygon(30.5% 80.5%, 50% 61%, 50% 100%)\", \"polygon(69.5% 80.5%, 50% 61%, 50% 100%)\", \"polygon(19.5% 69.5%,39% 50%,0 50%)\", \"polygon(19.5% 30.5%,39% 50%,0 50%)\", \"polygon(80.5% 69.5%,61% 50%,100% 50%)\", \"polygon(80.5% 30.5%,61% 50%,100% 50%)\", \"polygon(19.5% 69.5%, 30.5% 80.5%, 50% 61%, 50% 50%, 39% 50%)\", \"polygon(69.5% 19.5%, 80.5% 30.5%, 61% 50%, 50% 50%, 50% 39%)\", \"polygon(19.5% 30.5%, 30.5% 19.5%, 80.5% 69.5%,69.5% 80.5%)\"],\n  \"area\": [0.0113, 0.0113, 0.0113, 0.0113, 0.0334, 0.0334, 0.0334, 0.0334, 0.0138, 0.0138, 0.0138, 0.0138, 0.0138, 0.0138, 0.0138, 0.0138, 0.0153, 0.0153, 0.0390],\n  \"profit\": 400\n}, {\n  \"path\": \"yuan/19\",\n  \"bg\": require('../images/bg/yuan/19/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 25% 25%, 0 50%)\", \"polygon(0 0, 25% 25%, 50% 0)\", \"polygon(100% 0, 75% 25%, 50% 0)\", \"polygon(100% 0, 75% 25%, 100% 50%)\", \"polygon(100% 100%, 75% 75%, 100% 50%)\", \"polygon(100% 100%, 75% 75%, 50% 100%)\", \"polygon(0 100%, 25% 75%, 50% 100%)\", \"polygon(0 100%, 25% 75%, 0 50%)\", \"polygon(50% 0, 75% 25%, 50% 50%, 25% 25%)\", \"polygon(100% 50%, 75% 25%, 50% 50%, 75% 75%)\", \"polygon(50% 100%, 25% 75%, 50% 50%, 75% 75%)\", \"polygon(0 50%, 25% 75%, 50% 50%, 25% 25%)\", \"polygon(100% 0, 96.35% 0, 75% 21.35%, 78.65% 25%, 100% 3.65%)\", \"polygon(0 100%, 3.65% 100%, 25% 78.65%, 21.35% 75%, 0 96.35%)\", \"polygon(46.35% 0, 53.65% 0, 26.825% 26.825%, 23.175% 23.175%)\", \"polygon(0 46.35%, 0 53.65%, 26.825% 26.825%, 23.175% 23.175%)\", \"polygon(100% 46.35%, 100% 53.65%, 76.825% 76.825%, 73.175% 73.175%)\", \"polygon(46.35% 100%, 53.65% 100%, 76.825% 76.825%, 73.175% 73.175%)\", \"polygon(100% 100%, 96.35% 100%, 46.35% 50%, 50% 46.35%, 100% 96.35%)\", \"polygon(0 0, 3.65% 0, 53.65% 50%, 50% 53.65%, 0 3.65%)\", \"polygon(75% 28.65%, 71.35% 25%, 25% 71.35%, 28.65% 75%)\", \"polygon(53.65% 0, 50% 3.65%, 96.35% 50%, 100% 46.35%)\", \"polygon(3.65% 50%, 50% 96.35%, 46.35% 100%, 0 53.65%)\"],\n  \"area\": [0.0163, 0.0163, 0.0163, 0.0163, 0.0163, 0.0163, 0.0163, 0.0163, 0.0326, 0.0326, 0.0326, 0.0326, 0.0059, 0.0059, 0.0062, 0.0062, 0.0062, 0.0062, 0.0126, 0.0126, 0.0122, 0.0124, 0.0124],\n  \"profit\": 400\n}, {\n  \"path\": \"yuan/27\",\n  \"bg\": require('../images/bg/yuan/27/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 50%, 0 100%)\", \"polygon(0 0, 50% 50%, 100% 0)\", \"polygon(100% 100%, 50% 50%, 100% 0)\", \"polygon(100% 100%, 50% 50%, 0 100%)\", \"polygon(0 0, 19.76% 0, 50% 30.23%, 36.5% 36.5%)\", \"polygon(0 0, 0 19.76%, 30.23% 50%, 36.5% 36.5%)\", \"polygon(0 100%, 19.76% 100%, 50% 69.77%, 36.5% 63.5%)\", \"polygon(0 100%, 0 80.24%, 30.23% 50%, 36.5% 63.5%)\", \"polygon(100% 0, 80.24% 0, 50% 30.23%, 63.5% 36.5%)\", \"polygon(100% 0, 100% 19.76%, 69.77% 50%, 63.5% 36.5%)\", \"polygon(100% 100%, 100% 80.24%, 69.77% 50%, 63.5% 63.5%)\", \"polygon(100% 100%, 80.24% 100%, 50% 69.77%, 63.5% 63.5%)\", \"polygon(50% 30.23%, 50% 50%, 30.23% 50%, 36.5% 36.5%)\", \"polygon(50% 69.77%, 50% 50%, 30.23% 50%, 36.5% 63.5%)\", \"polygon(50% 30.23%, 50% 50%, 69.77% 50%, 63.5% 36.5%)\", \"polygon(50% 69.77%, 50% 50%, 69.77% 50%, 63.5% 63.5%)\"],\n  \"area\": [0.0328, 0.0328, 0.0328, 0.0328, 0.0238, 0.0238, 0.0238, 0.0238, 0.0238, 0.0238, 0.0238, 0.0095, 0.0095, 0.0095, 0.0095],\n  \"profit\": 400\n}, {\n  \"path\": \"yuan/29\",\n  \"bg\": require('../images/bg/yuan/29/bg.png'),\n  \"clipPath\": [\"polygon(6.25% 0, 0 6.25%, 0 0)\", \"polygon(6.25% 0, 18.75% 0, 6.25% 12.5%, 0 6.25%)\", \"polygon(18.75% 0, 31.25% 0, 12.5% 18.75%, 6.25% 12.5%)\", \"polygon(31.25% 0, 43.75% 0, 18.75% 25%, 12.5% 18.75%)\", \"polygon(43.75% 0, 56.25% 0, 50% 6.25%)\", \"polygon(56.25% 0, 68.75% 0, 56.25% 12.5%, 50% 6.25%)\", \"polygon(68.75% 0, 81.25% 0, 62.5% 18.75%, 56.25% 12.5%)\", \"polygon(81.25% 0, 93.75% 0, 68.75% 25%, 62.5% 18.75%)\", \"polygon(43.75% 0, 37.5% 6.25%, 68.75% 37.5%, 75% 31.25%)\", \"polygon(37.5% 6.25%, 31.25% 12.5%, 62.5% 43.75%, 68.75% 37.5%)\", \"polygon(31.25% 12.5%, 25% 18.75%, 56.25% 50%, 62.5% 43.75%)\", \"polygon(25% 18.75%, 31.25% 25%, 25% 31.25%, 18.75% 25%)\", \"polygon(100% 6.25%, 93.75% 0, 100% 0)\", \"polygon(100% 6.25%, 100% 18.75%, 87.5% 6.25%, 93.75% 0)\", \"polygon(100% 18.75%, 100% 31.25%, 81.25% 12.5%, 87.5% 6.25%)\", \"polygon(100% 31.25%, 100% 43.75%, 75% 18.75%, 81.25% 12.5%)\", \"polygon(100% 43.75%, 100% 56.25%, 93.75% 50%)\", \"polygon(100% 56.25%, 100% 68.75%, 87.5% 56.25%, 93.75% 50%)\", \"polygon(100% 68.75%, 100% 81.25%, 81.25% 62.5%, 87.5% 56.25%)\", \"polygon(100% 81.25%, 100% 93.75%, 75% 68.75%, 81.25% 62.5%)\", \"polygon(100% 43.75%, 93.75% 37.5%, 62.5% 68.75%, 68.75% 75%)\", \"polygon(93.75% 37.5%, 87.5% 31.25%, 56.25% 62.5%, 62.5% 68.75%)\", \"polygon(87.5% 31.25%, 81.25% 25%, 50% 56.25%, 56.25% 62.5%)\", \"polygon(75% 18.75%, 81.25% 25%, 75% 31.25%, 68.75% 25%)\", \"polygon(93.75% 100%, 100% 93.75%, 100% 100%)\", \"polygon(93.75% 100%, 81.25% 100%, 93.75% 87.5%, 100% 93.75%)\", \"polygon(81.25% 100%, 68.75% 100%, 87.5% 81.25%, 93.75% 87.5%)\", \"polygon(68.75% 100%, 56.25% 100%, 81.25% 75%, 87.5% 81.25%)\", \"polygon(56.25% 100%, 43.75% 100%, 50% 93.75%)\", \"polygon(43.75% 100%, 31.25% 100%, 43.75% 87.5%, 50% 93.75%)\", \"polygon(31.25% 100%, 18.75% 100%, 37.5% 81.25%, 43.75% 87.5%)\", \"polygon(18.75% 100%, 6.25% 100%, 31.25% 75%, 37.5% 81.25%)\", \"polygon(56.25% 100%, 62.5% 93.75%, 31.25% 62.5%, 25% 68.75%)\", \"polygon(62.5% 93.75%, 68.75% 87.5%, 37.5% 56.25%, 31.25% 62.5%)\", \"polygon(68.75% 87.5%, 75% 81.25%, 43.75% 50%, 37.5% 56.25%)\", \"polygon(75% 81.25%, 68.75% 75%, 75% 68.75%, 81.25% 75%)\", \"polygon(0 93.75%, 6.25% 100%, 0 100%)\", \"polygon(0 93.75%, 0 81.25%, 12.5% 93.75%, 6.25% 100%)\", \"polygon(0 81.25%, 0 68.75%, 18.75% 87.5%, 12.5% 93.75%)\", \"polygon(0 68.75%, 0 56.25%, 25% 81.25%, 18.75% 87.5%)\", \"polygon(0 56.25%, 0 43.75%, 6.25% 50%)\", \"polygon(0 43.75%, 0 31.25%, 12.5% 43.75%, 6.25% 50%)\", \"polygon(0 31.25%, 0 18.75%, 18.75% 37.5%, 12.5% 43.75%)\", \"polygon(0 18.75%, 0 6.25%, 25% 31.25%, 18.75% 37.5%)\", \"polygon(0 56.25%, 6.25% 62.5%, 37.5% 31.25%, 31.25% 25%)\", \"polygon(6.25% 62.5%, 12.5% 68.75%, 43.75% 37.5%, 37.5% 31.25%)\", \"polygon(12.5% 68.75%, 18.75% 75%, 50% 43.75%, 43.75% 37.5%)\", \"polygon(25% 68.75%, 31.25% 75%, 25% 81.25%, 18.75% 75%)\", \"polygon(50% 43.75%, 56.25% 50%, 50% 56.25%, 43.75% 50%)\"],\n  \"area\": [0.0007, 0.0042, 0.0070, 0.0098, 0.0014, 0.0042, 0.0070, 0.0098, 0.0141, 0.0141, 0.0141, 0.0028, 0.0007, 0.0042, 0.0070, 0.0098, 0.0014, 0.0042, 0.0070, 0.0098, 0.0141, 0.0141, 0.0141, 0.0028, 0.0007, 0.0042, 0.0070, 0.0098, 0.0014, 0.0042, 0.0070, 0.0098, 0.0141, 0.0141, 0.0141, 0.0028, 0.0007, 0.0042, 0.0070, 0.0098, 0.0014, 0.0042, 0.0070, 0.0098, 0.0141, 0.0141, 0.0141, 0.0028, 0.0028],\n  \"profit\": 400\n}, {\n  \"path\": \"yuan/33\",\n  \"bg\": require('../images/bg/yuan/33/bg.png'),\n  \"clipPath\": [\"polygon(50% 0, 50% 50%, 0 50%)\", \"polygon(50% 0, 50% 50%, 100% 50%)\", \"polygon(100% 50%, 50% 50%, 50% 100%)\", \"polygon(0 50%, 50% 50%, 50% 100%)\", \"polygon(9.5% 42.08%, 17.42% 50%, 9.5% 57.92%)\", \"polygon(42.08% 9.5%, 50% 17.42%, 57.92% 9.5%)\", \"polygon(90.5% 42.08%, 82.58% 50%, 90.5% 57.92%)\", \"polygon(42.08% 90.5%, 50% 82.58%, 57.92% 90.5%)\", \"polygon(23.88% 23.88%, 43.38% 23.88%, 23.88% 43.38%)\", \"polygon(76.12% 23.88%, 56.62% 23.88%, 76.12% 43.38%)\", \"polygon(23.88% 76.12%, 43.38% 76.12%, 23.88% 56.62%)\", \"polygon(76.12% 76.12%, 56.62% 76.12%, 76.12% 56.62%)\", \"polygon(57.92% 9.5%, 71% 9.5%, 50% 30.5%, 43.38% 23.88%)\", \"polygon(50% 30.5%, 9.5% 71%, 9.5% 57.92%, 43.38% 23.88%)\", \"polygon(90.5% 81%, 90.5% 57.92%, 82.58% 50%, 76.12% 56.62%)\", \"polygon(76.12% 43.38%, 69.7% 50%, 29% 9.5%, 42.08% 9.5%)\", \"polygon(50% 69.5%, 90.5% 29%, 90.5% 42.08%, 56.62% 76.12%)\", \"polygon(42.08% 90.5%, 29% 90.5%, 50% 69.5%, 56.62% 76.12%)\", \"polygon(9.5% 29%, 9.5% 42.08%, 17.42% 50%, 23.88% 43.38%)\", \"polygon(23.88% 56.62%, 30.3% 50%, 71% 90.5%, 57.92% 90.5%)\", \"polygon(9.5% 9.5%, 23.88% 23.88%, 23.88% 43.38%, 9.5% 29%)\", \"polygon(9.5% 9.5%, 23.88% 23.88%, 43.38% 23.88%, 29% 9.5%)\", \"polygon(90.5% 9.5%, 76.12% 23.88%, 76.12% 43.38%, 90.5% 29%)\", \"polygon(90.5% 9.5%, 76.12% 23.88%, 56.62% 23.88%, 71% 9.5%)\", \"polygon(90.5% 90.5%, 76.12% 76.12%, 76.12% 56.62%, 90.5% 71%)\", \"polygon(90.5% 90.5%, 76.12% 76.12%, 56.62% 76.12%, 71% 90.5%)\", \"polygon(9.5% 90.5%, 23.88% 76.12%, 23.88% 56.62%, 9.5% 71%)\", \"polygon(9.5% 90.5%, 23.88% 76.12%, 43.38% 76.12%, 29% 90.5%)\", \"polygon(0 0, 90.5% 0, 90.5% 9.5%, 0 9.5%)\", \"polygon(90.5% 0, 100% 0, 100% 90.5%, 90.5% 90.5%)\", \"polygon(100% 90.5%, 100% 100%, 9.5% 100%, 9.5% 90.5%)\", \"polygon(0 100%, 9.5% 100%, 9.5% 9.5%, 0 9.5%)\"],\n  \"area\": [0.0068, 0.0068, 0.0068, 0.0068, 0.0022, 0.0022, 0.0022, 0.0022, 0.0068, 0.0068, 0.0068, 0.0068, 0.0053, 0.0175, 0.0053, 0.0175, 0.0175, 0.0053, 0.0053, 0.0175, 0.0101, 0.0101, 0.0101, 0.0101, 0.0101, 0.0101, 0.0101, 0.0311, 0.0311, 0.0311, 0.0311],\n  \"profit\": 400\n}, {\n  \"path\": \"yuan/36\",\n  \"bg\": require('../images/bg/yuan/36/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 0, 0 100%, 50% 50%)\", \"polygon(100% 100%, 0 100%, 50% 50%)\", \"polygon(100% 100%, 100% 0, 50% 50%)\", \"polygon(13.88% 13.88%, 13.88% 50%, 50% 50%)\", \"polygon(86.12% 13.88%, 86.12% 50%, 50% 50%)\", \"polygon(86.12% 86.12%, 86.12% 50%, 50% 50%)\", \"polygon(13.88% 86.12%, 13.88% 50%, 50% 50%)\", \"polygon(13.88% 13.88%, 50% 13.88%, 50% 50%)\", \"polygon(86.12% 13.88%, 50% 13.88%, 50% 50%)\", \"polygon(86.12% 86.12%, 50% 86.12%, 50% 50%)\", \"polygon(13.88% 86.12%, 50% 86.12%, 50% 50%)\", \"polygon(81.37% 0, 100% 0, 50% 50%, 40.685% 40.685%)\", \"polygon(40.685% 40.685%, 50% 50%, 0 100%, 0 81.37%)\", \"polygon(100% 0, 100% 18.63%, 59.315% 59.315%, 50% 50%)\", \"polygon(50% 50%, 59.315% 59.315%, 18.63% 100%, 0 100%)\", \"polygon(50% 13.88%, 86.12% 50%, 68.06% 68.06%, 31.94% 31.94%)\", \"polygon(31.94% 31.94%, 68.06% 68.06%, 50% 86.12%, 13.88% 50%)\", \"polygon(0 0, 18.63% 0, 100% 81.37%, 100% 100%)\", \"polygon(0 0, 100% 100%, 81.37% 100%, 0 18.63%)\"],\n  \"area\": [0.0243, 0.0243, 0.0243, 0.0243, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0179, 0.0179, 0.0179, 0.0179, 0.0240, 0.0240, 0.0597, 0.0597],\n  \"profit\": 400\n}];\nexport const ming = [{\n  \"path\": \"ming/6\",\n  \"bg\": require('../images/bg/ming/6/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 100% 0, 87% 13%, 13% 13%)\", \"polygon(0 0, 13% 13%, 13% 87%, 0 100%)\", \"polygon(0 100%, 100% 100%, 87% 87%, 13% 87%)\", \"polygon(100% 0, 87% 13%, 87% 87%, 100% 100%)\", \"polygon(13% 13%, 33% 13%, 33% 33%, 13% 33%)\", \"polygon(67% 13%, 87% 13%, 87% 33%, 67% 33%)\", \"polygon(67% 87%, 87% 87%, 87% 67%, 67% 67%)\", \"polygon(13% 87%, 33% 87%, 33% 67%, 13% 67%)\", \"polygon(33% 13%, 67% 13%, 50% 30%)\", \"polygon(13% 33%, 13% 67%, 30% 50%)\", \"polygon(33% 87%, 67% 87%, 50% 70%)\", \"polygon(87% 33%, 87% 67%, 70% 50%)\", \"polygon(33% 13%, 50% 30%, 50% 50%, 33% 33%)\", \"polygon(67% 87%, 50% 70%, 50% 50%, 67% 67%)\", \"polygon(13% 67%, 33% 67%, 50% 50%, 30% 50%)\", \"polygon(87% 33%, 67% 33%, 50% 50%, 70% 50%)\", \"polygon(13% 33%, 33% 33%, 50% 50%, 30% 50%)\", \"polygon(67% 13%, 50% 30%, 50% 50%, 67% 33%)\", \"polygon(33% 87%, 50% 70%, 50% 50%, 33% 67%)\", \"polygon(87% 67%, 67% 67%, 50% 50%, 70% 50%)\"],\n  \"area\": [0.0403, 0.0403, 0.0403, 0.0403, 0.0145, 0.0145, 0.0145, 0.0145, 0.0105, 0.0105, 0.0105, 0.0105, 0.0124, 0.0124, 0.0124, 0.0124, 0.0124, 0.0124, 0.0124, 0.0124],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/7\",\n  \"bg\": require('../images/bg/ming/7/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 0 15.8%, 15.8% 0)\", \"polygon(100% 0, 100% 15.8%, 84.2% 0)\", \"polygon(0 100%, 0 84.2%, 15.8% 100%)\", \"polygon(100% 100%, 100% 84.2%, 84.2% 100%)\", \"polygon(0 15.8%, 0 50%, 50% 50%)\", \"polygon(0 84.2%, 0 50%, 50% 50%)\", \"polygon(100% 15.8%, 100% 50%, 50% 50%)\", \"polygon(100% 84.2%, 100% 50%, 50% 50%)\", \"polygon(15.8% 0, 50% 0, 50% 50%)\", \"polygon(15.8% 100%, 50% 100%, 50% 50%)\", \"polygon(84.2% 0, 50% 0, 50% 50%)\", \"polygon(84.2% 100%, 50% 100%, 50% 50%)\", \"polygon(15.8% 0, 54.57% 54.57%, 0 15.8%)\", \"polygon(15.8% 100%, 54.57% 45.43%, 0 84.2%)\", \"polygon(84.2% 0, 45.43% 54.57%, 100% 15.8%)\", \"polygon(84.2% 100%, 45.43% 45.43%, 100% 84.2%)\", \"polygon(23.94% 0, 45.35% 30.14%, 54.65% 30.14%, 76.06% 0)\", \"polygon(23.94% 100%, 45.35% 69.86%, 54.65% 69.86%, 76.06% 100%)\", \"polygon(0 23.94%, 30.14% 45.35%, 30.14% 54.65%, 0 76.06%)\", \"polygon(100% 23.94%, 69.86% 45.35%, 69.86% 54.65%, 100% 76.06%)\", \"polygon(45.35% 30.14%, 47.61% 30.14%, 47.61% 40.85%, 40.85% 40.85%, 37.75% 37.75%)\", \"polygon(30.14% 45.35%, 30.14% 47.61%, 40.85% 47.61%, 40.85% 40.85%, 37.75% 37.75%)\", \"polygon(54.65% 30.14%, 52.39% 30.14%, 52.39% 40.85%, 59.15% 40.85%, 62.25% 37.75%)\", \"polygon(69.86% 45.35%, 69.86% 47.61%, 59.15% 47.61%, 59.15% 40.85%, 62.25% 37.75%)\", \"polygon(30.14% 54.65%, 30.14% 52.39%, 40.85% 52.39%, 40.85% 59.15%, 37.75% 62.25%)\", \"polygon(45.35% 69.86%, 47.61% 69.86%, 47.61% 59.15%, 40.85% 59.15%, 37.75% 62.25%)\", \"polygon(54.65% 69.86%, 52.39% 69.86%, 52.39% 59.15%, 59.15% 59.15%, 62.25% 62.25%)\", \"polygon(69.86% 54.65%, 69.86% 52.39%, 59.15% 52.39%, 59.15% 59.15%, 62.25% 62.25%)\", \"polygon(47.61% 50%, 52.39% 50%, 52.39% 69.86%, 47.61% 69.86%)\", \"polygon(47.61% 30.14%, 52.39% 30.14%, 52.39% 50%, 47.61% 50%)\", \"polygon(30.14% 47.61%, 30.14% 52.39%, 50% 52.39%, 50% 47.61%)\", \"polygon(50% 47.61%, 50% 52.39%, 69.86% 52.39%, 69.86% 47.61%)\", \"polygon(40.85% 40.85%, 50% 40.85%, 40.85% 50%)\", \"polygon(59.15% 40.85%, 50% 40.85%, 59.15% 50%)\", \"polygon(40.85% 59.15%, 50% 59.15%, 40.85% 50%)\", \"polygon(59.15% 59.15%, 50% 59.15%, 59.15% 50%)\", \"polygon(50% 40.85%, 40.85% 50%, 50% 59.15%, 59.15% 50%)\"],\n  \"area\": [0.0044, 0.0044, 0.0044, 0.0044, 0.0095, 0.0095, 0.0095, 0.0095, 0.0095, 0.0095, 0.0095, 0.0095, 0.0228, 0.0228, 0.0228, 0.0228, 0.0336, 0.0336, 0.0336, 0.0336, 0.0027, 0.0027, 0.0027, 0.0027, 0.0027, 0.0027, 0.0027, 0.0027, 0.0019, 0.0019, 0.0019, 0.0019, 0.0015, 0.0015, 0.0015, 0.0015, 0.0060],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/11\",\n  \"bg\": require('../images/bg/ming/11/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 25% 25%)\", \"polygon(100% 0, 50% 0, 75% 25%)\", \"polygon(0 100%, 50% 100%, 25% 75%)\", \"polygon(100% 100%, 50% 100%, 75% 75%)\", \"polygon(0 0, 0 50%, 25% 25%)\", \"polygon(0 100%, 0 50%, 25% 75%)\", \"polygon(100% 0, 100% 50%, 75% 25%)\", \"polygon(100% 100%, 100% 50%, 75% 75%)\", \"polygon(0 0, 16.5% 0, 21.4% 4.9%, 4.9% 4.9%)\", \"polygon(0 0, 0 16.5%, 4.9% 21.4%, 4.9% 4.9%)\", \"polygon(100% 0, 83.5% 0, 78.6% 4.9%, 95.1% 4.9%)\", \"polygon(100% 0, 100% 16.5%, 95.1% 21.4%, 95.1% 4.9%)\", \"polygon(100% 100%, 100% 83.5%, 95.1% 78.6%, 95.1% 95.1%)\", \"polygon(100% 100%, 83.5% 100%, 78.6% 95.1%, 95.1% 95.1%)\", \"polygon(0 100%, 0 83.5%, 4.9% 78.6%, 4.9% 95.1%)\", \"polygon(0 100%, 16.5% 100%, 21.4% 95.1%, 4.9% 95.1%)\", \"polygon(50% 0, 25% 25%, 50% 50%)\", \"polygon(50% 0, 75% 25%, 50% 50%)\", \"polygon(50% 100%, 25% 75%, 50% 50%)\", \"polygon(50% 100%, 75% 75%, 50% 50%)\", \"polygon(0 50%, 25% 25%, 50% 50%)\", \"polygon(0 50%, 25% 75%, 50% 50%)\", \"polygon(100% 50%, 75% 25%, 50% 50%)\", \"polygon(100% 50%, 75% 75%, 50% 50%)\", \"polygon(44.9% 28.4%, 50% 33.3%, 50% 50%, 44.9% 45.1%)\", \"polygon(55.1% 28.4%, 50% 33.3%, 50% 50%, 55.1% 45.1%)\", \"polygon(28.4% 44.9%, 33.3% 50%, 50% 50%, 45.1% 44.9%)\", \"polygon(28.4% 55.1%, 33.3% 50%, 50% 50%, 45.1% 55.1%)\", \"polygon(71.6% 44.9%, 66.7% 50%, 50% 50%, 54.9% 44.9%)\", \"polygon(71.6% 55.1%, 66.7% 50%, 50% 50%, 54.9% 55.1%)\", \"polygon(44.9% 71.6%, 50% 66.7%, 50% 50%, 44.9% 55.1%)\", \"polygon(55.1% 71.6%, 50% 66.7%, 50% 50%, 55.1% 55.1%)\", \"polygon(4.9% 4.9%, 21.4% 4.9%, 44.9% 28.4%, 44.9% 44.9%)\", \"polygon(4.9% 4.9%, 4.9% 21.4%, 28.4% 44.9%, 44.9% 44.9%)\", \"polygon(4.9% 95.1%, 21.4% 95.1%, 44.9% 71.6%, 44.9% 55.1%)\", \"polygon(95.1% 4.9%, 95.1% 21.4%, 71.6% 44.9%, 55.1% 44.9%)\", \"polygon(95.1% 4.9%, 78.6% 4.9%, 55.1% 28.4%, 55.1% 44.9%)\", \"polygon(4.9% 95.1%, 4.9% 78.6%, 28.4% 55.1%, 44.9% 55.1%)\", \"polygon(95.1% 95.1%, 78.6% 95.1%, 55.1% 71.6%, 55.1% 55.1%)\", \"polygon(95.1% 95.1%, 95.1% 78.6%, 71.6% 55.1%, 55.1% 55.1%)\"],\n  \"area\": [0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0030, 0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0100, 0.0031, 0.0031, 0.0031, 0.0031, 0.0031, 0.0031, 0.0031, 0.0031, 0.0189, 0.0189, 0.0189, 0.0189, 0.0189, 0.0189, 0.0189, 0.0189],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/16\",\n  \"bg\": require('../images/bg/ming/16/bg.png'),\n  \"clipPath\": [\"polygon(14.66% 14.66%, 19.2% 14.66%, 19.2% 54.54%, 14.66% 54.54%)\", \"polygon(14.66% 14.66%, 35.34% 14.66%, 35.34% 19.2%, 14.66% 19.2%)\", \"polygon(85.34% 85.34%, 80.8% 85.34%, 80.8% 45.46%, 85.34% 45.46%)\", \"polygon(85.34% 85.34%, 64.66% 85.34%, 64.66% 80.8%, 85.34% 80.8%)\", \"polygon(45.45% 14.66%, 85.34% 14.66%, 85.34% 19.2%, 45.45% 19.2%)\", \"polygon(80.8% 14.66%, 85.34% 14.66%, 85.34% 35.34%, 80.8% 35.34%)\", \"polygon(54.55% 85.34%, 14.66% 85.34%, 14.66% 80.8%, 54.55% 80.8%)\", \"polygon(19.2% 85.34%, 14.66% 85.34%, 14.66% 64.66%, 19.2% 64.66%)\", \"polygon(19.2% 19.2%, 19.2% 80.8%, 50% 50%)\", \"polygon(19.2% 80.8%, 80.8% 80.8%, 50% 50%)\", \"polygon(80.8% 19.2%, 80.8% 80.8%, 50% 50%)\", \"polygon(19.2% 19.2%, 80.8% 19.2%, 50% 50%)\", \"polygon(19.2% 19.2%, 19.2% 30.8%, 30.8% 19.2%)\", \"polygon(19.2% 80.8%, 19.2% 69.2%, 30.8% 80.8%)\", \"polygon(80.8% 19.2%, 69.2% 19.2%, 80.8% 30.8%)\", \"polygon(80.8% 80.8%, 69.2% 80.8%, 80.8% 69.2%)\", \"polygon(0 0, 50% 0, 50% 14.66%, 0 14.66%)\", \"polygon(50% 0, 85.34% 0, 85.34% 14.66%, 50% 14.66%)\", \"polygon(85.34% 0, 100% 0, 100% 50%, 85.34% 50%)\", \"polygon(85.34% 50%, 100% 50%, 100% 85.34%, 85.34% 85.34%)\", \"polygon(50% 85.34%, 50% 100%, 14.66% 100%, 14.66% 85.34%)\", \"polygon(100% 85.34%, 100% 100%, 50% 100%, 50% 85.34%)\", \"polygon(14.66% 50%, 0% 50%, 0 14.66%, 14.66% 14.66%)\", \"polygon(14.66% 100%, 0% 100%, 0 50%, 14.66% 50%)\", \"polygon(50% 0, 50% 14.66%, 19.2% 45.46%, 19.2% 30.8%)\", \"polygon(14.66% 35.34%, 14.66% 50%, 0% 50%)\", \"polygon(54.54% 19.2%, 69.2% 19.2%, 100% 50%, 85.34% 50%)\", \"polygon(50% 0%, 50% 14.66%, 64.66% 14.66%)\", \"polygon(80.8% 54.54%, 80.8% 69.2%, 50% 100%, 50% 85.34%)\", \"polygon(85.34% 50%, 100% 50%, 85.34% 64.66%)\", \"polygon(45.46% 80.8%, 30.8% 80.8%, 0 50%, 14.66% 50%)\", \"polygon(50% 85.34%, 50% 100%, 35.34% 85.34%)\"],\n  \"area\": [0.0054, 0.0030, 0.0054, 0.0030, 0.0054, 0.0030, 0.0054, 0.0030, 0.0219, 0.0219, 0.0219, 0.0219, 0.0026, 0.0026, 0.0026, 0.0026, 0.0224, 0.0148, 0.0224, 0.0148, 0.0148, 0.0224, 0.0148, 0.0224, 0.0162, 0.0038, 0.0162, 0.0038, 0.0162, 0.0038, 0.0162, 0.0038],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/18\",\n  \"bg\": require('../images/bg/ming/18/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 0 100%, 50% 50%)\", \"polygon(100% 0, 100% 100%, 50% 50%)\", \"polygon(0 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 100% 100%, 50% 50%)\", \"polygon(8.53% 8.53%, 25.31% 8.53%, 8.53% 25.31%)\", \"polygon(91.47% 8.53%, 74.69% 8.53%, 91.47% 25.31%)\", \"polygon(8.53% 91.47%, 25.31% 91.47%, 8.53% 74.69%)\", \"polygon(91.47% 91.47%, 74.69% 91.47%, 91.47% 74.69%)\", \"polygon(20.14% 20.14%, 20.14% 79.86%, 50% 50%)\", \"polygon(79.86% 20.14%, 79.86% 79.86%, 50% 50%)\", \"polygon(20.14% 20.14%, 79.86% 20.14%, 50% 50%)\", \"polygon(20.14% 79.86%, 79.86% 79.86%, 50% 50%)\", \"polygon(20.14% 25.31%, 25.31% 20.14%, 79.86% 74.69%, 74.69% 79.86%)\", \"polygon(39.07% 55.85%, 44.15% 60.93%, 25.31% 79.86%, 20.14% 74.69%)\", \"polygon(60.93% 55.85%, 55.85% 60.93%, 74.69% 79.86%, 79.86% 74.69%)\", \"polygon(60.93% 44.15%, 55.89% 39.07%, 74.69% 20.14%, 79.86% 25.31%)\", \"polygon(39.07% 44.15%, 44.11% 39.07%, 25.31% 20.14%, 20.14% 25.31%)\", \"polygon(25.31% 8.53%, 25.31% 20.14%, 20.14% 25.31%, 8.53% 25.31%)\", \"polygon(74.69% 8.53%, 74.69% 20.14%, 79.86% 25.31%, 91.47% 25.31%)\", \"polygon(25.31% 91.47%, 25.31% 79.86%, 20.14% 74.69%, 8.53% 74.69%)\", \"polygon(74.69% 91.47%, 74.69% 79.86%, 79.86% 74.69%, 91.47% 74.69%)\", \"polygon(25.31% 8.53%, 25.31% 20.14%, 55.04% 50%, 60.93% 44.15%)\", \"polygon(74.69% 91.47%, 74.69% 79.86%, 44.96% 50%, 39.07% 55.85%)\", \"polygon(79.86% 74.69%, 91.47% 74.69%, 66.78% 50%, 60.93% 55.89%)\", \"polygon(20.14% 25.31%, 8.53% 25.31%, 33.22% 50%, 39.07% 44.11%)\", \"polygon(91.47% 25.31%, 79.86% 25.31%, 50% 55.04%, 55.85% 60.93%)\", \"polygon(25.31% 79.86%, 25.31% 91.47%, 50% 66.78%, 44.11% 60.93%)\", \"polygon(8.53% 74.69%, 20.14% 74.69%, 50% 44.96%, 44.15% 39.07%)\", \"polygon(74.69% 20.14%, 74.69% 8.53%, 50% 33.22%, 55.89% 39.07%)\", \"polygon(0 0, 91.47% 0, 91.47% 8.53%, 0 8.53%)\", \"polygon(91.47% 0, 100% 0, 100% 91.47%, 91.47% 91.47%)\", \"polygon(100% 91.47%, 100% 100%, 8.53% 100%, 8.53% 91.47%)\", \"polygon(0 8.53%, 8.53% 8.53%, 8.53% 100%, 0 100%)\"],\n  \"area\": [0.0161, 0.0161, 0.0161, 0.0161, 0.0050, 0.0050, 0.0050, 0.0050, 0.0065, 0.0065, 0.0065, 0.0065, 0.0069, 0.0069, 0.0069, 0.0069, 0.0018, 0.0045, 0.0045, 0.0045, 0.0045, 0.0137, 0.0137, 0.0093, 0.0093, 0.0137, 0.0093, 0.0137, 0.0093, 0.0275, 0.0275, 0.0275, 0.0275],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/23\",\n  \"bg\": require('../images/bg/ming/23/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%, 0 50%)\", \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\", \"polygon(100% 50%, 100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 50%, 0 100%, 50% 100%, 50% 50%)\", \"polygon(50% 15.32%, 15.32% 50%, 50% 50%)\", \"polygon(50% 15.32%, 84.68% 50%, 50% 50%)\", \"polygon(50% 84.68%, 15.32% 50%, 50% 50%)\", \"polygon(84.68% 50%, 50% 84.68%, 50% 50%)\", \"polygon(50% 15.32%, 54.84% 20.16%, 50% 25%, 45.16% 20.16%)\", \"polygon(50% 84.68%, 54.84% 79.84%, 50% 75%, 45.16% 79.84%)\", \"polygon(15.32% 50%, 20.16% 54.84%, 25% 50%, 20.16% 45.16%)\", \"polygon(84.68% 50%, 79.84% 54.84%, 75% 50%, 79.84% 45.16%)\", \"polygon(50% 25%, 25% 50%, 50% 75%, 75% 50%)\", \"polygon(0 0, 91.94% 0, 91.94% 8.06%, 0 8.06%)\", \"polygon(91.94% 0, 100% 0, 100% 91.94%, 91.94% 91.94%)\", \"polygon(100% 91.94%, 100% 100%, 8.06% 100%, 8.06% 91.94%)\", \"polygon(0 8.06%, 8.06% 8.06%, 8.06% 100%, 0 100%)\"],\n  \"area\": [0.0405, 0.0405, 0.0405, 0.0405, 0.0087, 0.0087, 0.0087, 0.0087, 0.0016, 0.0016, 0.0016, 0.0016, 0.0471, 0.0275, 0.0275, 0.0275, 0.0275],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/25\",\n  \"bg\": require('../images/bg/ming/25/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(50% 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 0, 0 50%, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(35.6% 0, 50% 0, 50% 14.39%, 32.195% 32.195%, 17.8% 17.8%)\", \"polygon(64.4% 0, 50% 0, 50% 14.39%, 67.805% 32.195%, 82.2% 17.8%)\", \"polygon(35.6% 100%, 50% 100%, 50% 85.61%, 32.195% 67.805%, 17.8% 82.2%)\", \"polygon(64.4% 100%, 50% 100%, 50% 85.61%, 67.805% 67.805%, 82.2% 82.2%)\", \"polygon(14.39% 50%, 37.195% 27.195%, 37.195% 72.805%)\", \"polygon(85.61% 50%, 62.805% 27.195%, 62.805% 72.805%)\", \"polygon(50% 14.39%, 72.805% 37.195%, 27.195% 37.195%)\", \"polygon(50% 85.61%, 72.805% 62.805%, 27.195% 62.805%)\", \"polygon(17.8% 17.8%, 32.195% 32.195%, 14.39% 50%, 0 50%, 0 35.6%)\", \"polygon(82.2% 17.8%, 67.805% 32.195%, 85.61% 50%, 100% 50%, 100% 35.6%)\", \"polygon(17.8% 82.2%, 32.195% 67.805%, 14.39% 50%, 0 50%, 0 64.4%)\", \"polygon(82.2% 82.2%, 67.805% 67.805%, 85.61% 50%, 100% 50%, 100% 64.4%)\", \"polygon(0 0, 13.45% 0, 50% 36.55%,50% 50%, 36.55% 50%, 0 13.45%)\", \"polygon(100% 0, 86.55% 0, 50% 36.55%, 50% 50%, 63.45% 50%, 100% 13.45%)\", \"polygon(100% 100%, 86.55% 100%, 50% 63.45%, 50% 50%, 63.45% 50%, 100% 86.55%)\", \"polygon(0 100%, 13.45% 100%, 50% 63.45%, 50% 50%, 36.55% 50%, 0 86.55%)\"],\n  \"area\": [0.0045, 0.0045, 0.0045, 0.0045, 0.0045, 0.0045, 0.0045, 0.0045, 0.0152, 0.0152, 0.0152, 0.0152, 0.0090, 0.0090, 0.0090, 0.0090, 0.0152, 0.0152, 0.0152, 0.0152, 0.0417, 0.0417, 0.0417, 0.0417],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/28\",\n  \"bg\": require('../images/bg/ming/28/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%, 0 50%)\", \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\", \"polygon(100% 50%, 100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 50%, 0 100%, 50% 100%, 50% 50%)\", \"polygon(25.2% 0, 31.3% 0, 43.42% 10.72%, 40.8% 14.2%)\", \"polygon(100% 25.2%, 100% 31.3%, 89.28% 43.42%, 85.8% 40.8%)\", \"polygon(74.8% 100%, 68.7% 100%, 56.38% 89.28%, 59.2% 85.8%)\", \"polygon(0 74.8%, 0 68.7%, 10.72% 56.38%, 14.2% 59.2%)\", \"polygon(47.2% 9.54%, 53.05% 9.54%, 19.2% 43.6%, 16.3% 40.5%)\", \"polygon(52.8% 90.46%, 46.95% 90.46%, 80.8% 56.4%, 83.7% 59.5%)\", \"polygon(9.54% 52.8%, 9.54% 46.95%, 43.6% 80.8%, 40.5% 83.7%)\", \"polygon(90.46% 47.2%, 90.46% 53.05%, 56.4% 19.2%, 59.5% 16.3%)\", \"polygon(67.5% 0, 73.5% 0, 53.05% 20.5%, 47% 20.5%)\", \"polygon(32.5% 100%, 26.5% 100%, 46.95% 79.5%, 53% 79.5%)\", \"polygon(100% 67.5%, 100% 73.5%, 79.5% 53.05%, 79.5% 47%)\", \"polygon(0 32.5%, 0 26.5%, 20.5% 46.95%, 20.5% 53%)\", \"polygon(0 0, 22.8% 0, 39.2% 15%, 15% 39.2%, 0 24%)\", \"polygon(100% 0, 76% 0, 60.8% 15%, 85% 39.2%, 100% 24%)\", \"polygon(0 100%, 24% 100%, 39.2% 85%, 15% 60.8%, 0 76%)\", \"polygon(100% 100%, 76% 100%, 60.8% 85%, 85% 60.8%, 100% 76%)\", \"polygon(22.8% 0, 25.2% 0, 40.8% 14.2%, 39.2% 15%)\", \"polygon(100% 22.8%, 100% 25.2%, 85.8% 40.8%, 85% 39.2%)\", \"polygon(0 77.2%, 0 74.8%, 14.2% 59.2%, 15% 60.8%)\", \"polygon(77.2% 100%, 74.8% 100%, 59.2% 85.8%, 60.8% 85%)\", \"polygon(31.3% 0, 33.9% 0, 44.6% 9.54%, 43.42% 10.72%)\", \"polygon(100% 31.3%, 100% 33.9%, 90.46% 44.6%, 89.28% 43.42%)\", \"polygon(0 68.7%, 0 66.1%, 9.54% 55.4%, 10.72% 56.58%)\", \"polygon(68.7% 100%, 66.1% 100%, 55.4% 90.46%, 56.58% 89.28%)\", \"polygon(44.6% 9.54%, 47.2% 9.54%, 16.3% 40.5%, 15% 39.3%)\", \"polygon(90.46% 44.6%, 90.46% 47.2%, 59.5% 16.3%, 60.7% 15%)\", \"polygon(9.54% 55.4%, 9.54% 52.8%, 40.5% 83.7%, 39.3% 85%)\", \"polygon(55.4% 90.46%, 52.8% 90.46%, 83.7% 59.5%, 85% 60.7%)\", \"polygon(53.05% 9.54%, 56% 9.54%, 20.5% 44.6%, 19.2% 43.6%)\", \"polygon(90.46% 53.05%, 90.46% 56%, 55.4% 20.5%, 56.4% 19.2%)\", \"polygon(9.54% 46.95%, 9.54% 44%, 44.6% 79.5%, 43.6% 80.8%)\", \"polygon(46.95% 90.46%, 44% 90.46%, 79.5% 55.4%, 80.8% 56.4%)\", \"polygon(44.8% 20.5%, 47% 20.5%, 67.5% 0, 65% 0)\", \"polygon(79.5% 44.8%, 79.5% 47%, 100% 67.5%, 100% 65%)\", \"polygon(20.5% 55.2%, 20.5% 53%, 0 32.5%, 0 35%)\", \"polygon(55.2% 79.5%, 53% 79.5%, 32.5% 100%, 35% 100%)\", \"polygon(53.05% 20.5%, 55.4% 20.5%, 76% 0, 73.5% 0)\", \"polygon(79.5% 53.05%, 79.5% 55.4%, 100% 76%, 100% 73.5%)\", \"polygon(20.5% 46.95%, 20.5% 44.6%, 0 24%, 0 26.5%)\", \"polygon(46.95% 79.5%, 44.6% 79.5%, 24% 100%, 26.5% 100%)\", \"polygon(33.9% 0, 65% 0, 55.6% 9.54%, 44.6% 9.54%)\", \"polygon(100% 33.9%, 100% 65%, 90.46% 55.6%, 90.46% 44.6%)\", \"polygon(0 66.1%, 0 35%, 9.54% 44.4%, 9.54% 55.4%)\", \"polygon(66.1% 100%, 35% 100%, 44.4% 90.46%, 55.4% 90.46%)\"],\n  \"area\": [0.0045, 0.0045, 0.0045, 0.0045, 0.0152, 0.0152, 0.0152, 0.0152, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0045, 0.0045, 0.0045, 0.0045, 0.0090, 0.0090, 0.0090, 0.0090, 0.0152, 0.0152, 0.0152, 0.0152, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0090, 0.0417, 0.0417, 0.0417, 0.0417],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/34\",\n  \"bg\": require('../images/bg/ming/34/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(50% 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 0, 0 50%, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(8.33% 8.33%, 13.89% 8.33%, 52.78% 47.22%, 47.22% 52.78%, 8.33% 13.89%)\", \"polygon(47.22% 52.78%, 52.78% 47.22%, 91.67% 86.11%, 91.67% 91.67%, 86.11% 91.67%)\", \"polygon(91.67% 8.33%, 86.11% 8.33%, 47.22% 47.22%, 52.78% 52.78%, 91.67% 13.89%)\", \"polygon(52.78% 52.78%, 47.22% 47.22%, 8.33% 86.11%, 8.33% 91.67%, 13.89% 91.67%)\", \"polygon(50% 0, 0 50%, 50% 50%)\", \"polygon(50% 0, 100% 50%, 50% 50%)\", \"polygon(50% 100%, 0 50%, 50% 50%)\", \"polygon(50% 100%, 100% 50%, 50% 50%)\", \"polygon(50% 13.89%, 31.945% 31.945%, 50% 50%)\", \"polygon(50% 13.89%, 68.055% 31.945%, 50% 50%)\", \"polygon(50% 86.11%, 31.945% 68.055%, 50% 50%)\", \"polygon(50% 86.11%, 68.055% 68.055%, 50% 50%)\", \"polygon(13.89% 50%, 31.945% 31.945%, 50% 50%)\", \"polygon(86.11% 50%, 68.055% 31.945%, 50% 50%)\", \"polygon(13.89% 50%, 31.945% 68.055%, 50% 50%)\", \"polygon(86.11% 50%, 68.055% 68.055%, 50% 50%)\", \"polygon(31.94% 31.94%, 50% 31.94%, 50% 50%)\", \"polygon(68.06% 31.94%, 50% 31.94%, 50% 50%)\", \"polygon(68.06% 68.06%, 50% 68.06%, 50% 50%)\", \"polygon(31.94% 68.06%, 50% 68.06%, 50% 50%)\", \"polygon(31.94% 31.94%, 31.94% 50%, 50% 50%)\", \"polygon(68.06% 31.94%, 68.06% 50%, 50% 50%)\", \"polygon(31.94% 68.06%, 31.94% 50%, 50% 50%)\", \"polygon(68.06% 68.06%, 68.06% 50%, 50% 50%)\", \"polygon(40.28% 40.28%, 59.72% 40.28%, 59.72% 59.72%, 40.28% 59.72%)\", \"polygon(8.33% 0, 100% 0, 100% 8.33%, 8.33% 8.33%)\", \"polygon(91.67% 8.33%, 100% 8.33%, 100% 100%, 91.67% 100%)\", \"polygon(0 91.67%, 91.67% 91.67%, 91.67% 100%, 0 100%)\", \"polygon(0 0, 8.33% 0, 8.33% 91.67%, 0 91.67%)\"],\n  \"area\": [0.0088, 0.0088, 0.0088, 0.0088, 0.0088, 0.0088, 0.0088, 0.0088, 0.0069, 0.0069, 0.0069, 0.0069, 0.0156, 0.0156, 0.0156, 0.0156, 0.0060, 0.0060, 0.0060, 0.0060, 0.0060, 0.0060, 0.0060, 0.0060, 0.0043, 0.0043, 0.0043, 0.0043, 0.0043, 0.0043, 0.0043, 0.0043, 0.0131, 0.0266, 0.0266, 0.0266, 0.0266],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/38\",\n  \"bg\": require('../images/bg/ming/38/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(50% 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 0, 0 50%, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(15.04% 15.04%, 50% 15.04%, 50% 50%)\", \"polygon(84.96% 15.04%, 50% 15.04%, 50% 50%)\", \"polygon(15.04% 84.96%, 50% 84.96%, 50% 50%)\", \"polygon(84.96% 84.96%, 50% 84.96%, 50% 50%)\", \"polygon(15.04% 15.04%, 15.04% 50%, 50% 50%)\", \"polygon(84.96% 15.04%, 84.96% 50%, 50% 50%)\", \"polygon(15.04% 84.96%, 15.04% 50%, 50% 50%)\", \"polygon(84.96% 84.96%, 84.96% 50%, 50% 50%)\", \"polygon(50% 0, 34.96% 15.04%, 50% 50%)\", \"polygon(50% 0, 65.04% 15.04%, 50% 50%)\", \"polygon(50% 100%, 34.96% 84.96%, 50% 50%)\", \"polygon(50% 100%, 65.04% 84.96%, 50% 50%)\", \"polygon(0 50%, 15.04% 34.96%, 50% 50%)\", \"polygon(0 50%, 15.04% 65.04%, 50% 50%)\", \"polygon(100% 50%, 84.96% 34.96%, 50% 50%)\", \"polygon(100% 50%, 84.96% 65.04%, 50% 50%)\"],\n  \"area\": [0.0187, 0.0187, 0.0187, 0.0187, 0.0187, 0.0187, 0.0187, 0.0187, 0.0132, 0.0132, 0.0132, 0.0132, 0.0132, 0.0132, 0.0132, 0.0132, 0.0131, 0.0131, 0.0131, 0.0131, 0.0131, 0.0131, 0.0131, 0.0131],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/39\",\n  \"bg\": require('../images/bg/ming/39/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(50% 0, 100% 0, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(0 0, 0 50%, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(50% 0, 34.85% 13.48%, 50% 50%)\", \"polygon(50% 0, 65.15% 13.48%, 50% 50%)\", \"polygon(50% 100%, 34.85% 86.52%, 50% 50%)\", \"polygon(50% 100%, 65.15% 86.52%, 50% 50%)\", \"polygon(0 50%, 13.48% 34.85%, 50% 50%)\", \"polygon(0 50%, 13.48% 65.15%, 50% 50%)\", \"polygon(100% 50%, 86.52% 34.85%, 50% 50%)\", \"polygon(100% 50%, 86.52% 65.15%, 50% 50%)\", \"polygon(0 0, 34.85% 13.48%, 50% 50%)\", \"polygon(0 0, 13.48% 34.85%, 50% 50%)\", \"polygon(100% 0, 65.15% 13.48%, 50% 50%)\", \"polygon(100% 0, 86.52% 34.85%, 50% 50%)\", \"polygon(0 100%, 34.85% 86.52%, 50% 50%)\", \"polygon(0 100%, 13.48% 65.15%, 50% 50%)\", \"polygon(100% 100%, 65.15% 86.52%, 50% 50%)\", \"polygon(100% 100%, 86.52% 65.15%, 50% 50%)\"],\n  \"area\": [0.0120, 0.0120, 0.0120, 0.0120, 0.0120, 0.0120, 0.0120, 0.0120, 0.0137, 0.0137, 0.0137, 0.0137, 0.0137, 0.0137, 0.0137, 0.0137, 0.0193, 0.0193, 0.0193, 0.0193, 0.0193, 0.0193, 0.0193, 0.0193],\n  \"profit\": 450\n}, {\n  \"path\": \"ming/40\",\n  \"bg\": require('../images/bg/ming/40/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%, 0 50%)\", \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%, 0 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%, 100% 50%)\", \"polygon(11.32% 11.32%, 22.64% 22.64%, 22.64% 50%, 11.32% 50%)\", \"polygon(11.32% 50%, 22.64% 50%, 22.64% 77.36%, 11.32% 88.68%)\", \"polygon(88.68% 11.32%, 77.36% 22.64%, 77.36% 50%, 88.68% 50%)\", \"polygon(88.68% 50%, 77.36% 50%, 77.36% 77.36%, 88.68% 88.68%)\", \"polygon(11.32% 11.32%, 22.64% 22.64%, 50% 22.64%, 50% 11.32%)\", \"polygon(50% 11.32%, 50% 22.64%, 77.36% 22.64%, 88.68% 11.32%)\", \"polygon(11.32% 88.68%, 22.64% 77.36%, 50% 77.36%, 50% 88.68%)\", \"polygon(50% 88.68%, 50% 77.36%, 77.36% 77.36%, 88.68% 88.68%)\", \"polygon(50% 11.32%, 38.68% 22.64%, 50% 50%, 61.32% 22.64%)\", \"polygon(50% 88.68%, 38.68% 77.36%, 50% 50%, 61.32% 77.36%)\", \"polygon(11.32% 50%, 22.64% 38.68%, 50% 50%, 22.64% 61.32%)\", \"polygon(88.68% 50%, 77.36% 38.68%, 50% 50%, 77.36% 61.32%)\", \"polygon(0 0, 100% 0, 88.68% 11.32%, 11.32% 11.32%)\", \"polygon(0 100%, 100% 100%, 88.68% 88.68%, 11.32% 88.68%)\", \"polygon(0 0, 11.32% 11.32%, 11.32% 88.68%, 0 100%)\", \"polygon(100% 0, 88.68% 11.32%, 88.68% 88.68%, 100% 100%)\"],\n  \"area\": [0.0157, 0.0157, 0.0157, 0.0157, 0.0112, 0.0112, 0.0112, 0.0112, 0.0112, 0.0112, 0.0112, 0.0112, 0.0157, 0.0157, 0.0157, 0.0157, 0.0362, 0.0362, 0.0362, 0.0362],\n  \"profit\": 450\n}];\nexport const qing = [{\n  \"path\": \"qing/13\",\n  \"bg\": require('../images/bg/qing/13/bg.png'),\n  \"clipPath\": [\"path('M 0 0 L 0 100% L 23.8% 76.2% A 20.2% 20.2% 0 0 1 22.1% 50% A 20.2% 20.2% 0 0 1 23.8% 23.8% Z')\", \"path('M 27.35% 27.35% A 15.15% 15.15% 0 0 0 29.3% 50% L 50% 50% Z')\", \"path('M 29.3% 50% A 15.15% 15.15% 0 0 0 27.35% 72.65% L 50% 50% Z')\", \"path('M 0 0 L 100% 0 L 76.2% 23.8% A 20.2% 20.2% 0 0 0 50% 22.1% A 20.2% 20.2% 0 0 0 23.8% 23.8% Z')\", \"path('M 27.35% 27.35% A 15.15% 15.15% 0 0 1 50% 29.3% L 50% 50% Z')\", \"path('M 50% 29.3% A 15.15% 15.15% 0 0 1 72.65% 27.35% L 50% 50% Z')\", \"path('M 100% 100% L 0 100% L 23.8% 76.2% A 20.2% 20.2% 0 0 0 50% 77.9% A 20.2% 20.2% 0 0 0 76.2% 76.2% Z')\", \"path('M 72.65% 72.65% A 15.15% 15.15% 0 0 1 50% 70.7% L 50% 50% Z')\", \"path('M 50% 70.7% A 15.15% 15.15% 0 0 1 27.35% 72.65% L 50% 50% Z')\", \"path('M 100% 0 L 100% 100% L 76.2% 76.2% A 20.2% 20.2% 0 0 0 77.9% 50% A 20.2% 20.2% 0 0 0 76.2% 23.8% Z')\", \"path('M 72.65% 27.35% A 15.15% 15.15% 0 0 1 70.7% 50% L 50% 50% Z')\", \"path('M 70.7% 50% A 15.15% 15.15% 0 0 1 72.65% 72.65% L 50% 50% Z')\", \"path('M 22.1% 50% A 20.2% 20.2% 0 0 1 23.8% 23.8% L 27.35% 27.35% A 15.15% 15.15% 0 0 0 29.3% 50% Z')\", \"path('M 23.8% 23.8% A 20.2% 20.2% 0 0 1 50% 22.1% L 50% 29.3% A 15.15% 15.15% 0 0 0 27.35% 27.35% Z')\", \"path('M 50% 22.1% A 20.2% 20.2% 0 0 1 76.2% 23.8% L 72.65% 27.35% A 15.15% 15.15% 0 0 0 50% 29.3% Z')\", \"path('M 76.2% 23.8% A 20.2% 20.2% 0 0 1 77.9% 50% L 70.7% 50% A 15.15% 15.15% 0 0 0 72.65% 27.35% Z')\", \"path('M 77.9% 50% A 20.2% 20.2% 0 0 1 76.2% 76.2% L 72.65% 72.65% A 15.15% 15.15% 0 0 0 70.7% 50% Z')\", \"path('M 76.2% 76.2% A 20.2% 20.2% 0 0 1 50% 77.9% L 50% 70.7% A 15.15% 15.15% 0 0 0 72.65% 72.65% Z')\", \"path('M 50% 77.9% A 20.2% 20.2% 0 0 1 23.8% 76.2% L 27.35% 72.65% A 15.15% 15.15% 0 0 0 50% 70.7% Z')\", \"path('M 23.8% 76.2% A 20.2% 20.2% 0 0 1 22.1% 50% L 29.3% 50% A 15.15% 15.15% 0 0 0 27.35% 72.65% Z')\"],\n  \"area\": [0.0574, 0.0119, 0.0119, 0.0574, 0.0119, 0.0119, 0.0574, 0.0119, 0.0119, 0.0574, 0.0119, 0.0119, 0.0044, 0.0044, 0.0044, 0.0044, 0.0044, 0.0044, 0.0044, 0.0044],\n  \"profit\": 500\n}, {\n  \"path\": \"qing/30\",\n  \"bg\": require('../images/bg/qing/30/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 50% 0, 50% 50%)\", \"polygon(0 0, 0 50%, 50% 50%)\", \"polygon(100% 0, 50% 0, 50% 50%)\", \"polygon(100% 0, 100% 50%, 50% 50%)\", \"polygon(0 100%, 50% 100%, 50% 50%)\", \"polygon(0 100%, 0 50%, 50% 50%)\", \"polygon(100% 100%, 50% 100%, 50% 50%)\", \"polygon(100% 100%, 100% 50%, 50% 50%)\", \"polygon(50% 1.7%, 25.85% 25.85%, 50% 50%)\", \"polygon(25.85% 25.85%, 1.7% 50%, 50% 50%)\", \"polygon(50% 1.7%, 74.15% 25.85%, 50% 50%)\", \"polygon(74.15% 25.85%, 98.3% 50%, 50% 50%)\", \"polygon(50% 98.3%, 25.85% 74.15%, 50% 50%)\", \"polygon(25.85% 74.15%, 1.7% 50%, 50% 50%)\", \"polygon(50% 98.3%, 74.15% 74.15%, 50% 50%)\", \"polygon(74.15% 74.15%, 98.3% 50%, 50% 50%)\", \"polygon(50% 12%, 31% 31%, 50% 50%)\", \"polygon(31% 31%, 12% 50%, 50% 50%)\", \"polygon(50% 12%, 69% 31%, 50% 50%)\", \"polygon(69% 31%, 88% 50%, 50% 50%)\", \"polygon(50% 88%, 69% 69%, 50% 50%)\", \"polygon(69% 69%, 88% 50%, 50% 50%)\", \"polygon(50% 88%, 31% 69%, 50% 50%)\", \"polygon(31% 69%, 12% 50%, 50% 50%)\", \"polygon(50% 20%, 35% 35%, 50% 50%)\", \"polygon(35% 35%, 20% 50%, 50% 50%)\", \"polygon(50% 20%, 65% 35%, 50% 50%)\", \"polygon(65% 35%, 80% 50%, 50% 50%)\", \"polygon(50% 80%, 65% 65%, 50% 50%)\", \"polygon(65% 65%, 80% 50%, 50% 50%)\", \"polygon(50% 80%, 35% 65%, 50% 50%)\", \"polygon(35% 65%, 20% 50%, 50% 50%)\", \"path('M 28.5% 28.5% A 26% 26% 0 0 1 50% 50% Z')\", \"path('M 28.5% 28.5% A 26% 26% 0 0 0 50% 50% Z')\", \"path('M 71.5% 28.5% A 26% 26% 0 0 1 50% 50% Z')\", \"path('M 71.5% 28.5% A 26% 26% 0 0 0 50% 50% Z')\", \"path('M 28.5% 71.5% A 26% 26% 0 0 1 50% 50% Z')\", \"path('M 28.5% 71.5% A 26% 26% 0 0 0 50% 50% Z')\", \"path('M 71.5% 71.5% A 26% 26% 0 0 1 50% 50% Z')\", \"path('M 71.5% 71.5% A 26% 26% 0 0 0 50% 50% Z')\", \"polygon(7.1% 7.1%, 15.5% 7.9%, 20.8% 20.8%, 7.9% 15.5%)\", \"polygon(7.1% 92.9%, 15.5% 92.1%, 20.8% 79.2%, 7.9% 84.5%)\", \"polygon(92.9% 92.9%, 84.5% 92.1%, 79.2% 79.2%, 92.1% 84.5%)\", \"polygon(92.9% 7.1%, 84.5% 7.9%, 79.2% 20.8%, 92.1% 15.5%)\"],\n  \"area\": [0.0206, 0.0206, 0.0206, 0.0206, 0.0206, 0.0206, 0.0206, 0.0206, 0.0082, 0.0082, 0.0082, 0.0082, 0.0082, 0.0082, 0.0082, 0.0082, 0.0046, 0.0046, 0.0046, 0.0046, 0.0046, 0.0046, 0.0046, 0.0046, 0.0061, 0.0061, 0.0061, 0.0061, 0.0061, 0.0061, 0.0061, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037],\n  \"profit\": 500\n}, {\n  \"path\": \"qing/35\",\n  \"bg\": require('../images/bg/qing/35/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 0% 2.65%, 90.15% 2.65%, 90.15% 0%)\", \"polygon(0 9.85%, 0% 7.2%, 90.15% 7.2%, 90.15% 9.85%)\", \"polygon(90.15% 0, 92.8% 0, 92.8% 90.15%, 90.15% 90.15%)\", \"polygon(97.35% 0, 100% 0, 100% 90.15%, 97.35% 90.15%)\", \"polygon(100% 90.15%, 100% 92.8%, 9.85% 92.8%, 9.85% 90.15%)\", \"polygon(100% 100%, 100% 97.35%, 9.85% 97.35%, 9.85% 100%)\", \"polygon(0 9.85%, 2.65% 9.85%, 2.65% 100%, 0 100%)\", \"polygon(9.85% 9.85%, 7.2% 9.85%, 7.2% 100%, 9.85% 100%)\", \"polygon(0 2.65%, 0 7.2%, 90.15% 7.2%, 90.15% 2.65%)\", \"polygon(97.35% 0, 92.8% 0, 92.8% 90.15%, 97.35% 90.15%)\", \"polygon(100% 97.35%, 100% 92.8%, 9.85% 92.8%, 9.85% 97.35%)\", \"polygon(2.65% 100%, 7.2% 100%, 7.2% 9.85%, 2.65% 9.85%)\", \"path('M 33.3% 33.3% A 3.3% 3.3% 0 0 0 33.3% 38% A 17.6% 17.6% 0 0 1 33.3% 62% A 3.3% 3.3% 0 0 0 33.3% 66.7% L 50% 50% Z')\", \"path('M 33.3% 33.3% A 3.3% 3.3% 0 0 1 38% 33.3% A 17.6% 17.6% 0 0 0 62% 33.3% A 3.3% 3.3% 0 0 1 66.7% 33.3% L 50% 50% Z')\", \"path('M 33.3% 66.7% A 3.3% 3.3% 0 0 0 38% 66.7% A 17.6% 17.6% 0 0 1 62% 66.7% A 3.3% 3.3% 0 0 0 66.7% 66.7% L 50% 50% Z')\", \"path('M 66.7% 33.3% A 3.3% 3.3% 0 0 1 66.7% 38% A 17.6% 17.6% 0 0 0 66.7% 62% A 3.3% 3.3% 0 0 1 66.7% 66.7% L 50% 50% Z')\", \"path('M 9.85% 9.85% L 33.3% 33.3% A 3.3% 3.3% 0 0 0 33.3% 38% A 17.6% 17.6% 0 0 1 33.3% 62% A 3.3% 3.3% 0 0 0 33.3% 66.7% L 9.85% 90.15% Z')\", \"path('M 9.85% 9.85% L 33.3% 33.3% A 3.3% 3.3% 0 0 1 38% 33.3% A 17.6% 17.6% 0 0 0 62% 33.3% A 3.3% 3.3% 0 0 1 66.7% 33.3% L 90.15% 9.85% Z')\", \"path('M 9.85% 90.15% L 33.3% 66.7% A 3.3% 3.3% 0 0 0 38% 66.7% A 17.6% 17.6% 0 0 1 62% 66.7% A 3.3% 3.3% 0 0 0 66.7% 66.7% L 90.15% 90.15% Z')\", \"path('M 90.15% 9.85% L 66.7% 33.3% A 3.3% 3.3% 0 0 1 66.7% 38% A 17.6% 17.6% 0 0 0 66.7% 62% A 3.3% 3.3% 0 0 1 66.7% 66.7% L 90.15% 90.15% Z')\"],\n  \"area\": [0.0081, 0.0081, 0.0081, 0.0081, 0.0081, 0.0081, 0.0081, 0.0081, 0.0158, 0.0158, 0.0158, 0.0158, 0.0074, 0.0074, 0.0074, 0.0074, 0.0506, 0.0506, 0.0506, 0.0506],\n  \"profit\": 500\n}];\nexport const changgui = [{\n  \"path\": \"changgui/人字拼\",\n  \"bg\": require('../images/bg/changgui/人字拼/bg.png'),\n  \"clipPath\": [\"polygon(0 0, 12.5% 0, 12.5% 60%, 0 60%)\", \"polygon(25% 12.5%, 25% 25%, 85% 25%, 85% 12.5%)\", \"polygon(37.5% 37.5%, 50% 37.5%, 50% 97.5%, 37.5% 97.5%)\", \"polygon(62.5% 50%, 62.5% 62.5%, 100% 62.5%, 100% 50%)\", \"polygon(75% 75%, 87.5% 75%, 87.5% 100%, 75% 100%)\", \"polygon(0 72.5%, 25% 72.5%, 25% 85%, 0 85%)\", \"polygon(12.5% 0, 12.5% 12.5%, 72.5% 12.5%, 72.5% 0)\", \"polygon(25% 25%, 37.5% 25% , 37.5% 85%, 25% 85%)\", \"polygon(50% 37.5%, 100% 37.5%, 100% 50%, 50% 50%)\", \"polygon(62.5% 62.5%, 75% 62.5%, 75% 100%, 62.5% 100%)\", \"polygon(87.5% 75%, 87.5% 87.5%, 100% 87.5%, 100% 75%)\", \"polygon(0 60%, 12.5% 60%, 12.5% 72.5%, 0 72.5%)\", \"polygon(0 97.5%, 50% 97.5%, 50% 100%, 0 100%)\", \"polygon(85% 0, 97.5% 0, 97.5% 25%, 85% 25%)\", \"polygon(12.5% 12.5%, 25% 12.5%, 25% 72.5%, 12.5% 72.5%)\", \"polygon(37.5% 25%, 37.5% 37.5%, 97.5% 37.5%, 97.5% 25%)\", \"polygon(50% 50%, 62.5% 50%, 62.5% 100%, 50% 100%)\", \"polygon(75% 62.5%, 75% 75%, 100% 75%, 100% 62.5%)\", \"polygon(87.5% 87.5%, 100% 87.5%, 100% 100%, 87.5% 100%)\", \"polygon(0 85%, 37.5% 85%, 37.5% 97.5%, 0 97.5%)\", \"polygon(72.5% 0, 85% 0, 85% 12.5%, 72.5% 12.5%)\", \"polygon(97.5% 0, 100% 0, 100% 37.5%, 97.5% 37.5%)\"]\n}, {\n  \"path\": \"changgui/鱼骨拼\",\n  \"bg\": require('../images/bg/changgui/鱼骨拼/bg.png'),\n  \"clipPath\": [\"polygon(50% 0, 50% 17.7%, 0 67.7%, 0 50%)\", \"polygon(50% 17.7%, 50% 35.4%, 100% 85.4%, 100% 67.7%)\", \"polygon(50% 53.1%, 50% 70.8%, 0 120.8%, 0 103.1%)\", \"polygon(50% 70.8%, 50% 88.5%, 100% 138.5%, 100% 120.8%)\", \"polygon(50% -35.4%, 50% -17.7%, 0 32.3%, 0 14.6%)\", \"polygon(50% -53.1%, 50% -35.4%, 100% 14.6%, 100% -3.1%)\", \"polygon(50% 0, 50% 17.7%, 100% 67.7%, 100% 50%)\", \"polygon(50% 35.4%, 50% 53.1%, 0 103.1%, 0 85.4%)\", \"polygon(50% 53.1%, 50% 70.8%, 100% 120.8%, 100% 103.1%)\", \"polygon(50% 88.5%, 50% 106.2%, 0 156.2%, 0 138.5%)\", \"polygon(50% -17.7%, 50% 0, 0 50%, 0 32.3%)\", \"polygon(50% -35.4%, 50% -17.7%, 100% 32.3%, 100% 14.6%)\", \"polygon(50% 17.7%, 50% 35.4%, 0 85.4%, 0 67.7%)\", \"polygon(50% 35.4%, 50% 53.1%, 100% 103.1%, 100% 85.4%)\", \"polygon(50% 70.8%, 50% 88.5%, 0 138.5%, 0 120.8%)\", \"polygon(50% 88.5%, 50% 106.2%, 100% 156.2%, 100% 138.5%)\", \"polygon(50% -17.7%, 50% 0, 100% 50%, 100% 32.3%)\", \"polygon(50% -53.1%, 50% -35.4%, 0 14.6%, 0 -3.1%)\"]\n}, {\n  \"path\": \"changgui/长条\",\n  \"bg\": require('../images/bg/changgui/长条/bg.png'),\n  \"clipPath\": [\"polygon(0 10.42%,100% 10.42%, 100% 20.84%, 0 20.84%)\", \"polygon(0 52.1%,100% 52.1%, 100% 62.52%, 0 62.52%)\", \"polygon(0 93.78%,100% 93.78%, 100% 100%, 0 100%)\", \"polygon(0 0,50% 0, 50% 10.42%, 0 10.42%)\", \"polygon(0 31.26%,50% 31.26%, 50% 20.84%, 0 20.84%)\", \"polygon(0 41.68%,50% 41.68%, 50% 52.1%, 0 52.1%)\", \"polygon(0 62.52%,50% 62.52%, 50% 72.94%, 0 72.94%)\", \"polygon(0 83.36%,50% 83.36%, 50% 93.78%, 0 93.78%)\", \"polygon(0 31.26%,100% 31.26%, 100% 41.68%, 0 41.68%)\", \"polygon(0 72.94%,100% 72.94%, 100% 83.36%, 0 83.36%)\", \"polygon(50% 0,100% 0, 100% 10.42%, 50% 10.42%)\", \"polygon(50% 31.26%,100% 31.26%, 100% 20.84%, 50% 20.84%)\", \"polygon(50% 41.68%,100% 41.68%, 100% 52.1%, 50% 52.1%)\", \"polygon(50% 62.52%,100% 62.52%, 100% 72.94%, 50% 72.94%)\", \"polygon(50% 83.36%,100% 83.36%, 100% 93.78%, 50% 93.78%)\", \"polygon(25% 10.42%,75% 10.42%, 75% 20.84%, 25% 20.84%)\", \"polygon(25% 31.26%,75% 31.26%, 75% 41.68%, 25% 41.68%)\", \"polygon(25% 52.1%,75% 52.1%, 75% 62.52%, 25% 62.52%)\", \"polygon(25% 72.94%,75% 72.94%, 75% 83.36%, 25% 83.36%)\", \"polygon(25% 93.78%,75% 93.78%, 75% 104.2%, 25% 104.2%)\"]\n}];", "map": {"version": 3, "names": ["woodImages", "require", "price", "desc", "img", "scientificName", "origin", "woodProperty", "purpose", "empty", "tang", "song", "yuan", "ming", "qing", "chang<PERSON>i"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Source.js"], "sourcesContent": ["export const woodImages = [\n    // {\n    //     \"id\": 1,\n    //     \"src\": require('../images/wood/1-雨树.jpg'),\n    //     \"name\": \"雨树\",\n    //     price: 100,\n    //     desc: {\n    //         img: require('../images/desc/雨树_desc.png'),\n    //         scientificName: '<PERSON><PERSON><PERSON> (Jacq.) Merr.',\n    //         origin: '热带美洲，广植于全世界热带地区',\n    //         woodProperty: '心材褐色，耐腐、轻软、切面平滑有光泽，干燥后不翘不裂',\n    //         purpose: '家具、雕刻、门、窗、箱板等用材'\n    //     }\n    // },\n    {\n        \"id\": 2,\n        \"src\": require(\"../images/wood/2-黑核桃.jpg\"),\n        \"name\": \"黑核桃\",\n        price: 893,\n        desc: {\n            img: require('../images/desc/黑核桃_desc.jpeg'),\n            scientificName: 'BlackWalnut',\n            origin: '亚洲东部至地中海地区﹐同时亦分布在北美、中美洲及安第斯山脉一带',\n            woodProperty: '胡桃木是密度中等的结实的硬木，材质坚硬，细腻均匀，抗弯曲及抗压度中等，不易开裂；抗菌、白蚁的危害、耐腐蚀',\n            purpose: '易于用手工和机械工具加工。适于敲钉、螺钻和胶合'\n        }\n    },\n    // {\n    //     \"id\": 3,\n    //     \"src\": require(\"../images/wood/3-格木.jpg\"),\n    //     \"name\": \"格木\",\n    //     price: 100,\n    //     desc: {\n    //         img: require('../images/desc/格木_desc.jpeg'),\n    //         scientificName: 'Erythrophleum fordii Oliv',\n    //         origin: '非洲',\n    //         woodProperty: '格木为珍贵的硬材树种，木材坚硬，被称为铁木，极耐腐',\n    //         purpose: '为优良的建筑、工艺及家具用材，耐水湿，可供船板、桅插和上等家具等用材'\n    //     }\n    // },\n    {\n        \"id\": 4,\n        \"src\": require(\"../images/wood/4-阔变豆.jpg\"),\n        \"name\": \"阔变豆\",\n        price: 593,\n        desc: {\n            img: require('../images/desc/阔变豆_desc.jpg'),\n            scientificName: 'platymis cium sp',\n            origin: '巴西、圭亚那、哥伦比亚等地',\n            woodProperty: '材质重，强度高，纹理清晰',\n            purpose: '高档家具、地板、乐器小提琴等'\n        }\n    },\n    {\n        \"id\": 5,\n        \"src\": require(\"../images/wood/5-橡木.jpg\"),\n        \"name\": \"橡木\",\n        price: 527,\n        desc: {\n            img: require('../images/desc/橡木_desc.jpg'),\n            scientificName: 'Quercus spp',\n            origin: '欧洲',\n            woodProperty: '质地紧致，手感细腻舒适；纹理顺美，色泽温婉雅致。型材力学强度更高，结构牢固，经久耐用不变形',\n            purpose: '主要用于木门窗、地板、高档装修、高档家具等'\n        }\n    },\n    {\n        \"id\": 6,\n        \"src\": require(\"../images/wood/6-大果紫檀.jpg\"),\n        \"name\": \"大果紫檀\",\n        price: 1343,\n        desc: {\n            img: require('../images/desc/大果紫檀_desc.jpg'),\n            scientificName: 'Padauk',\n            origin: '缅甸、老挝、泰国等地',\n            woodProperty: '密度高，材质重，耐腐性强，具有清香，纹理清晰美观',\n            purpose: '高档家具、地板、汽车装饰等'\n        }\n    },\n    {\n        \"id\": 7,\n        \"src\": require(\"../images/wood/7-阔叶黄檀.jpg\"),\n        \"name\": \"阔叶黄檀\",\n        price: 1760,\n        desc: {\n            img: require('../images/desc/阔叶黄檀_desc.jpeg'),\n            scientificName: 'Dalbergia latifolia',\n            origin: '印度、印度尼西亚等地',\n            woodProperty: '密度高，材质重，耐腐蚀、纹理清晰美观',\n            purpose: '高档红木家具、地板、钢琴等'\n        }\n    },\n    {\n        \"id\": 8,\n        \"src\": require(\"../images/wood/8-军刀豆.jpg\"),\n        \"name\": \"军刀豆\",\n        price: 1427,\n        desc: {\n            img: require('../images/desc/军刀豆_desc.jpg'),\n            scientificName: 'parinari campestris',\n            origin: '印度、印度尼西亚等地',\n            woodProperty: '经济价值高，木材耐腐，能抗海生动物危害，木材加工性能好，具油性感，油漆上蜡性好',\n            purpose: '家具制作用材'\n        }\n    },\n    {\n        \"id\": 9,\n        \"src\": require(\"../images/wood/9-鸡翅木.jpg\"),\n        \"name\": \"鸡翅木\",\n        price: 627,\n        desc: {\n            img: require('../images/desc/鸡翅木_desc.jpg'),\n            scientificName: 'Millettia Laurentii',\n            origin: '非洲',\n            woodProperty: '质较硬，较平滑，易条状剥离。具光泽；有油性感。纹理直；结构粗而不均匀；质重硬；强度高；干缩甚大。加工略难，易钝锯；抛光略难；钉钉须先打孔；弯曲性能极佳。很耐腐',\n            purpose: '适用于高级家具、刨切微薄木、室内装修、地板、细木工、运动器材、雕刻等'\n        }\n    },\n    {\n        \"id\": 10,\n        \"src\": require(\"../images/wood/10-爱里古夷苏木.jpg\"),\n        \"name\": \"爱里古夷苏木\",\n        price: 760,\n        desc: {\n            img: require('../images/desc/爱里古夷苏木_desc.jpeg'),\n            scientificName: 'Newtonia spp. I.alba',\n            origin: '喀麦隆、卢旺达、肯尼亚等',\n            woodProperty: '纹理交错，结甚细而匀；木材重至甚重；干缩中至大，强度高',\n            purpose: '用于建筑、室内装修，家具，单板，胶合板，车辆，造船，包装箱，枕木等'\n        }\n    },\n    // {\n    //     \"id\": 11,\n    //     \"src\": require(\"../images/wood/11-油楠木.jpg\"),\n    //     \"name\": \"油楠木\",\n    //     price: 100,\n    //     desc: {\n    //         img: require('../images/desc/油楠木_desc.jpeg'),\n    //         scientificName: 'Sindora glabra Merr. ex de Wit',\n    //         origin: '马来西亚、东南亚、海南',\n    //         woodProperty: '木材结构略粗，木肌细致，光泽强，极具油性;伸缩性不大，耐久稳定，易加工，可塑性强，心材耐腐蚀',\n    //         purpose: '在古筝、古琴的制作中，油楠木为主材的乐器是最高品质的，还是家具、地板的用材'\n    //     }\n    // },\n    {\n        \"id\": 12,\n        \"src\": require(\"../images/wood/12-印茄木.jpg\"),\n        \"name\": \"印茄木\",\n        price: 527,\n        desc: {\n            img: require('../images/desc/印茄木_desc.jpeg'),\n            scientificName: 'Intsia.spp',\n            origin: '东南亚',\n            woodProperty: '结构粗；质量硬；强度高；干缩甚小。耐腐、干燥慢，但稳定性很好',\n            purpose: '常被用于制作门窗、高级家具室内装饰、门窗柜、车船桥梁、地板，是户外家具、户外地板、扶手楼梯、亭台楼阁和木质建筑的重要用材树种'\n        }\n    },\n    {\n        \"id\": 13,\n        \"src\": require(\"../images/wood/13-柚木.jpg\"),\n        \"name\": \"柚木\",\n        price: 843,\n        desc: {\n            img: require('../images/desc/柚木_desc.jpeg'),\n            scientificName: 'Teak',\n            origin: '缅甸、印尼等地',\n            woodProperty: '油性强，耐腐耐磨、稳定性极强',\n            purpose: '地板、游艇甲板等'\n        }\n    },\n    {\n        \"id\": 14,\n        \"src\": require(\"../images/wood/14-鲍迪豆.jpg\"),\n        \"name\": \"鲍迪豆\",\n        price: 527,\n        desc: {\n            img: require('../images/desc/鲍迪豆_desc.jpg'),\n            scientificName: 'Bowdichia spp.',\n            origin: '南美洲北部',\n            woodProperty: '鲍迪豆的木材为散孔材，心材红褐色至巧克力色，具有深浅相间的带状条纹，与边材界限明显，边材窄且为白色',\n            purpose: '由于鲍迪豆的木材重且硬，加工困难，但其耐腐、耐虫害，常用于制作高级家具、地板等'\n        }\n    },\n    // {\n    //     \"id\": 16,\n    //     \"src\": require(\"../images/wood/16-香脂木豆.jpg\"),\n    //     \"name\": \"香脂木豆\",\n    //     price: 100,\n    //     desc: {\n    //         img: require('../images/desc/香脂木豆_desc.png'),\n    //         scientificName: 'Red Incienso',\n    //         origin: '于巴西、秘鲁、委内瑞拉、阿根廷等地',\n    //         woodProperty: '纹理交错、重硬坚韧、芳香四溢，木材甚稳定，花纹美观，耐久、耐腐耐磨',\n    //         purpose: '适用于建筑、地板、家具、装饰单板、胶合板、车辆、造船、矿柱、枕木、电杆、农具、工具柄、雕刻、车旋制品等'\n    //     }\n    // }\n]\n\nexport const empty = {\n    \"path\": \"empty\",\n    \"clipPath\": []\n}\nexport const tang = [\n    {\n        \"path\": \"tang/1\",\n        \"bg\": require('../images/bg/tang/1/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 50%, 100% 0)\",\n            \"polygon(100% 0, 50% 50%, 100% 100%)\",\n            \"polygon(100% 100%, 50% 50%, 0 100%)\",\n            \"polygon(0 100%, 50% 50%, 0 0)\",\n            \"polygon(50% 50%,60.55% 39.45%,100% 78.9%,100% 100%)\",\n            \"polygon(0 0,21.1% 0,60.55% 39.45%,50% 50%)\",\n            \"polygon(0 0,50% 50%,39.45% 60.55%,0 21.1%)\",\n            \"polygon(50% 50%,100% 100%,78.9% 100%,39.45% 60.55%)\",\n            \"polygon(0 78.9%,0 100%,100% 0,78.9% 0)\",\n            \"polygon(0 100%,21.1% 100%,100% 21.1%,100% 0)\"\n        ],\n        \"area\": [\n            0.02984,\n            0.02984,\n            0.02984,\n            0.02984,\n            0.02600,\n            0.02600,\n            0.02600,\n            0.02600,\n            0.07000,\n            0.07000,\n        ],\n        \"profit\": 300\n    },\n    {\n        \"path\": \"tang/2\",\n        \"bg\": require('../images/bg/tang/2/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 20% 0, 50% 30%, 50% 50%)\",\n            \"polygon(0 0, 0 20%, 30% 50%, 50% 50%)\",\n            \"polygon(100% 0, 80% 0, 50% 30%, 50% 50%)\",\n            \"polygon(100% 0, 100% 20%, 50% 70%, 50% 50%)\",\n            \"polygon(0 100%, 20% 100%, 50% 70%, 50% 50%)\",\n            \"polygon(0 100%, 0 80%, 30% 50%, 50% 50%)\",\n            \"polygon(100% 100%, 80% 100%, 50% 70%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 80%, 70% 50%, 50% 50%)\",\n            \"polygon(20% 0, 35% 15%, 65% 15%, 80% 0)\",\n            \"polygon(0 20%, 15% 35%, 15% 65%, 0 80%)\",\n            \"polygon(20% 100%, 35% 85%, 65% 85%, 80% 100%)\",\n            \"polygon(100% 20%, 85% 35%, 85% 65%, 100% 80%)\",\n            \"polygon(35% 15%, 65% 15%, 50% 30%)\",\n            \"polygon(85% 35%, 85% 65%, 70% 50%)\",\n            \"polygon(35% 85%, 65% 85%, 50% 70%)\",\n            \"polygon(15% 35%, 15% 65%, 30% 50%)\"\n        ],\n        \"area\": [\n            0.0288,\n            0.0288,\n            0.0288,\n            0.0288,\n            0.0288,\n            0.0288,\n            0.0288,\n            0.0288,\n            0.0245,\n            0.0245,\n            0.0245,\n            0.0245,\n            0.0080,\n            0.0080,\n            0.0080,\n            0.0080,\n        ],\n        \"profit\": 300\n    },\n    {\n        \"path\": \"tang/3\",\n        \"bg\": require('../images/bg/tang/3/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 15% 15%, 65% 15%, 80% 0)\",\n            \"polygon(100% 20%, 85% 35%, 85% 85%, 100% 100%)\",\n            \"polygon(100% 100%, 85% 85%, 35% 85%, 20% 100%)\",\n            \"polygon(0 0, 15% 15%, 15% 65%, 0 80%)\",\n            \"polygon(15% 15%, 35% 15%, 85% 65%, 85% 85%)\",\n            \"polygon(15% 15%, 15% 35%, 65% 85%, 85% 85%)\",\n            \"polygon(100% 0, 80% 0, 50% 30%, 60% 40%)\",\n            \"polygon(100% 0, 100% 20%, 70% 50%, 60% 40%)\",\n            \"polygon(0 100%, 20% 100%, 50% 70%, 40% 60%)\",\n            \"polygon(0 100%, 0 80%, 30% 50%, 40% 60%)\",\n            \"polygon(35% 15%, 65% 15%, 50% 30%)\",\n            \"polygon(15% 35%, 15% 65%, 30% 50%)\",\n            \"polygon(35% 85%, 65% 85%, 50% 70%)\",\n            \"polygon(85% 35%, 85% 65%, 70% 50%)\"\n        ],\n        \"area\": [\n            0.0358,\n            0.0358,\n            0.0358,\n            0.0358,\n            0.0415,\n            0.0415,\n            0.0246,\n            0.0246,\n            0.0246,\n            0.0246,\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0088,\n        ],\n        \"profit\": 300\n    },\n    {\n        \"path\": \"tang/8\",\n        \"bg\": require('../images/bg/tang/8/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 100% 100%, 50% 50%)\",\n            \"polygon(0 0, 50% 50%, 0 100%)\",\n            \"polygon(100% 0, 50% 50%, 100% 100%)\",\n            \"polygon(34.4% 15.4%, 50% 31%, 65.6% 15.4%)\",\n            \"polygon(15.4% 34.4%, 31% 50%, 15.4% 65.6%)\",\n            \"polygon(34.4% 84.6%, 50% 69%, 65.6% 84.6%)\",\n            \"polygon(84.6% 34.4%, 69% 50%, 84.6% 65.6%)\",\n            \"polygon(15.4% 15.4%, 34.4% 15.4%, 50% 31%, 50% 50%)\",\n            \"polygon(84.6% 84.6%, 65.6% 84.6%, 50% 69%, 50% 50%)\",\n            \"polygon(84.6% 15.4%, 84.6% 34.4%, 69% 50%, 50% 50%)\",\n            \"polygon(15.4% 84.6%, 15.4% 65.6%, 31% 50%, 50% 50%)\",\n            \"polygon(84.6% 15.4%, 65.6% 15.4%, 50% 31%, 50% 50%)\",\n            \"polygon(15.4% 84.6%, 34.4% 84.6%, 50% 69%, 50% 50%)\",\n            \"polygon(84.6% 84.6%, 84.6% 65.6%, 69% 50%, 50% 50%)\",\n            \"polygon(15.4% 15.4%, 15.4% 34.4%, 31% 50%, 50% 50%)\"\n        ],\n        \"area\": [\n            0.0469,\n            0.0469,\n            0.0469,\n            0.0469,\n            0.0087,\n            0.0087,\n            0.0087,\n            0.0087,\n            0.0172,\n            0.0172,\n            0.0172,\n            0.0172,\n            0.0172,\n            0.0172,\n            0.0172,\n            0.0172,\n        ],\n        \"profit\": 300\n    },\n    {\n        \"path\": \"tang/10\",\n        \"bg\": require('../images/bg/tang/10/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 100% 100%, 50% 50%)\",\n            \"polygon(0 0, 50% 50%, 0 100%)\",\n            \"polygon(100% 0, 50% 50%, 100% 100%)\",\n            \"polygon(34.6% 15.1%, 84.9% 65.4%, 84.9% 15.1%)\",\n            \"polygon(15.1% 34.6%, 65.4% 84.9%, 15.1% 84.9%)\",\n            \"polygon(15.1% 15.1%, 34.6% 15.1%, 84.9% 65.4%, 84.9% 84.9%, 65.4% 84.9%, 15.1% 34.6%)\"\n        ],\n        \"area\": [\n            0.0451,\n            0.0451,\n            0.0451,\n            0.0451,\n            0.0476,\n            0.0476,\n            0.0854,\n        ],\n        \"profit\": 300\n    },\n    {\n        \"path\": \"tang/15\",\n        \"bg\": require('../images/bg/tang/15/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 100% 100%, 50% 50%)\",\n            \"polygon(0 0, 50% 50%, 0 100%)\",\n            \"polygon(100% 0, 50% 50%, 100% 100%)\",\n            \"polygon(14.14% 14.14%, 50% 14.14%, 50% 50%, 14.14% 50%)\",\n            \"polygon(50% 14.14%, 85.86% 14.14%, 85.86% 50%, 50% 50%)\",\n            \"polygon(85.86% 50%, 85.86% 85.86%, 50% 85.86%, 50% 50%)\",\n            \"polygon(14.14% 50%, 50% 50%, 50% 85.86%, 14.14% 85.86%)\"\n        ],\n        \"area\": [\n            0.0324,\n            0.0324,\n            0.0324,\n            0.0324,\n            0.0580,\n            0.0580,\n            0.0580,\n            0.0580,\n        ],\n        \"profit\": 300\n    },\n    {\n        \"path\": \"tang/26\",\n        \"bg\": require('../images/bg/tang/26/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 100% 100%, 50% 50%)\",\n            \"polygon(0 0, 50% 50%, 0 100%)\",\n            \"polygon(100% 0, 50% 50%, 100% 100%)\",\n            \"polygon(0 0, 17.36% 0, 50% 32.64%, 50% 50%, 32.64% 50%, 0% 17.36%)\",\n            \"polygon(82.64% 0, 100% 0, 100% 17.36%, 67.36% 50%, 50% 50%, 50% 32.64%)\",\n            \"polygon(0 100%, 17.36% 100%, 50% 67.36%, 50% 50%, 32.64% 50%, 0 82.64%)\",\n            \"polygon(100% 100%, 100% 82.64%, 67.36% 50%, 50% 50%, 50% 67.36%, 82.64% 100%)\"\n        ],\n        \"area\": [\n            0.0509,\n            0.0509,\n            0.0509,\n            0.0509,\n            0.0391,\n            0.0391,\n            0.0391,\n            0.0391,\n        ],\n        \"profit\": 300\n    }\n]\n\nexport const song = [\n    {\n        \"path\": \"song/4\",\n        \"bg\": require('../images/bg/song/4/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(0 0, 0 50%, 50% 50%)\",\n            \"polygon(100% 0, 50% 0, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(13% 0, 100% 0, 100% 13%, 13% 13%)\",\n            \"polygon(87% 13%, 100% 13%, 100% 100%, 87% 100%)\",\n            \"polygon(0 87%, 87% 87%, 87% 100%, 0 100%)\",\n            \"polygon(0 0, 13% 0, 13% 87%, 0 87%)\",\n            \"polygon(42.5% 13%, 57.5% 13%, 57.5% 87%, 42.5% 87%)\",\n            \"polygon(42.5% 87%, 57.5% 87%, 57.5% 50%, 42.5% 50%)\",\n            \"polygon(13% 42.5%, 13% 57.5%, 50% 57.5%, 50% 42.5%)\",\n            \"polygon(87% 42.5%, 87% 57.5%, 50% 57.5%, 50% 42.5%)\",\n            \"polygon(13% 13%, 23% 13%, 50% 40%, 50% 50%, 40% 50%, 13% 23%)\",\n            \"polygon(13% 87%, 23% 87%, 50% 60%, 50% 50%, 40% 50%, 13% 77%)\",\n            \"polygon(87% 13%, 77% 13%, 50% 40%, 50% 50%, 60% 50%, 87% 23%)\",\n            \"polygon(87% 87%, 77% 87%, 50% 60%, 50% 50%, 60% 50%, 87% 77%)\",\n        ],\n        \"area\": [\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0405,\n            0.0405,\n            0.0405,\n            0.0405,\n            0.0121,\n            0.0121,\n            0.0121,\n            0.0121,\n            0.0237,\n            0.0237,\n            0.0237,\n            0.0237,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/5\",\n        \"bg\": require('../images/bg/song/5/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0,13% 13%, 87% 13%,100% 0)\",\n            \"polygon(0 0,13% 13%, 13% 87%,0 100%)\",\n            \"polygon(0 100%,13% 87%, 87% 87%,100% 100%)\",\n            \"polygon(100% 0,87% 13%, 87% 87%,100% 100%)\",\n            \"polygon(13% 13%, 50% 13%, 50% 50%)\",\n            \"polygon(13% 13%, 13% 50%, 50% 50%)\",\n            \"polygon(87% 13%, 50% 13%, 50% 50%)\",\n            \"polygon(87% 13%, 87% 50%, 50% 50%)\",\n            \"polygon(13% 87%, 50% 87%, 50% 50%)\",\n            \"polygon(13% 87%, 13% 50%, 50% 50%)\",\n            \"polygon(87% 87%, 50% 87%, 50% 50%)\",\n            \"polygon(87% 87%, 87% 50%, 50% 50%)\",\n            \"polygon(0 0, 15.5% 0, 39% 23.5%, 39% 39%)\",\n            \"polygon(0 0, 0 15.5%, 23.5% 39%, 39% 39%)\",\n            \"polygon(100% 0, 84.5% 0, 61% 23.5%, 61% 39%)\",\n            \"polygon(100% 0, 100% 15.5%, 76.5% 39%, 61% 39%)\",\n            \"polygon(0 100%, 15.5% 100%, 39% 76.5%, 39% 61%)\",\n            \"polygon(0 100%, 0 84.5%, 23.5% 61%, 39% 61%)\",\n            \"polygon(100% 100%, 84.5% 100%, 61% 76.5%, 61% 61%)\",\n            \"polygon(100% 100%, 100% 84.5%, 76.5% 61%, 61% 61%)\",\n            \"polygon(39% 13%, 50% 13%, 50% 50%, 39% 39%)\",\n            \"polygon(61% 13%, 50% 13%, 50% 50%, 61% 39%)\",\n            \"polygon(39% 87%, 50% 87%, 50% 50%, 39% 61%)\",\n            \"polygon(61% 87%, 50% 87%, 50% 50%, 61% 61%)\",\n            \"polygon(87% 39%, 87% 50%, 50% 50%, 61% 39%)\",\n            \"polygon(87% 61%, 87% 50%, 50% 50%, 61% 61%)\",\n            \"polygon(13% 39%, 13% 50%, 50% 50%, 39% 39%)\",\n            \"polygon(13% 61%, 13% 50%, 50% 50%, 39% 61%)\"\n        ],\n        \"area\": [\n            0.0255,\n            0.0255,\n            0.0255,\n            0.0255,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0175,\n            0.0175,\n            0.0175,\n            0.0175,\n            0.0175,\n            0.0175,\n            0.0175,\n            0.0175,\n            0.0126,\n            0.0126,\n            0.0126,\n            0.0126,\n            0.0126,\n            0.0126,\n            0.0126,\n            0.0126,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/9\",\n        \"bg\": require('../images/bg/song/9/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 93% 7%, 7% 7%)\",\n            \"polygon(0 100%, 100% 100%, 93% 93%, 7% 93%)\",\n            \"polygon(0 0, 0 100%, 7% 93%, 7% 7%)\",\n            \"polygon(100% 0, 100% 100%, 93% 93%, 93% 7%)\",\n            \"polygon(18.8% 7%, 38.2% 7%, 28.5% 16.7%)\",\n            \"polygon(7% 18.8%, 7% 38.2%, 16.7% 28.5%)\",\n            \"polygon(7% 81.2%, 7% 61.8%, 16.7% 71.5%)\",\n            \"polygon(18.8% 93%, 38.2% 93%, 28.5% 83.3%)\",\n            \"polygon(81.2% 93%, 61.8% 93%, 71.5% 83.3%)\",\n            \"polygon(93% 81.2%, 93% 61.8%, 83.3% 71.5%)\",\n            \"polygon(93% 18.8%, 93% 38.2%, 83.3% 28.5%)\",\n            \"polygon(81.2% 7%, 61.8% 7%, 71.5% 16.7%)\",\n            \"polygon(50% 18.8%, 40.3% 28.5%, 50% 38.2%, 59.7% 28.5%)\",\n            \"polygon(18.8% 50%, 28.5% 40.3%, 38.2% 50%, 28.5% 59.7%)\",\n            \"polygon(50% 81.2%, 40.3% 71.5%, 50% 61.8%, 59.7% 71.5%)\",\n            \"polygon(81.2% 50%, 71.5% 40.3%, 61.8% 50%, 71.5% 59.7%)\",\n            \"polygon(38.2% 7%, 50% 7%, 28.5% 28.5%, 28.5% 16.7%)\",\n            \"polygon(50% 7%, 50% 18.8%, 40.3% 28.5%, 28.5% 28.5%)\",\n            \"polygon(61.8% 7%, 50% 7%, 71.5% 28.5%, 71.5% 16.7%)\",\n            \"polygon(50% 7%, 50% 18.8%, 59.7% 28.5%, 71.5% 28.5%)\",\n            \"polygon(7% 38.2%, 7% 50%, 28.5% 28.5%, 16.7% 28.5%)\",\n            \"polygon(7% 50%, 18.8% 50%, 28.5% 40.3%, 28.5% 28.5%)\",\n            \"polygon(7% 61.8%, 7% 50%, 28.5% 71.5%, 16.7% 71.5%)\",\n            \"polygon(7% 50%, 18.8% 50%, 28.5% 59.7%, 28.5% 71.5%)\",\n            \"polygon(38.2% 93%, 50% 93%, 28.5% 71.5%, 28.5% 83.3%)\",\n            \"polygon(50% 93%, 50% 81.2%, 40.3% 71.5%, 28.5% 71.5%)\",\n            \"polygon(61.8% 93%, 50% 93%, 71.5% 71.5%, 71.5% 83.3%)\",\n            \"polygon(50% 93%, 50% 81.2%, 59.7% 71.5%, 71.5% 71.5%)\",\n            \"polygon(93% 38.2%, 93% 50%, 71.5% 28.5%, 83.3% 28.5%)\",\n            \"polygon(93% 50%, 81.2% 50%, 71.5% 40.3%, 71.5% 28.5%)\",\n            \"polygon(93% 61.8%, 93% 50%, 71.5% 71.5%, 83.3% 71.5%)\",\n            \"polygon(93% 50%, 81.2% 50%, 71.5% 59.7%, 71.5% 71.5%)\",\n            \"polygon(7% 7%, 18.8% 7%, 50% 38.2%, 50% 50%)\",\n            \"polygon(7% 7%, 7% 18.8%, 38.2% 50%, 50% 50%)\",\n            \"polygon(93% 7%, 81.2% 7%, 50% 38.2%, 50% 50%)\",\n            \"polygon(93% 7%, 93% 18.8%, 61.8% 50%, 50% 50%)\",\n            \"polygon(7% 93%, 18.8% 93%, 50% 61.8%, 50% 50%)\",\n            \"polygon(7% 93%, 7% 81.2%, 38.2% 50%, 50% 50%)\",\n            \"polygon(93% 93%, 81.2% 93%, 50% 61.8%, 50% 50%)\",\n            \"polygon(93% 93%, 93% 81.2%, 61.8% 50%, 50% 50%)\",\n        ],\n        \"area\": [\n            0.0234,\n            0.0234,\n            0.0234,\n            0.0234,\n            0.0034,\n            0.0034,\n            0.0034,\n            0.0034,\n            0.0034,\n            0.0034,\n            0.0034,\n            0.0034,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0053,\n            0.0160,\n            0.0160,\n            0.0160,\n            0.0160,\n            0.0160,\n            0.0160,\n            0.0160,\n            0.0160,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/14\",\n        \"bg\": require('../images/bg/song/14/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 0, 0 100%, 50% 50%)\",\n            \"polygon(100% 0, 100% 100%, 50% 50%)\",\n            \"polygon(0 100%, 100% 100%, 50% 50%)\",\n            \"polygon(0 0, 36.47% 0, 46.95% 10.47%, 10.47% 10.47%)\",\n            \"polygon(0 0, 10.47% 10.47%, 10.47% 46.95%, 0 36.47%)\",\n            \"polygon(100% 100%, 100% 63.53%, 89.53% 53.05%, 89.53% 89.53%)\",\n            \"polygon(100% 100%, 89.53% 89.53%, 53.05% 89.53%, 63.53% 100%)\",\n            \"polygon(46.95% 10.47%, 10.47% 10.47%, 89.53% 89.53%, 89.53% 53.05%)\",\n            \"polygon(10.47% 10.47%, 10.47% 46.95%, 53.05% 89.53%, 89.533% 89.53%)\"\n        ],\n        \"area\": [\n            0.0370,\n            0.0370,\n            0.0370,\n            0.0370,\n            0.0136,\n            0.0136,\n            0.0136,\n            0.0136,\n            0.0796,\n            0.0796,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/17\",\n        \"bg\": require('../images/bg/song/17/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(100% 0, 50% 0, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(0 50%, 0 0, 50% 50%)\",\n            \"polygon(0 44.57%, 50% 44.57%, 50% 55.43%, 0 55.43%)\",\n            \"polygon(100% 44.57%, 50% 44.57%, 50% 55.43%, 100% 55.43%)\",\n            \"polygon(44.57% 0, 55.43% 0, 55.43% 50%, 44.57% 50%)\",\n            \"polygon(44.57% 100%, 55.43% 100%, 55.43% 50%, 44.57% 50%)\",\n            \"polygon(0 0, 16.38% 0, 50% 33.62%, 50% 50%)\",\n            \"polygon(0 0, 0 16.38%, 33.62% 50%, 50% 50%)\",\n            \"polygon(100% 0, 83.62% 0, 50% 33.62%, 50% 50%)\",\n            \"polygon(100% 0, 100% 16.38%, 66.38% 50%, 50% 50%)\",\n            \"polygon(0 100%, 16.38% 100%, 50% 66.38%, 50% 50%)\",\n            \"polygon(0 100%, 0 83.62%, 33.62% 50%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 83.62%, 66.38% 50%, 50% 50%)\",\n            \"polygon(100% 100%, 83.62% 100%, 50% 66.38%, 50% 50%)\"\n        ],\n        \"area\": [\n            0.0148,\n            0.0148,\n            0.0148,\n            0.0148,\n            0.0148,\n            0.0148,\n            0.0148,\n            0.0148,\n            0.0125,\n            0.0125,\n            0.0125,\n            0.0125,\n            0.0480,\n            0.0480,\n            0.0480,\n            0.0480,\n            0.0480,\n            0.0480,\n            0.0480,\n            0.0480,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/20\",\n        \"bg\": require('../images/bg/song/20/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%, 0 50%)\",\n            \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(100% 50%, 100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 50%, 0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(50% 0, 25% 25%, 50% 50%, 75% 25%)\",\n            \"polygon(50% 100%, 25% 75%, 50% 50%, 75% 75%)\",\n            \"polygon(0 50%, 25% 25%, 50% 50%, 25% 75%)\",\n            \"polygon(100% 50%, 75% 25%, 50% 50%, 75% 75%)\",\n            \"polygon(55.14% 55.14%, 44.86% 44.86%, 17.03% 72.69%, 27.31% 82.97%)\",\n            \"polygon(72.69% 17.03%, 82.97% 27.31%, 55.14% 55.14%, 44.86% 44.86%)\",\n            \"polygon(17.03% 27.31%, 27.31% 17.03%, 82.97% 72.69%, 72.69% 82.97%)\",\n            \"polygon(27.53% 0, 50% 0, 0 50%, 0 27.53%)\",\n            \"polygon(72.47% 0, 50% 0, 100% 50%, 100% 27.53%)\",\n            \"polygon(27.53% 100%, 50% 100%, 0 50%, 0 72.47%)\",\n            \"polygon(72.47% 100%, 50% 100%, 100% 50%, 100% 72.47%)\",\n        ],\n        \"area\": [\n            0.0135,\n            0.0135,\n            0.0135,\n            0.0135,\n            0.0282,\n            0.0282,\n            0.0282,\n            0.0282,\n            0.0148,\n            0.0148,\n            0.0374,\n            0.0315,\n            0.0315,\n            0.0315,\n            0.0315,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/21\",\n        \"bg\": require('../images/bg/song/21/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 0 50%)\",\n            \"polygon(100% 0, 50% 0, 100% 50%)\",\n            \"polygon(0 100%, 50% 100%, 0 50%)\",\n            \"polygon(100% 100%, 50% 100%, 100% 50%)\",\n            \"polygon(0 0, 86.29% 0, 86.29% 13.71%, 0 13.71%)\",\n            \"polygon(86.29% 0, 100% 0, 100% 86.29%, 86.29% 86.29%)\",\n            \"polygon(100% 86.29%, 100% 100%, 13.71% 100%, 13.71% 86.29%)\",\n            \"polygon(13.71% 100%, 0 100%, 0% 13.71%, 13.71% 13.71%)\",\n            \"polygon(36.11% 13.71%, 50% 13.71%, 50% 50%, 13.71% 50%, 13.71% 36.11%)\",\n            \"polygon(36.11% 86.29%, 50% 86.29%, 50% 50%, 13.71% 50%, 13.71% 63.89%)\",\n            \"polygon(63.89% 13.71%, 50% 13.71%, 50% 50%, 86.29% 50%, 86.29% 36.11%)\",\n            \"polygon(63.89% 86.29%, 50% 86.29%, 50% 50%, 86.29% 50%, 86.29% 63.89%)\",\n            \"polygon(50% 34.03%, 65.97% 50%, 50% 65.97%, 34.03% 50%)\"\n        ],\n        \"area\": [\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0426,\n            0.0426,\n            0.0426,\n            0.0426,\n            0.0338,\n            0.0338,\n            0.0338,\n            0.0338,\n            0.0184,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/22\",\n        \"bg\": require('../images/bg/song/22/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 0, 0 100%, 50% 50%)\",\n            \"polygon(100% 100%, 0 100%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 0, 50% 50%)\",\n            \"polygon(9.83% 9.83%, 25.9% 9.83%, 25.9% 90.17%, 9.83% 90.17%)\",\n            \"polygon(25.9% 9.83%, 41.97% 9.83%, 41.97% 70%, 25.9% 70%)\",\n            \"polygon(25.9% 70%, 41.97% 70%, 41.97% 90.17%, 25.9% 90.17%)\",\n            \"polygon(41.97% 9.83%, 58.03% 9.83%, 58.03% 50%, 41.97% 50%)\",\n            \"polygon(41.97% 50%, 58.03% 50%, 58.03% 90.17%, 41.97% 90.17%)\",\n            \"polygon(58.03% 9.83%, 74.1% 9.83%, 74.1% 30%, 58.03% 30%)\",\n            \"polygon(58.03% 30%, 74.1% 30%, 74.1% 90.17%, 58.03% 90.17%)\",\n            \"polygon(74.1% 9.83%, 90.17% 9.83%, 90.17% 90.17%, 74.1% 90.17%)\",\n            \"polygon(74.1% 9.83%, 90.17% 9.83%, 9.83% 90.17%, 9.83% 74.1%)\",\n            \"polygon(90.17% 9.83%, 90.17% 25.9%, 25.9% 90.17%, 9.83% 90.17%)\"\n        ],\n        \"area\": [\n            0.0319,\n            0.0319,\n            0.0319,\n            0.0319,\n            0.0335,\n            0.0225,\n            0.0044,\n            0.0135,\n            0.0135,\n            0.0044,\n            0.0225,\n            0.0335,\n            0.0419,\n            0.0419,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/31\",\n        \"bg\": require('../images/bg/song/31/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%, 0 50%)\",\n            \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%, 0 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%, 100% 50%)\",\n            \"polygon(100% 0, 91.16% 0, 100% 8.84%)\",\n            \"polygon(0 100%, 0 91.16%, 8.84% 100%)\",\n            \"polygon(0 0, 10.5% 0, 4.42% 95.58%, 0 91.16%)\",\n            \"polygon(0 0, 91.16% 0, 95.58% 4.42%, 0 10.5%)\",\n            \"polygon(95.58% 4.42%, 100% 8.84%, 100% 100%, 89.5% 100%)\",\n            \"polygon(100% 89.5%, 100% 100%, 8.84% 100%, 4.42% 95.58%)\",\n            \"polygon(7.23% 50%, 28.615% 28.615%, 50% 50%, 28.615% 71.385%)\",\n            \"polygon(92.77% 50%, 71.385% 28.615%, 50% 50%, 71.385% 71.385%)\",\n            \"polygon(50% 7.23%, 28.615% 28.615%, 50% 50%, 71.385% 28.615%)\",\n            \"polygon(50% 92.77%, 28.615% 71.385%, 50% 50%, 71.385% 71.385%)\",\n            \"polygon(35.7433% 21.4867%, 21.45% 35.7433%, 64% 78.6%, 78.5133% 64.2567%)\",\n            \"polygon(9.72% 9.72%, 25.19% 8.77%, 8.77% 25.19%)\",\n            \"polygon(90.28% 90.28%, 74.81% 91.23%, 91.23% 74.81%)\",\n            \"polygon(5.58% 76.86%, 23.14% 94.43%, 4.42% 95.58%)\",\n            \"polygon(76.86% 5.58%, 94.43% 23.14%, 95.58% 4.42%)\",\n            \"polygon(33.27% 76.04%, 23.97% 66.74%, 39.74% 54.01%, 46% 60.25%)\",\n            \"polygon(76.04% 33.27%, 66.74% 23.97%, 54.01% 39.74%, 60.25% 46%)\"\n        ],\n        \"area\": [\n            0.0245,\n            0.0311,\n            0.0311,\n            0.0245,\n            0.0014,\n            0.0014,\n            0.0216,\n            0.0256,\n            0.0216,\n            0.0256,\n            0.0178,\n            0.0178,\n            0.0178,\n            0.0178,\n            0.0437,\n            0.0043,\n            0.0043,\n            0.0064,\n            0.0063,\n            0.0079,\n            0.0079,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/32\",\n        \"bg\": require('../images/bg/song/32/bg.png'),\n        \"clipPath\": [\n            \"\",\n            \"polygon(0 41.1%, 30% 41.1%, 30% 100%, 0 100%)\",\n            \"polygon(100% 58.9%, 70% 58.9%, 70% 0, 100% 0)\",\n            \"polygon(58.9% 0, 58.9% 30%, 0 30%, 0 0)\",\n            \"polygon(41.1% 100%, 41.1% 70%, 100% 70%, 100% 100%)\",\n            \"polygon(86.3% 0, 100% 13.7%, 63.7% 50%, 50% 36.3%)\",\n            \"polygon(36.3% 50%, 50% 63.7%, 13.7% 100%, 0 86.3% )\",\n            \"polygon(36.3% 50%, 50% 36.3%, 13.7% 0, 0 13.7% )\",\n            \"polygon(63.7% 50%, 50% 63.7%, 86.3% 100%, 100% 86.3% )\",\n            \"polygon(22.6% 36.3%, 36.3% 50%, 86.3% 0, 58.9% 0)\",\n            \"polygon(77.4% 63.7%, 63.7% 50%, 13.7% 100%, 41.1% 100%)\",\n            \"polygon(50% 36.3%, 63.7% 22.6%, 100% 58.9%, 100% 86.3%)\",\n            \"polygon(50% 63.7%, 36.3% 77.4%, 0 41.1%, 0 13.7%)\",\n            \"polygon(0 0, 13.7% 0, 0 13.7%)\",\n            \"polygon(100% 100%, 86.3% 100%, 100% 86.3%)\",\n            \"polygon(100% 0, 100% 86.3%, 93.15% 93.15%, 89.09% 0)\",\n            \"polygon(100% 0, 100% 10.91%, 6.85% 6.85%, 13.7% 0)\",\n            \"polygon(0 100%, 10.91% 100%, 6.85% 6.85%, 0 13.7%)\",\n            \"polygon(0 89.09%, 0 100%, 86.3% 100%, 93.15% 93.15%)\",\n        ],\n        \"area\": [\n            0.0135,\n            0.0064,\n            0.0064,\n            0.0074,\n            0.0074,\n            0.0157,\n            0.0157,\n            0.0184,\n            0.0184,\n            0.0333,\n            0.0333,\n            0.0344,\n            0.0344,\n            0.0036,\n            0.0036,\n            0.0250,\n            0.0290,\n            0.0250,\n            0.0290,\n        ],\n        \"profit\": 350\n    },\n    {\n        \"path\": \"song/37\",\n        \"bg\": require('../images/bg/song/37/bg.png'),\n        \"clipPath\": [\n            \"\",\n            \"polygon(0 0, 34.5% 34.5%, 63.39% 34.5%, 100% 0)\",\n            \"polygon(0 100%, 34.5% 63.39%, 63.39% 63.39%, 100% 100%)\",\n            \"polygon(0 0, 34.5% 34.5%, 34.5% 63.39%, 0 100%)\",\n            \"polygon(100% 0, 63.39% 34.5%, 63.39% 63.39%, 100% 100%)\"\n        ],\n        \"area\": [\n            0.0346,\n            0.0814,\n            0.0814,\n            0.0814,\n            0.0814,\n        ],\n        \"profit\": 350\n    }\n]\n\n\nexport const yuan = [\n    {\n        \"path\": \"yuan/12\",\n        \"bg\": require('../images/bg/yuan/12/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 25% 0, 0 25%)\",\n            \"polygon(0 100%, 75% 100%, 0 75%)\",\n            \"polygon(100% 100%, 75% 100%, 100% 75%)\",\n            \"polygon(100% 0, 75% 0, 100% 25%)\",\n            \"polygon(25% 0, 50% 0, 0 50%, 0 25%)\",\n            \"polygon(75% 0, 50% 0, 100% 50%, 100% 25%)\",\n            \"polygon(25% 100%, 50% 100%, 0 50%, 0 75%)\",\n            \"polygon(75% 100%, 50% 100%, 100% 50%, 100% 75%)\",\n            \"polygon(30.5% 19.5%, 50% 39%, 50% 0)\",\n            \"polygon(69.5% 19.5%, 50% 39%, 50% 0)\",\n            \"polygon(30.5% 80.5%, 50% 61%, 50% 100%)\",\n            \"polygon(69.5% 80.5%, 50% 61%, 50% 100%)\",\n            \"polygon(19.5% 69.5%,39% 50%,0 50%)\",\n            \"polygon(19.5% 30.5%,39% 50%,0 50%)\",\n            \"polygon(80.5% 69.5%,61% 50%,100% 50%)\",\n            \"polygon(80.5% 30.5%,61% 50%,100% 50%)\",\n            \"polygon(19.5% 69.5%, 30.5% 80.5%, 50% 61%, 50% 50%, 39% 50%)\",\n            \"polygon(69.5% 19.5%, 80.5% 30.5%, 61% 50%, 50% 50%, 50% 39%)\",\n            \"polygon(19.5% 30.5%, 30.5% 19.5%, 80.5% 69.5%,69.5% 80.5%)\"\n        ],\n        \"area\": [\n            0.0113,\n            0.0113,\n            0.0113,\n            0.0113,\n            0.0334,\n            0.0334,\n            0.0334,\n            0.0334,\n            0.0138,\n            0.0138,\n            0.0138,\n            0.0138,\n            0.0138,\n            0.0138,\n            0.0138,\n            0.0138,\n            0.0153,\n            0.0153,\n            0.0390,\n        ],\n        \"profit\": 400\n    },\n    {\n        \"path\": \"yuan/19\",\n        \"bg\": require('../images/bg/yuan/19/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 25% 25%, 0 50%)\",\n            \"polygon(0 0, 25% 25%, 50% 0)\",\n            \"polygon(100% 0, 75% 25%, 50% 0)\",\n            \"polygon(100% 0, 75% 25%, 100% 50%)\",\n            \"polygon(100% 100%, 75% 75%, 100% 50%)\",\n            \"polygon(100% 100%, 75% 75%, 50% 100%)\",\n            \"polygon(0 100%, 25% 75%, 50% 100%)\",\n            \"polygon(0 100%, 25% 75%, 0 50%)\",\n            \"polygon(50% 0, 75% 25%, 50% 50%, 25% 25%)\",\n            \"polygon(100% 50%, 75% 25%, 50% 50%, 75% 75%)\",\n            \"polygon(50% 100%, 25% 75%, 50% 50%, 75% 75%)\",\n            \"polygon(0 50%, 25% 75%, 50% 50%, 25% 25%)\",\n            \"polygon(100% 0, 96.35% 0, 75% 21.35%, 78.65% 25%, 100% 3.65%)\",\n            \"polygon(0 100%, 3.65% 100%, 25% 78.65%, 21.35% 75%, 0 96.35%)\",\n            \"polygon(46.35% 0, 53.65% 0, 26.825% 26.825%, 23.175% 23.175%)\",\n            \"polygon(0 46.35%, 0 53.65%, 26.825% 26.825%, 23.175% 23.175%)\",\n            \"polygon(100% 46.35%, 100% 53.65%, 76.825% 76.825%, 73.175% 73.175%)\",\n            \"polygon(46.35% 100%, 53.65% 100%, 76.825% 76.825%, 73.175% 73.175%)\",\n            \"polygon(100% 100%, 96.35% 100%, 46.35% 50%, 50% 46.35%, 100% 96.35%)\",\n            \"polygon(0 0, 3.65% 0, 53.65% 50%, 50% 53.65%, 0 3.65%)\",\n            \"polygon(75% 28.65%, 71.35% 25%, 25% 71.35%, 28.65% 75%)\",\n            \"polygon(53.65% 0, 50% 3.65%, 96.35% 50%, 100% 46.35%)\",\n            \"polygon(3.65% 50%, 50% 96.35%, 46.35% 100%, 0 53.65%)\"\n        ],\n        \"area\": [\n            0.0163,\n            0.0163,\n            0.0163,\n            0.0163,\n            0.0163,\n            0.0163,\n            0.0163,\n            0.0163,\n            0.0326,\n            0.0326,\n            0.0326,\n            0.0326,\n            0.0059,\n            0.0059,\n            0.0062,\n            0.0062,\n            0.0062,\n            0.0062,\n            0.0126,\n            0.0126,\n            0.0122,\n            0.0124,\n            0.0124,\n        ],\n        \"profit\": 400\n    },\n    {\n        \"path\": \"yuan/27\",\n        \"bg\": require('../images/bg/yuan/27/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 50%, 0 100%)\",\n            \"polygon(0 0, 50% 50%, 100% 0)\",\n            \"polygon(100% 100%, 50% 50%, 100% 0)\",\n            \"polygon(100% 100%, 50% 50%, 0 100%)\",\n            \"polygon(0 0, 19.76% 0, 50% 30.23%, 36.5% 36.5%)\",\n            \"polygon(0 0, 0 19.76%, 30.23% 50%, 36.5% 36.5%)\",\n            \"polygon(0 100%, 19.76% 100%, 50% 69.77%, 36.5% 63.5%)\",\n            \"polygon(0 100%, 0 80.24%, 30.23% 50%, 36.5% 63.5%)\",\n            \"polygon(100% 0, 80.24% 0, 50% 30.23%, 63.5% 36.5%)\",\n            \"polygon(100% 0, 100% 19.76%, 69.77% 50%, 63.5% 36.5%)\",\n            \"polygon(100% 100%, 100% 80.24%, 69.77% 50%, 63.5% 63.5%)\",\n            \"polygon(100% 100%, 80.24% 100%, 50% 69.77%, 63.5% 63.5%)\",\n            \"polygon(50% 30.23%, 50% 50%, 30.23% 50%, 36.5% 36.5%)\",\n            \"polygon(50% 69.77%, 50% 50%, 30.23% 50%, 36.5% 63.5%)\",\n            \"polygon(50% 30.23%, 50% 50%, 69.77% 50%, 63.5% 36.5%)\",\n            \"polygon(50% 69.77%, 50% 50%, 69.77% 50%, 63.5% 63.5%)\"\n\n        ],\n        \"area\": [\n            0.0328,\n            0.0328,\n            0.0328,\n            0.0328,\n            0.0238,\n            0.0238,\n            0.0238,\n            0.0238,\n            0.0238,\n            0.0238,\n            0.0238,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0095,\n        ],\n        \"profit\": 400\n    },\n    {\n        \"path\": \"yuan/29\",\n        \"bg\": require('../images/bg/yuan/29/bg.png'),\n        \"clipPath\": [\n            \"polygon(6.25% 0, 0 6.25%, 0 0)\",\n            \"polygon(6.25% 0, 18.75% 0, 6.25% 12.5%, 0 6.25%)\",\n            \"polygon(18.75% 0, 31.25% 0, 12.5% 18.75%, 6.25% 12.5%)\",\n            \"polygon(31.25% 0, 43.75% 0, 18.75% 25%, 12.5% 18.75%)\",\n            \"polygon(43.75% 0, 56.25% 0, 50% 6.25%)\",\n            \"polygon(56.25% 0, 68.75% 0, 56.25% 12.5%, 50% 6.25%)\",\n            \"polygon(68.75% 0, 81.25% 0, 62.5% 18.75%, 56.25% 12.5%)\",\n            \"polygon(81.25% 0, 93.75% 0, 68.75% 25%, 62.5% 18.75%)\",\n            \"polygon(43.75% 0, 37.5% 6.25%, 68.75% 37.5%, 75% 31.25%)\",\n            \"polygon(37.5% 6.25%, 31.25% 12.5%, 62.5% 43.75%, 68.75% 37.5%)\",\n            \"polygon(31.25% 12.5%, 25% 18.75%, 56.25% 50%, 62.5% 43.75%)\",\n            \"polygon(25% 18.75%, 31.25% 25%, 25% 31.25%, 18.75% 25%)\",\n            \"polygon(100% 6.25%, 93.75% 0, 100% 0)\",\n            \"polygon(100% 6.25%, 100% 18.75%, 87.5% 6.25%, 93.75% 0)\",\n            \"polygon(100% 18.75%, 100% 31.25%, 81.25% 12.5%, 87.5% 6.25%)\",\n            \"polygon(100% 31.25%, 100% 43.75%, 75% 18.75%, 81.25% 12.5%)\",\n            \"polygon(100% 43.75%, 100% 56.25%, 93.75% 50%)\",\n            \"polygon(100% 56.25%, 100% 68.75%, 87.5% 56.25%, 93.75% 50%)\",\n            \"polygon(100% 68.75%, 100% 81.25%, 81.25% 62.5%, 87.5% 56.25%)\",\n            \"polygon(100% 81.25%, 100% 93.75%, 75% 68.75%, 81.25% 62.5%)\",\n            \"polygon(100% 43.75%, 93.75% 37.5%, 62.5% 68.75%, 68.75% 75%)\",\n            \"polygon(93.75% 37.5%, 87.5% 31.25%, 56.25% 62.5%, 62.5% 68.75%)\",\n            \"polygon(87.5% 31.25%, 81.25% 25%, 50% 56.25%, 56.25% 62.5%)\",\n            \"polygon(75% 18.75%, 81.25% 25%, 75% 31.25%, 68.75% 25%)\",\n            \"polygon(93.75% 100%, 100% 93.75%, 100% 100%)\",\n            \"polygon(93.75% 100%, 81.25% 100%, 93.75% 87.5%, 100% 93.75%)\",\n            \"polygon(81.25% 100%, 68.75% 100%, 87.5% 81.25%, 93.75% 87.5%)\",\n            \"polygon(68.75% 100%, 56.25% 100%, 81.25% 75%, 87.5% 81.25%)\",\n            \"polygon(56.25% 100%, 43.75% 100%, 50% 93.75%)\",\n            \"polygon(43.75% 100%, 31.25% 100%, 43.75% 87.5%, 50% 93.75%)\",\n            \"polygon(31.25% 100%, 18.75% 100%, 37.5% 81.25%, 43.75% 87.5%)\",\n            \"polygon(18.75% 100%, 6.25% 100%, 31.25% 75%, 37.5% 81.25%)\",\n            \"polygon(56.25% 100%, 62.5% 93.75%, 31.25% 62.5%, 25% 68.75%)\",\n            \"polygon(62.5% 93.75%, 68.75% 87.5%, 37.5% 56.25%, 31.25% 62.5%)\",\n            \"polygon(68.75% 87.5%, 75% 81.25%, 43.75% 50%, 37.5% 56.25%)\",\n            \"polygon(75% 81.25%, 68.75% 75%, 75% 68.75%, 81.25% 75%)\",\n            \"polygon(0 93.75%, 6.25% 100%, 0 100%)\",\n            \"polygon(0 93.75%, 0 81.25%, 12.5% 93.75%, 6.25% 100%)\",\n            \"polygon(0 81.25%, 0 68.75%, 18.75% 87.5%, 12.5% 93.75%)\",\n            \"polygon(0 68.75%, 0 56.25%, 25% 81.25%, 18.75% 87.5%)\",\n            \"polygon(0 56.25%, 0 43.75%, 6.25% 50%)\",\n            \"polygon(0 43.75%, 0 31.25%, 12.5% 43.75%, 6.25% 50%)\",\n            \"polygon(0 31.25%, 0 18.75%, 18.75% 37.5%, 12.5% 43.75%)\",\n            \"polygon(0 18.75%, 0 6.25%, 25% 31.25%, 18.75% 37.5%)\",\n            \"polygon(0 56.25%, 6.25% 62.5%, 37.5% 31.25%, 31.25% 25%)\",\n            \"polygon(6.25% 62.5%, 12.5% 68.75%, 43.75% 37.5%, 37.5% 31.25%)\",\n            \"polygon(12.5% 68.75%, 18.75% 75%, 50% 43.75%, 43.75% 37.5%)\",\n            \"polygon(25% 68.75%, 31.25% 75%, 25% 81.25%, 18.75% 75%)\",\n            \"polygon(50% 43.75%, 56.25% 50%, 50% 56.25%, 43.75% 50%)\",\n        ],\n        \"area\": [\n            0.0007,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0014,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0141,\n            0.0141,\n            0.0141,\n            0.0028,\n            0.0007,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0014,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0141,\n            0.0141,\n            0.0141,\n            0.0028,\n            0.0007,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0014,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0141,\n            0.0141,\n            0.0141,\n            0.0028,\n            0.0007,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0014,\n            0.0042,\n            0.0070,\n            0.0098,\n            0.0141,\n            0.0141,\n            0.0141,\n            0.0028,\n            0.0028,\n        ],\n        \"profit\": 400\n    },\n    {\n        \"path\": \"yuan/33\",\n        \"bg\": require('../images/bg/yuan/33/bg.png'),\n        \"clipPath\": [\n            \"polygon(50% 0, 50% 50%, 0 50%)\",\n            \"polygon(50% 0, 50% 50%, 100% 50%)\",\n            \"polygon(100% 50%, 50% 50%, 50% 100%)\",\n            \"polygon(0 50%, 50% 50%, 50% 100%)\",\n            \"polygon(9.5% 42.08%, 17.42% 50%, 9.5% 57.92%)\",\n            \"polygon(42.08% 9.5%, 50% 17.42%, 57.92% 9.5%)\",\n            \"polygon(90.5% 42.08%, 82.58% 50%, 90.5% 57.92%)\",\n            \"polygon(42.08% 90.5%, 50% 82.58%, 57.92% 90.5%)\",\n            \"polygon(23.88% 23.88%, 43.38% 23.88%, 23.88% 43.38%)\",\n            \"polygon(76.12% 23.88%, 56.62% 23.88%, 76.12% 43.38%)\",\n            \"polygon(23.88% 76.12%, 43.38% 76.12%, 23.88% 56.62%)\",\n            \"polygon(76.12% 76.12%, 56.62% 76.12%, 76.12% 56.62%)\",\n            \"polygon(57.92% 9.5%, 71% 9.5%, 50% 30.5%, 43.38% 23.88%)\",\n            \"polygon(50% 30.5%, 9.5% 71%, 9.5% 57.92%, 43.38% 23.88%)\",\n            \"polygon(90.5% 81%, 90.5% 57.92%, 82.58% 50%, 76.12% 56.62%)\",\n            \"polygon(76.12% 43.38%, 69.7% 50%, 29% 9.5%, 42.08% 9.5%)\",\n            \"polygon(50% 69.5%, 90.5% 29%, 90.5% 42.08%, 56.62% 76.12%)\",\n            \"polygon(42.08% 90.5%, 29% 90.5%, 50% 69.5%, 56.62% 76.12%)\",\n            \"polygon(9.5% 29%, 9.5% 42.08%, 17.42% 50%, 23.88% 43.38%)\",\n            \"polygon(23.88% 56.62%, 30.3% 50%, 71% 90.5%, 57.92% 90.5%)\",\n            \"polygon(9.5% 9.5%, 23.88% 23.88%, 23.88% 43.38%, 9.5% 29%)\",\n            \"polygon(9.5% 9.5%, 23.88% 23.88%, 43.38% 23.88%, 29% 9.5%)\",\n            \"polygon(90.5% 9.5%, 76.12% 23.88%, 76.12% 43.38%, 90.5% 29%)\",\n            \"polygon(90.5% 9.5%, 76.12% 23.88%, 56.62% 23.88%, 71% 9.5%)\",\n            \"polygon(90.5% 90.5%, 76.12% 76.12%, 76.12% 56.62%, 90.5% 71%)\",\n            \"polygon(90.5% 90.5%, 76.12% 76.12%, 56.62% 76.12%, 71% 90.5%)\",\n            \"polygon(9.5% 90.5%, 23.88% 76.12%, 23.88% 56.62%, 9.5% 71%)\",\n            \"polygon(9.5% 90.5%, 23.88% 76.12%, 43.38% 76.12%, 29% 90.5%)\",\n            \"polygon(0 0, 90.5% 0, 90.5% 9.5%, 0 9.5%)\",\n            \"polygon(90.5% 0, 100% 0, 100% 90.5%, 90.5% 90.5%)\",\n            \"polygon(100% 90.5%, 100% 100%, 9.5% 100%, 9.5% 90.5%)\",\n            \"polygon(0 100%, 9.5% 100%, 9.5% 9.5%, 0 9.5%)\"\n        ],\n        \"area\": [\n            0.0068,\n            0.0068,\n            0.0068,\n            0.0068,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0022,\n            0.0068,\n            0.0068,\n            0.0068,\n            0.0068,\n            0.0053,\n            0.0175,\n            0.0053,\n            0.0175,\n            0.0175,\n            0.0053,\n            0.0053,\n            0.0175,\n            0.0101,\n            0.0101,\n            0.0101,\n            0.0101,\n            0.0101,\n            0.0101,\n            0.0101,\n            0.0311,\n            0.0311,\n            0.0311,\n            0.0311,\n        ],\n        \"profit\": 400\n    },\n    {\n        \"path\": \"yuan/36\",\n        \"bg\": require('../images/bg/yuan/36/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 0, 0 100%, 50% 50%)\",\n            \"polygon(100% 100%, 0 100%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 0, 50% 50%)\",\n            \"polygon(13.88% 13.88%, 13.88% 50%, 50% 50%)\",\n            \"polygon(86.12% 13.88%, 86.12% 50%, 50% 50%)\",\n            \"polygon(86.12% 86.12%, 86.12% 50%, 50% 50%)\",\n            \"polygon(13.88% 86.12%, 13.88% 50%, 50% 50%)\",\n            \"polygon(13.88% 13.88%, 50% 13.88%, 50% 50%)\",\n            \"polygon(86.12% 13.88%, 50% 13.88%, 50% 50%)\",\n            \"polygon(86.12% 86.12%, 50% 86.12%, 50% 50%)\",\n            \"polygon(13.88% 86.12%, 50% 86.12%, 50% 50%)\",\n            \"polygon(81.37% 0, 100% 0, 50% 50%, 40.685% 40.685%)\",\n            \"polygon(40.685% 40.685%, 50% 50%, 0 100%, 0 81.37%)\",\n            \"polygon(100% 0, 100% 18.63%, 59.315% 59.315%, 50% 50%)\",\n            \"polygon(50% 50%, 59.315% 59.315%, 18.63% 100%, 0 100%)\",\n            \"polygon(50% 13.88%, 86.12% 50%, 68.06% 68.06%, 31.94% 31.94%)\",\n            \"polygon(31.94% 31.94%, 68.06% 68.06%, 50% 86.12%, 13.88% 50%)\",\n            \"polygon(0 0, 18.63% 0, 100% 81.37%, 100% 100%)\",\n            \"polygon(0 0, 100% 100%, 81.37% 100%, 0 18.63%)\"\n        ],\n        \"area\": [\n            0.0243,\n            0.0243,\n            0.0243,\n            0.0243,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0179,\n            0.0179,\n            0.0179,\n            0.0179,\n            0.0240,\n            0.0240,\n            0.0597,\n            0.0597,\n        ],\n        \"profit\": 400\n    }\n]\n\nexport const ming = [\n    {\n        \"path\": \"ming/6\",\n        \"bg\": require('../images/bg/ming/6/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 100% 0, 87% 13%, 13% 13%)\",\n            \"polygon(0 0, 13% 13%, 13% 87%, 0 100%)\",\n            \"polygon(0 100%, 100% 100%, 87% 87%, 13% 87%)\",\n            \"polygon(100% 0, 87% 13%, 87% 87%, 100% 100%)\",\n            \"polygon(13% 13%, 33% 13%, 33% 33%, 13% 33%)\",\n            \"polygon(67% 13%, 87% 13%, 87% 33%, 67% 33%)\",\n            \"polygon(67% 87%, 87% 87%, 87% 67%, 67% 67%)\",\n            \"polygon(13% 87%, 33% 87%, 33% 67%, 13% 67%)\",\n            \"polygon(33% 13%, 67% 13%, 50% 30%)\",\n            \"polygon(13% 33%, 13% 67%, 30% 50%)\",\n            \"polygon(33% 87%, 67% 87%, 50% 70%)\",\n            \"polygon(87% 33%, 87% 67%, 70% 50%)\",\n            \"polygon(33% 13%, 50% 30%, 50% 50%, 33% 33%)\",\n            \"polygon(67% 87%, 50% 70%, 50% 50%, 67% 67%)\",\n            \"polygon(13% 67%, 33% 67%, 50% 50%, 30% 50%)\",\n            \"polygon(87% 33%, 67% 33%, 50% 50%, 70% 50%)\",\n            \"polygon(13% 33%, 33% 33%, 50% 50%, 30% 50%)\",\n            \"polygon(67% 13%, 50% 30%, 50% 50%, 67% 33%)\",\n            \"polygon(33% 87%, 50% 70%, 50% 50%, 33% 67%)\",\n            \"polygon(87% 67%, 67% 67%, 50% 50%, 70% 50%)\"\n        ],\n        \"area\": [\n            0.0403,\n            0.0403,\n            0.0403,\n            0.0403,\n            0.0145,\n            0.0145,\n            0.0145,\n            0.0145,\n            0.0105,\n            0.0105,\n            0.0105,\n            0.0105,\n            0.0124,\n            0.0124,\n            0.0124,\n            0.0124,\n            0.0124,\n            0.0124,\n            0.0124,\n            0.0124,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/7\",\n        \"bg\": require('../images/bg/ming/7/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 0 15.8%, 15.8% 0)\",\n            \"polygon(100% 0, 100% 15.8%, 84.2% 0)\",\n            \"polygon(0 100%, 0 84.2%, 15.8% 100%)\",\n            \"polygon(100% 100%, 100% 84.2%, 84.2% 100%)\",\n            \"polygon(0 15.8%, 0 50%, 50% 50%)\",\n            \"polygon(0 84.2%, 0 50%, 50% 50%)\",\n            \"polygon(100% 15.8%, 100% 50%, 50% 50%)\",\n            \"polygon(100% 84.2%, 100% 50%, 50% 50%)\",\n            \"polygon(15.8% 0, 50% 0, 50% 50%)\",\n            \"polygon(15.8% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(84.2% 0, 50% 0, 50% 50%)\",\n            \"polygon(84.2% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(15.8% 0, 54.57% 54.57%, 0 15.8%)\",\n            \"polygon(15.8% 100%, 54.57% 45.43%, 0 84.2%)\",\n            \"polygon(84.2% 0, 45.43% 54.57%, 100% 15.8%)\",\n            \"polygon(84.2% 100%, 45.43% 45.43%, 100% 84.2%)\",\n            \"polygon(23.94% 0, 45.35% 30.14%, 54.65% 30.14%, 76.06% 0)\",\n            \"polygon(23.94% 100%, 45.35% 69.86%, 54.65% 69.86%, 76.06% 100%)\",\n            \"polygon(0 23.94%, 30.14% 45.35%, 30.14% 54.65%, 0 76.06%)\",\n            \"polygon(100% 23.94%, 69.86% 45.35%, 69.86% 54.65%, 100% 76.06%)\",\n            \"polygon(45.35% 30.14%, 47.61% 30.14%, 47.61% 40.85%, 40.85% 40.85%, 37.75% 37.75%)\",\n            \"polygon(30.14% 45.35%, 30.14% 47.61%, 40.85% 47.61%, 40.85% 40.85%, 37.75% 37.75%)\",\n            \"polygon(54.65% 30.14%, 52.39% 30.14%, 52.39% 40.85%, 59.15% 40.85%, 62.25% 37.75%)\",\n            \"polygon(69.86% 45.35%, 69.86% 47.61%, 59.15% 47.61%, 59.15% 40.85%, 62.25% 37.75%)\",\n            \"polygon(30.14% 54.65%, 30.14% 52.39%, 40.85% 52.39%, 40.85% 59.15%, 37.75% 62.25%)\",\n            \"polygon(45.35% 69.86%, 47.61% 69.86%, 47.61% 59.15%, 40.85% 59.15%, 37.75% 62.25%)\",\n            \"polygon(54.65% 69.86%, 52.39% 69.86%, 52.39% 59.15%, 59.15% 59.15%, 62.25% 62.25%)\",\n            \"polygon(69.86% 54.65%, 69.86% 52.39%, 59.15% 52.39%, 59.15% 59.15%, 62.25% 62.25%)\",\n            \"polygon(47.61% 50%, 52.39% 50%, 52.39% 69.86%, 47.61% 69.86%)\",\n            \"polygon(47.61% 30.14%, 52.39% 30.14%, 52.39% 50%, 47.61% 50%)\",\n            \"polygon(30.14% 47.61%, 30.14% 52.39%, 50% 52.39%, 50% 47.61%)\",\n            \"polygon(50% 47.61%, 50% 52.39%, 69.86% 52.39%, 69.86% 47.61%)\",\n            \"polygon(40.85% 40.85%, 50% 40.85%, 40.85% 50%)\",\n            \"polygon(59.15% 40.85%, 50% 40.85%, 59.15% 50%)\",\n            \"polygon(40.85% 59.15%, 50% 59.15%, 40.85% 50%)\",\n            \"polygon(59.15% 59.15%, 50% 59.15%, 59.15% 50%)\",\n            \"polygon(50% 40.85%, 40.85% 50%, 50% 59.15%, 59.15% 50%)\"\n        ],\n        \"area\": [\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0095,\n            0.0228,\n            0.0228,\n            0.0228,\n            0.0228,\n            0.0336,\n            0.0336,\n            0.0336,\n            0.0336,\n            0.0027,\n            0.0027,\n            0.0027,\n            0.0027,\n            0.0027,\n            0.0027,\n            0.0027,\n            0.0027,\n            0.0019,\n            0.0019,\n            0.0019,\n            0.0019,\n            0.0015,\n            0.0015,\n            0.0015,\n            0.0015,\n            0.0060,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/11\",\n        \"bg\": require('../images/bg/ming/11/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 25% 25%)\",\n            \"polygon(100% 0, 50% 0, 75% 25%)\",\n            \"polygon(0 100%, 50% 100%, 25% 75%)\",\n            \"polygon(100% 100%, 50% 100%, 75% 75%)\",\n            \"polygon(0 0, 0 50%, 25% 25%)\",\n            \"polygon(0 100%, 0 50%, 25% 75%)\",\n            \"polygon(100% 0, 100% 50%, 75% 25%)\",\n            \"polygon(100% 100%, 100% 50%, 75% 75%)\",\n            \"polygon(0 0, 16.5% 0, 21.4% 4.9%, 4.9% 4.9%)\",\n            \"polygon(0 0, 0 16.5%, 4.9% 21.4%, 4.9% 4.9%)\",\n            \"polygon(100% 0, 83.5% 0, 78.6% 4.9%, 95.1% 4.9%)\",\n            \"polygon(100% 0, 100% 16.5%, 95.1% 21.4%, 95.1% 4.9%)\",\n            \"polygon(100% 100%, 100% 83.5%, 95.1% 78.6%, 95.1% 95.1%)\",\n            \"polygon(100% 100%, 83.5% 100%, 78.6% 95.1%, 95.1% 95.1%)\",\n            \"polygon(0 100%, 0 83.5%, 4.9% 78.6%, 4.9% 95.1%)\",\n            \"polygon(0 100%, 16.5% 100%, 21.4% 95.1%, 4.9% 95.1%)\",\n            \"polygon(50% 0, 25% 25%, 50% 50%)\",\n            \"polygon(50% 0, 75% 25%, 50% 50%)\",\n            \"polygon(50% 100%, 25% 75%, 50% 50%)\",\n            \"polygon(50% 100%, 75% 75%, 50% 50%)\",\n            \"polygon(0 50%, 25% 25%, 50% 50%)\",\n            \"polygon(0 50%, 25% 75%, 50% 50%)\",\n            \"polygon(100% 50%, 75% 25%, 50% 50%)\",\n            \"polygon(100% 50%, 75% 75%, 50% 50%)\",\n            \"polygon(44.9% 28.4%, 50% 33.3%, 50% 50%, 44.9% 45.1%)\",\n            \"polygon(55.1% 28.4%, 50% 33.3%, 50% 50%, 55.1% 45.1%)\",\n            \"polygon(28.4% 44.9%, 33.3% 50%, 50% 50%, 45.1% 44.9%)\",\n            \"polygon(28.4% 55.1%, 33.3% 50%, 50% 50%, 45.1% 55.1%)\",\n            \"polygon(71.6% 44.9%, 66.7% 50%, 50% 50%, 54.9% 44.9%)\",\n            \"polygon(71.6% 55.1%, 66.7% 50%, 50% 50%, 54.9% 55.1%)\",\n            \"polygon(44.9% 71.6%, 50% 66.7%, 50% 50%, 44.9% 55.1%)\",\n            \"polygon(55.1% 71.6%, 50% 66.7%, 50% 50%, 55.1% 55.1%)\",\n            \"polygon(4.9% 4.9%, 21.4% 4.9%, 44.9% 28.4%, 44.9% 44.9%)\",\n            \"polygon(4.9% 4.9%, 4.9% 21.4%, 28.4% 44.9%, 44.9% 44.9%)\",\n            \"polygon(4.9% 95.1%, 21.4% 95.1%, 44.9% 71.6%, 44.9% 55.1%)\",\n            \"polygon(95.1% 4.9%, 95.1% 21.4%, 71.6% 44.9%, 55.1% 44.9%)\",\n            \"polygon(95.1% 4.9%, 78.6% 4.9%, 55.1% 28.4%, 55.1% 44.9%)\",\n            \"polygon(4.9% 95.1%, 4.9% 78.6%, 28.4% 55.1%, 44.9% 55.1%)\",\n            \"polygon(95.1% 95.1%, 78.6% 95.1%, 55.1% 71.6%, 55.1% 55.1%)\",\n            \"polygon(95.1% 95.1%, 95.1% 78.6%, 71.6% 55.1%, 55.1% 55.1%)\",\n        ],\n        \"area\": [\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0030,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0100,\n            0.0031,\n            0.0031,\n            0.0031,\n            0.0031,\n            0.0031,\n            0.0031,\n            0.0031,\n            0.0031,\n            0.0189,\n            0.0189,\n            0.0189,\n            0.0189,\n            0.0189,\n            0.0189,\n            0.0189,\n            0.0189,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/16\",\n        \"bg\": require('../images/bg/ming/16/bg.png'),\n        \"clipPath\": [\n            \"polygon(14.66% 14.66%, 19.2% 14.66%, 19.2% 54.54%, 14.66% 54.54%)\",\n            \"polygon(14.66% 14.66%, 35.34% 14.66%, 35.34% 19.2%, 14.66% 19.2%)\",\n            \"polygon(85.34% 85.34%, 80.8% 85.34%, 80.8% 45.46%, 85.34% 45.46%)\",\n            \"polygon(85.34% 85.34%, 64.66% 85.34%, 64.66% 80.8%, 85.34% 80.8%)\",\n            \"polygon(45.45% 14.66%, 85.34% 14.66%, 85.34% 19.2%, 45.45% 19.2%)\",\n            \"polygon(80.8% 14.66%, 85.34% 14.66%, 85.34% 35.34%, 80.8% 35.34%)\",\n            \"polygon(54.55% 85.34%, 14.66% 85.34%, 14.66% 80.8%, 54.55% 80.8%)\",\n            \"polygon(19.2% 85.34%, 14.66% 85.34%, 14.66% 64.66%, 19.2% 64.66%)\",\n            \"polygon(19.2% 19.2%, 19.2% 80.8%, 50% 50%)\",\n            \"polygon(19.2% 80.8%, 80.8% 80.8%, 50% 50%)\",\n            \"polygon(80.8% 19.2%, 80.8% 80.8%, 50% 50%)\",\n            \"polygon(19.2% 19.2%, 80.8% 19.2%, 50% 50%)\",\n            \"polygon(19.2% 19.2%, 19.2% 30.8%, 30.8% 19.2%)\",\n            \"polygon(19.2% 80.8%, 19.2% 69.2%, 30.8% 80.8%)\",\n            \"polygon(80.8% 19.2%, 69.2% 19.2%, 80.8% 30.8%)\",\n            \"polygon(80.8% 80.8%, 69.2% 80.8%, 80.8% 69.2%)\",\n            \"polygon(0 0, 50% 0, 50% 14.66%, 0 14.66%)\",\n            \"polygon(50% 0, 85.34% 0, 85.34% 14.66%, 50% 14.66%)\",\n            \"polygon(85.34% 0, 100% 0, 100% 50%, 85.34% 50%)\",\n            \"polygon(85.34% 50%, 100% 50%, 100% 85.34%, 85.34% 85.34%)\",\n            \"polygon(50% 85.34%, 50% 100%, 14.66% 100%, 14.66% 85.34%)\",\n            \"polygon(100% 85.34%, 100% 100%, 50% 100%, 50% 85.34%)\",\n            \"polygon(14.66% 50%, 0% 50%, 0 14.66%, 14.66% 14.66%)\",\n            \"polygon(14.66% 100%, 0% 100%, 0 50%, 14.66% 50%)\",\n            \"polygon(50% 0, 50% 14.66%, 19.2% 45.46%, 19.2% 30.8%)\",\n            \"polygon(14.66% 35.34%, 14.66% 50%, 0% 50%)\",\n            \"polygon(54.54% 19.2%, 69.2% 19.2%, 100% 50%, 85.34% 50%)\",\n            \"polygon(50% 0%, 50% 14.66%, 64.66% 14.66%)\",\n            \"polygon(80.8% 54.54%, 80.8% 69.2%, 50% 100%, 50% 85.34%)\",\n            \"polygon(85.34% 50%, 100% 50%, 85.34% 64.66%)\",\n            \"polygon(45.46% 80.8%, 30.8% 80.8%, 0 50%, 14.66% 50%)\",\n            \"polygon(50% 85.34%, 50% 100%, 35.34% 85.34%)\"\n        ],\n        \"area\": [\n            0.0054,\n            0.0030,\n            0.0054,\n            0.0030,\n            0.0054,\n            0.0030,\n            0.0054,\n            0.0030,\n            0.0219,\n            0.0219,\n            0.0219,\n            0.0219,\n            0.0026,\n            0.0026,\n            0.0026,\n            0.0026,\n            0.0224,\n            0.0148,\n            0.0224,\n            0.0148,\n            0.0148,\n            0.0224,\n            0.0148,\n            0.0224,\n            0.0162,\n            0.0038,\n            0.0162,\n            0.0038,\n            0.0162,\n            0.0038,\n            0.0162,\n            0.0038,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/18\",\n        \"bg\": require('../images/bg/ming/18/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 0 100%, 50% 50%)\",\n            \"polygon(100% 0, 100% 100%, 50% 50%)\",\n            \"polygon(0 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 100% 100%, 50% 50%)\",\n            \"polygon(8.53% 8.53%, 25.31% 8.53%, 8.53% 25.31%)\",\n            \"polygon(91.47% 8.53%, 74.69% 8.53%, 91.47% 25.31%)\",\n            \"polygon(8.53% 91.47%, 25.31% 91.47%, 8.53% 74.69%)\",\n            \"polygon(91.47% 91.47%, 74.69% 91.47%, 91.47% 74.69%)\",\n            \"polygon(20.14% 20.14%, 20.14% 79.86%, 50% 50%)\",\n            \"polygon(79.86% 20.14%, 79.86% 79.86%, 50% 50%)\",\n            \"polygon(20.14% 20.14%, 79.86% 20.14%, 50% 50%)\",\n            \"polygon(20.14% 79.86%, 79.86% 79.86%, 50% 50%)\",\n            \"polygon(20.14% 25.31%, 25.31% 20.14%, 79.86% 74.69%, 74.69% 79.86%)\",\n            \"polygon(39.07% 55.85%, 44.15% 60.93%, 25.31% 79.86%, 20.14% 74.69%)\",\n            \"polygon(60.93% 55.85%, 55.85% 60.93%, 74.69% 79.86%, 79.86% 74.69%)\",\n            \"polygon(60.93% 44.15%, 55.89% 39.07%, 74.69% 20.14%, 79.86% 25.31%)\",\n            \"polygon(39.07% 44.15%, 44.11% 39.07%, 25.31% 20.14%, 20.14% 25.31%)\",\n            \"polygon(25.31% 8.53%, 25.31% 20.14%, 20.14% 25.31%, 8.53% 25.31%)\",\n            \"polygon(74.69% 8.53%, 74.69% 20.14%, 79.86% 25.31%, 91.47% 25.31%)\",\n            \"polygon(25.31% 91.47%, 25.31% 79.86%, 20.14% 74.69%, 8.53% 74.69%)\",\n            \"polygon(74.69% 91.47%, 74.69% 79.86%, 79.86% 74.69%, 91.47% 74.69%)\",\n            \"polygon(25.31% 8.53%, 25.31% 20.14%, 55.04% 50%, 60.93% 44.15%)\",\n            \"polygon(74.69% 91.47%, 74.69% 79.86%, 44.96% 50%, 39.07% 55.85%)\",\n            \"polygon(79.86% 74.69%, 91.47% 74.69%, 66.78% 50%, 60.93% 55.89%)\",\n            \"polygon(20.14% 25.31%, 8.53% 25.31%, 33.22% 50%, 39.07% 44.11%)\",\n            \"polygon(91.47% 25.31%, 79.86% 25.31%, 50% 55.04%, 55.85% 60.93%)\",\n            \"polygon(25.31% 79.86%, 25.31% 91.47%, 50% 66.78%, 44.11% 60.93%)\",\n            \"polygon(8.53% 74.69%, 20.14% 74.69%, 50% 44.96%, 44.15% 39.07%)\",\n            \"polygon(74.69% 20.14%, 74.69% 8.53%, 50% 33.22%, 55.89% 39.07%)\",\n            \"polygon(0 0, 91.47% 0, 91.47% 8.53%, 0 8.53%)\",\n            \"polygon(91.47% 0, 100% 0, 100% 91.47%, 91.47% 91.47%)\",\n            \"polygon(100% 91.47%, 100% 100%, 8.53% 100%, 8.53% 91.47%)\",\n            \"polygon(0 8.53%, 8.53% 8.53%, 8.53% 100%, 0 100%)\"\n        ],\n        \"area\": [\n            0.0161,\n            0.0161,\n            0.0161,\n            0.0161,\n            0.0050,\n            0.0050,\n            0.0050,\n            0.0050,\n            0.0065,\n            0.0065,\n            0.0065,\n            0.0065,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0018,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0137,\n            0.0137,\n            0.0093,\n            0.0093,\n            0.0137,\n            0.0093,\n            0.0137,\n            0.0093,\n            0.0275,\n            0.0275,\n            0.0275,\n            0.0275,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/23\",\n        \"bg\": require('../images/bg/ming/23/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%, 0 50%)\",\n            \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(100% 50%, 100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 50%, 0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(50% 15.32%, 15.32% 50%, 50% 50%)\",\n            \"polygon(50% 15.32%, 84.68% 50%, 50% 50%)\",\n            \"polygon(50% 84.68%, 15.32% 50%, 50% 50%)\",\n            \"polygon(84.68% 50%, 50% 84.68%, 50% 50%)\",\n            \"polygon(50% 15.32%, 54.84% 20.16%, 50% 25%, 45.16% 20.16%)\",\n            \"polygon(50% 84.68%, 54.84% 79.84%, 50% 75%, 45.16% 79.84%)\",\n            \"polygon(15.32% 50%, 20.16% 54.84%, 25% 50%, 20.16% 45.16%)\",\n            \"polygon(84.68% 50%, 79.84% 54.84%, 75% 50%, 79.84% 45.16%)\",\n            \"polygon(50% 25%, 25% 50%, 50% 75%, 75% 50%)\",\n            \"polygon(0 0, 91.94% 0, 91.94% 8.06%, 0 8.06%)\",\n            \"polygon(91.94% 0, 100% 0, 100% 91.94%, 91.94% 91.94%)\",\n            \"polygon(100% 91.94%, 100% 100%, 8.06% 100%, 8.06% 91.94%)\",\n            \"polygon(0 8.06%, 8.06% 8.06%, 8.06% 100%, 0 100%)\"\n        ],\n        \"area\": [\n            0.0405,\n            0.0405,\n            0.0405,\n            0.0405,\n            0.0087,\n            0.0087,\n            0.0087,\n            0.0087,\n            0.0016,\n            0.0016,\n            0.0016,\n            0.0016,\n            0.0471,\n            0.0275,\n            0.0275,\n            0.0275,\n            0.0275,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/25\",\n        \"bg\": require('../images/bg/ming/25/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(50% 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 0, 0 50%, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(35.6% 0, 50% 0, 50% 14.39%, 32.195% 32.195%, 17.8% 17.8%)\",\n            \"polygon(64.4% 0, 50% 0, 50% 14.39%, 67.805% 32.195%, 82.2% 17.8%)\",\n            \"polygon(35.6% 100%, 50% 100%, 50% 85.61%, 32.195% 67.805%, 17.8% 82.2%)\",\n            \"polygon(64.4% 100%, 50% 100%, 50% 85.61%, 67.805% 67.805%, 82.2% 82.2%)\",\n            \"polygon(14.39% 50%, 37.195% 27.195%, 37.195% 72.805%)\",\n            \"polygon(85.61% 50%, 62.805% 27.195%, 62.805% 72.805%)\",\n            \"polygon(50% 14.39%, 72.805% 37.195%, 27.195% 37.195%)\",\n            \"polygon(50% 85.61%, 72.805% 62.805%, 27.195% 62.805%)\",\n            \"polygon(17.8% 17.8%, 32.195% 32.195%, 14.39% 50%, 0 50%, 0 35.6%)\",\n            \"polygon(82.2% 17.8%, 67.805% 32.195%, 85.61% 50%, 100% 50%, 100% 35.6%)\",\n            \"polygon(17.8% 82.2%, 32.195% 67.805%, 14.39% 50%, 0 50%, 0 64.4%)\",\n            \"polygon(82.2% 82.2%, 67.805% 67.805%, 85.61% 50%, 100% 50%, 100% 64.4%)\",\n            \"polygon(0 0, 13.45% 0, 50% 36.55%,50% 50%, 36.55% 50%, 0 13.45%)\",\n            \"polygon(100% 0, 86.55% 0, 50% 36.55%, 50% 50%, 63.45% 50%, 100% 13.45%)\",\n            \"polygon(100% 100%, 86.55% 100%, 50% 63.45%, 50% 50%, 63.45% 50%, 100% 86.55%)\",\n            \"polygon(0 100%, 13.45% 100%, 50% 63.45%, 50% 50%, 36.55% 50%, 0 86.55%)\"\n        ],\n        \"area\": [\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0417,\n            0.0417,\n            0.0417,\n            0.0417,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/28\",\n        \"bg\": require('../images/bg/ming/28/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%, 0 50%)\",\n            \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(100% 50%, 100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 50%, 0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(25.2% 0, 31.3% 0, 43.42% 10.72%, 40.8% 14.2%)\",\n            \"polygon(100% 25.2%, 100% 31.3%, 89.28% 43.42%, 85.8% 40.8%)\",\n            \"polygon(74.8% 100%, 68.7% 100%, 56.38% 89.28%, 59.2% 85.8%)\",\n            \"polygon(0 74.8%, 0 68.7%, 10.72% 56.38%, 14.2% 59.2%)\",\n            \"polygon(47.2% 9.54%, 53.05% 9.54%, 19.2% 43.6%, 16.3% 40.5%)\",\n            \"polygon(52.8% 90.46%, 46.95% 90.46%, 80.8% 56.4%, 83.7% 59.5%)\",\n            \"polygon(9.54% 52.8%, 9.54% 46.95%, 43.6% 80.8%, 40.5% 83.7%)\",\n            \"polygon(90.46% 47.2%, 90.46% 53.05%, 56.4% 19.2%, 59.5% 16.3%)\",\n            \"polygon(67.5% 0, 73.5% 0, 53.05% 20.5%, 47% 20.5%)\",\n            \"polygon(32.5% 100%, 26.5% 100%, 46.95% 79.5%, 53% 79.5%)\",\n            \"polygon(100% 67.5%, 100% 73.5%, 79.5% 53.05%, 79.5% 47%)\",\n            \"polygon(0 32.5%, 0 26.5%, 20.5% 46.95%, 20.5% 53%)\",\n            \"polygon(0 0, 22.8% 0, 39.2% 15%, 15% 39.2%, 0 24%)\",\n            \"polygon(100% 0, 76% 0, 60.8% 15%, 85% 39.2%, 100% 24%)\",\n            \"polygon(0 100%, 24% 100%, 39.2% 85%, 15% 60.8%, 0 76%)\",\n            \"polygon(100% 100%, 76% 100%, 60.8% 85%, 85% 60.8%, 100% 76%)\",\n            \"polygon(22.8% 0, 25.2% 0, 40.8% 14.2%, 39.2% 15%)\",\n            \"polygon(100% 22.8%, 100% 25.2%, 85.8% 40.8%, 85% 39.2%)\",\n            \"polygon(0 77.2%, 0 74.8%, 14.2% 59.2%, 15% 60.8%)\",\n            \"polygon(77.2% 100%, 74.8% 100%, 59.2% 85.8%, 60.8% 85%)\",\n            \"polygon(31.3% 0, 33.9% 0, 44.6% 9.54%, 43.42% 10.72%)\",\n            \"polygon(100% 31.3%, 100% 33.9%, 90.46% 44.6%, 89.28% 43.42%)\",\n            \"polygon(0 68.7%, 0 66.1%, 9.54% 55.4%, 10.72% 56.58%)\",\n            \"polygon(68.7% 100%, 66.1% 100%, 55.4% 90.46%, 56.58% 89.28%)\",\n            \"polygon(44.6% 9.54%, 47.2% 9.54%, 16.3% 40.5%, 15% 39.3%)\",\n            \"polygon(90.46% 44.6%, 90.46% 47.2%, 59.5% 16.3%, 60.7% 15%)\",\n            \"polygon(9.54% 55.4%, 9.54% 52.8%, 40.5% 83.7%, 39.3% 85%)\",\n            \"polygon(55.4% 90.46%, 52.8% 90.46%, 83.7% 59.5%, 85% 60.7%)\",\n            \"polygon(53.05% 9.54%, 56% 9.54%, 20.5% 44.6%, 19.2% 43.6%)\",\n            \"polygon(90.46% 53.05%, 90.46% 56%, 55.4% 20.5%, 56.4% 19.2%)\",\n            \"polygon(9.54% 46.95%, 9.54% 44%, 44.6% 79.5%, 43.6% 80.8%)\",\n            \"polygon(46.95% 90.46%, 44% 90.46%, 79.5% 55.4%, 80.8% 56.4%)\",\n            \"polygon(44.8% 20.5%, 47% 20.5%, 67.5% 0, 65% 0)\",\n            \"polygon(79.5% 44.8%, 79.5% 47%, 100% 67.5%, 100% 65%)\",\n            \"polygon(20.5% 55.2%, 20.5% 53%, 0 32.5%, 0 35%)\",\n            \"polygon(55.2% 79.5%, 53% 79.5%, 32.5% 100%, 35% 100%)\",\n            \"polygon(53.05% 20.5%, 55.4% 20.5%, 76% 0, 73.5% 0)\",\n            \"polygon(79.5% 53.05%, 79.5% 55.4%, 100% 76%, 100% 73.5%)\",\n            \"polygon(20.5% 46.95%, 20.5% 44.6%, 0 24%, 0 26.5%)\",\n            \"polygon(46.95% 79.5%, 44.6% 79.5%, 24% 100%, 26.5% 100%)\",\n            \"polygon(33.9% 0, 65% 0, 55.6% 9.54%, 44.6% 9.54%)\",\n            \"polygon(100% 33.9%, 100% 65%, 90.46% 55.6%, 90.46% 44.6%)\",\n            \"polygon(0 66.1%, 0 35%, 9.54% 44.4%, 9.54% 55.4%)\",\n            \"polygon(66.1% 100%, 35% 100%, 44.4% 90.46%, 55.4% 90.46%)\"\n        ],\n        \"area\": [\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0045,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0152,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0090,\n            0.0417,\n            0.0417,\n            0.0417,\n            0.0417,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/34\",\n        \"bg\": require('../images/bg/ming/34/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(50% 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 0, 0 50%, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(8.33% 8.33%, 13.89% 8.33%, 52.78% 47.22%, 47.22% 52.78%, 8.33% 13.89%)\",\n            \"polygon(47.22% 52.78%, 52.78% 47.22%, 91.67% 86.11%, 91.67% 91.67%, 86.11% 91.67%)\",\n            \"polygon(91.67% 8.33%, 86.11% 8.33%, 47.22% 47.22%, 52.78% 52.78%, 91.67% 13.89%)\",\n            \"polygon(52.78% 52.78%, 47.22% 47.22%, 8.33% 86.11%, 8.33% 91.67%, 13.89% 91.67%)\",\n            \"polygon(50% 0, 0 50%, 50% 50%)\",\n            \"polygon(50% 0, 100% 50%, 50% 50%)\",\n            \"polygon(50% 100%, 0 50%, 50% 50%)\",\n            \"polygon(50% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(50% 13.89%, 31.945% 31.945%, 50% 50%)\",\n            \"polygon(50% 13.89%, 68.055% 31.945%, 50% 50%)\",\n            \"polygon(50% 86.11%, 31.945% 68.055%, 50% 50%)\",\n            \"polygon(50% 86.11%, 68.055% 68.055%, 50% 50%)\",\n            \"polygon(13.89% 50%, 31.945% 31.945%, 50% 50%)\",\n            \"polygon(86.11% 50%, 68.055% 31.945%, 50% 50%)\",\n            \"polygon(13.89% 50%, 31.945% 68.055%, 50% 50%)\",\n            \"polygon(86.11% 50%, 68.055% 68.055%, 50% 50%)\",\n            \"polygon(31.94% 31.94%, 50% 31.94%, 50% 50%)\",\n            \"polygon(68.06% 31.94%, 50% 31.94%, 50% 50%)\",\n            \"polygon(68.06% 68.06%, 50% 68.06%, 50% 50%)\",\n            \"polygon(31.94% 68.06%, 50% 68.06%, 50% 50%)\",\n            \"polygon(31.94% 31.94%, 31.94% 50%, 50% 50%)\",\n            \"polygon(68.06% 31.94%, 68.06% 50%, 50% 50%)\",\n            \"polygon(31.94% 68.06%, 31.94% 50%, 50% 50%)\",\n            \"polygon(68.06% 68.06%, 68.06% 50%, 50% 50%)\",\n            \"polygon(40.28% 40.28%, 59.72% 40.28%, 59.72% 59.72%, 40.28% 59.72%)\",\n            \"polygon(8.33% 0, 100% 0, 100% 8.33%, 8.33% 8.33%)\",\n            \"polygon(91.67% 8.33%, 100% 8.33%, 100% 100%, 91.67% 100%)\",\n            \"polygon(0 91.67%, 91.67% 91.67%, 91.67% 100%, 0 100%)\",\n            \"polygon(0 0, 8.33% 0, 8.33% 91.67%, 0 91.67%)\"\n        ],\n        \"area\": [\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0088,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0069,\n            0.0156,\n            0.0156,\n            0.0156,\n            0.0156,\n            0.0060,\n            0.0060,\n            0.0060,\n            0.0060,\n            0.0060,\n            0.0060,\n            0.0060,\n            0.0060,\n            0.0043,\n            0.0043,\n            0.0043,\n            0.0043,\n            0.0043,\n            0.0043,\n            0.0043,\n            0.0043,\n            0.0131,\n            0.0266,\n            0.0266,\n            0.0266,\n            0.0266,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/38\",\n        \"bg\": require('../images/bg/ming/38/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(50% 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 0, 0 50%, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(15.04% 15.04%, 50% 15.04%, 50% 50%)\",\n            \"polygon(84.96% 15.04%, 50% 15.04%, 50% 50%)\",\n            \"polygon(15.04% 84.96%, 50% 84.96%, 50% 50%)\",\n            \"polygon(84.96% 84.96%, 50% 84.96%, 50% 50%)\",\n            \"polygon(15.04% 15.04%, 15.04% 50%, 50% 50%)\",\n            \"polygon(84.96% 15.04%, 84.96% 50%, 50% 50%)\",\n            \"polygon(15.04% 84.96%, 15.04% 50%, 50% 50%)\",\n            \"polygon(84.96% 84.96%, 84.96% 50%, 50% 50%)\",\n            \"polygon(50% 0, 34.96% 15.04%, 50% 50%)\",\n            \"polygon(50% 0, 65.04% 15.04%, 50% 50%)\",\n            \"polygon(50% 100%, 34.96% 84.96%, 50% 50%)\",\n            \"polygon(50% 100%, 65.04% 84.96%, 50% 50%)\",\n            \"polygon(0 50%, 15.04% 34.96%, 50% 50%)\",\n            \"polygon(0 50%, 15.04% 65.04%, 50% 50%)\",\n            \"polygon(100% 50%, 84.96% 34.96%, 50% 50%)\",\n            \"polygon(100% 50%, 84.96% 65.04%, 50% 50%)\"\n        ],\n        \"area\": [\n            0.0187,\n            0.0187,\n            0.0187,\n            0.0187,\n            0.0187,\n            0.0187,\n            0.0187,\n            0.0187,\n            0.0132,\n            0.0132,\n            0.0132,\n            0.0132,\n            0.0132,\n            0.0132,\n            0.0132,\n            0.0132,\n            0.0131,\n            0.0131,\n            0.0131,\n            0.0131,\n            0.0131,\n            0.0131,\n            0.0131,\n            0.0131,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/39\",\n        \"bg\": require('../images/bg/ming/39/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(50% 0, 100% 0, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 0, 0 50%, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(50% 0, 34.85% 13.48%, 50% 50%)\",\n            \"polygon(50% 0, 65.15% 13.48%, 50% 50%)\",\n            \"polygon(50% 100%, 34.85% 86.52%, 50% 50%)\",\n            \"polygon(50% 100%, 65.15% 86.52%, 50% 50%)\",\n            \"polygon(0 50%, 13.48% 34.85%, 50% 50%)\",\n            \"polygon(0 50%, 13.48% 65.15%, 50% 50%)\",\n            \"polygon(100% 50%, 86.52% 34.85%, 50% 50%)\",\n            \"polygon(100% 50%, 86.52% 65.15%, 50% 50%)\",\n            \"polygon(0 0, 34.85% 13.48%, 50% 50%)\",\n            \"polygon(0 0, 13.48% 34.85%, 50% 50%)\",\n            \"polygon(100% 0, 65.15% 13.48%, 50% 50%)\",\n            \"polygon(100% 0, 86.52% 34.85%, 50% 50%)\",\n            \"polygon(0 100%, 34.85% 86.52%, 50% 50%)\",\n            \"polygon(0 100%, 13.48% 65.15%, 50% 50%)\",\n            \"polygon(100% 100%, 65.15% 86.52%, 50% 50%)\",\n            \"polygon(100% 100%, 86.52% 65.15%, 50% 50%)\"\n        ],\n        \"area\": [\n            0.0120,\n            0.0120,\n            0.0120,\n            0.0120,\n            0.0120,\n            0.0120,\n            0.0120,\n            0.0120,\n            0.0137,\n            0.0137,\n            0.0137,\n            0.0137,\n            0.0137,\n            0.0137,\n            0.0137,\n            0.0137,\n            0.0193,\n            0.0193,\n            0.0193,\n            0.0193,\n            0.0193,\n            0.0193,\n            0.0193,\n            0.0193,\n        ],\n        \"profit\": 450\n    },\n    {\n        \"path\": \"ming/40\",\n        \"bg\": require('../images/bg/ming/40/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%, 0 50%)\",\n            \"polygon(50% 0, 100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%, 0 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%, 100% 50%)\",\n            \"polygon(11.32% 11.32%, 22.64% 22.64%, 22.64% 50%, 11.32% 50%)\",\n            \"polygon(11.32% 50%, 22.64% 50%, 22.64% 77.36%, 11.32% 88.68%)\",\n            \"polygon(88.68% 11.32%, 77.36% 22.64%, 77.36% 50%, 88.68% 50%)\",\n            \"polygon(88.68% 50%, 77.36% 50%, 77.36% 77.36%, 88.68% 88.68%)\",\n            \"polygon(11.32% 11.32%, 22.64% 22.64%, 50% 22.64%, 50% 11.32%)\",\n            \"polygon(50% 11.32%, 50% 22.64%, 77.36% 22.64%, 88.68% 11.32%)\",\n            \"polygon(11.32% 88.68%, 22.64% 77.36%, 50% 77.36%, 50% 88.68%)\",\n            \"polygon(50% 88.68%, 50% 77.36%, 77.36% 77.36%, 88.68% 88.68%)\",\n            \"polygon(50% 11.32%, 38.68% 22.64%, 50% 50%, 61.32% 22.64%)\",\n            \"polygon(50% 88.68%, 38.68% 77.36%, 50% 50%, 61.32% 77.36%)\",\n            \"polygon(11.32% 50%, 22.64% 38.68%, 50% 50%, 22.64% 61.32%)\",\n            \"polygon(88.68% 50%, 77.36% 38.68%, 50% 50%, 77.36% 61.32%)\",\n            \"polygon(0 0, 100% 0, 88.68% 11.32%, 11.32% 11.32%)\",\n            \"polygon(0 100%, 100% 100%, 88.68% 88.68%, 11.32% 88.68%)\",\n            \"polygon(0 0, 11.32% 11.32%, 11.32% 88.68%, 0 100%)\",\n            \"polygon(100% 0, 88.68% 11.32%, 88.68% 88.68%, 100% 100%)\"\n        ],\n        \"area\": [\n            0.0157,\n            0.0157,\n            0.0157,\n            0.0157,\n            0.0112,\n            0.0112,\n            0.0112,\n            0.0112,\n            0.0112,\n            0.0112,\n            0.0112,\n            0.0112,\n            0.0157,\n            0.0157,\n            0.0157,\n            0.0157,\n            0.0362,\n            0.0362,\n            0.0362,\n            0.0362,\n        ],\n        \"profit\": 450\n    }\n]\n\nexport const qing = [\n    {\n        \"path\": \"qing/13\",\n        \"bg\": require('../images/bg/qing/13/bg.png'),\n        \"clipPath\": [\n            \"path('M 0 0 L 0 100% L 23.8% 76.2% A 20.2% 20.2% 0 0 1 22.1% 50% A 20.2% 20.2% 0 0 1 23.8% 23.8% Z')\",\n            \"path('M 27.35% 27.35% A 15.15% 15.15% 0 0 0 29.3% 50% L 50% 50% Z')\",\n            \"path('M 29.3% 50% A 15.15% 15.15% 0 0 0 27.35% 72.65% L 50% 50% Z')\",\n            \"path('M 0 0 L 100% 0 L 76.2% 23.8% A 20.2% 20.2% 0 0 0 50% 22.1% A 20.2% 20.2% 0 0 0 23.8% 23.8% Z')\",\n            \"path('M 27.35% 27.35% A 15.15% 15.15% 0 0 1 50% 29.3% L 50% 50% Z')\",\n            \"path('M 50% 29.3% A 15.15% 15.15% 0 0 1 72.65% 27.35% L 50% 50% Z')\",\n            \"path('M 100% 100% L 0 100% L 23.8% 76.2% A 20.2% 20.2% 0 0 0 50% 77.9% A 20.2% 20.2% 0 0 0 76.2% 76.2% Z')\",\n            \"path('M 72.65% 72.65% A 15.15% 15.15% 0 0 1 50% 70.7% L 50% 50% Z')\",\n            \"path('M 50% 70.7% A 15.15% 15.15% 0 0 1 27.35% 72.65% L 50% 50% Z')\",\n            \"path('M 100% 0 L 100% 100% L 76.2% 76.2% A 20.2% 20.2% 0 0 0 77.9% 50% A 20.2% 20.2% 0 0 0 76.2% 23.8% Z')\",\n            \"path('M 72.65% 27.35% A 15.15% 15.15% 0 0 1 70.7% 50% L 50% 50% Z')\",\n            \"path('M 70.7% 50% A 15.15% 15.15% 0 0 1 72.65% 72.65% L 50% 50% Z')\",\n            \"path('M 22.1% 50% A 20.2% 20.2% 0 0 1 23.8% 23.8% L 27.35% 27.35% A 15.15% 15.15% 0 0 0 29.3% 50% Z')\",\n            \"path('M 23.8% 23.8% A 20.2% 20.2% 0 0 1 50% 22.1% L 50% 29.3% A 15.15% 15.15% 0 0 0 27.35% 27.35% Z')\",\n            \"path('M 50% 22.1% A 20.2% 20.2% 0 0 1 76.2% 23.8% L 72.65% 27.35% A 15.15% 15.15% 0 0 0 50% 29.3% Z')\",\n            \"path('M 76.2% 23.8% A 20.2% 20.2% 0 0 1 77.9% 50% L 70.7% 50% A 15.15% 15.15% 0 0 0 72.65% 27.35% Z')\",\n            \"path('M 77.9% 50% A 20.2% 20.2% 0 0 1 76.2% 76.2% L 72.65% 72.65% A 15.15% 15.15% 0 0 0 70.7% 50% Z')\",\n            \"path('M 76.2% 76.2% A 20.2% 20.2% 0 0 1 50% 77.9% L 50% 70.7% A 15.15% 15.15% 0 0 0 72.65% 72.65% Z')\",\n            \"path('M 50% 77.9% A 20.2% 20.2% 0 0 1 23.8% 76.2% L 27.35% 72.65% A 15.15% 15.15% 0 0 0 50% 70.7% Z')\",\n            \"path('M 23.8% 76.2% A 20.2% 20.2% 0 0 1 22.1% 50% L 29.3% 50% A 15.15% 15.15% 0 0 0 27.35% 72.65% Z')\",\n        ],\n        \"area\": [\n            0.0574,\n            0.0119,\n            0.0119,\n            0.0574,\n            0.0119,\n            0.0119,\n            0.0574,\n            0.0119,\n            0.0119,\n            0.0574,\n            0.0119,\n            0.0119,\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0044,\n            0.0044,\n        ],\n        \"profit\": 500\n    },\n    {\n        \"path\": \"qing/30\",\n        \"bg\": require('../images/bg/qing/30/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 50% 0, 50% 50%)\",\n            \"polygon(0 0, 0 50%, 50% 50%)\",\n            \"polygon(100% 0, 50% 0, 50% 50%)\",\n            \"polygon(100% 0, 100% 50%, 50% 50%)\",\n            \"polygon(0 100%, 50% 100%, 50% 50%)\",\n            \"polygon(0 100%, 0 50%, 50% 50%)\",\n            \"polygon(100% 100%, 50% 100%, 50% 50%)\",\n            \"polygon(100% 100%, 100% 50%, 50% 50%)\",\n            \"polygon(50% 1.7%, 25.85% 25.85%, 50% 50%)\",\n            \"polygon(25.85% 25.85%, 1.7% 50%, 50% 50%)\",\n            \"polygon(50% 1.7%, 74.15% 25.85%, 50% 50%)\",\n            \"polygon(74.15% 25.85%, 98.3% 50%, 50% 50%)\",\n            \"polygon(50% 98.3%, 25.85% 74.15%, 50% 50%)\",\n            \"polygon(25.85% 74.15%, 1.7% 50%, 50% 50%)\",\n            \"polygon(50% 98.3%, 74.15% 74.15%, 50% 50%)\",\n            \"polygon(74.15% 74.15%, 98.3% 50%, 50% 50%)\",\n            \"polygon(50% 12%, 31% 31%, 50% 50%)\",\n            \"polygon(31% 31%, 12% 50%, 50% 50%)\",\n            \"polygon(50% 12%, 69% 31%, 50% 50%)\",\n            \"polygon(69% 31%, 88% 50%, 50% 50%)\",\n            \"polygon(50% 88%, 69% 69%, 50% 50%)\",\n            \"polygon(69% 69%, 88% 50%, 50% 50%)\",\n            \"polygon(50% 88%, 31% 69%, 50% 50%)\",\n            \"polygon(31% 69%, 12% 50%, 50% 50%)\",\n            \"polygon(50% 20%, 35% 35%, 50% 50%)\",\n            \"polygon(35% 35%, 20% 50%, 50% 50%)\",\n            \"polygon(50% 20%, 65% 35%, 50% 50%)\",\n            \"polygon(65% 35%, 80% 50%, 50% 50%)\",\n            \"polygon(50% 80%, 65% 65%, 50% 50%)\",\n            \"polygon(65% 65%, 80% 50%, 50% 50%)\",\n            \"polygon(50% 80%, 35% 65%, 50% 50%)\",\n            \"polygon(35% 65%, 20% 50%, 50% 50%)\",\n            \"path('M 28.5% 28.5% A 26% 26% 0 0 1 50% 50% Z')\",\n            \"path('M 28.5% 28.5% A 26% 26% 0 0 0 50% 50% Z')\",\n            \"path('M 71.5% 28.5% A 26% 26% 0 0 1 50% 50% Z')\",\n            \"path('M 71.5% 28.5% A 26% 26% 0 0 0 50% 50% Z')\",\n            \"path('M 28.5% 71.5% A 26% 26% 0 0 1 50% 50% Z')\",\n            \"path('M 28.5% 71.5% A 26% 26% 0 0 0 50% 50% Z')\",\n            \"path('M 71.5% 71.5% A 26% 26% 0 0 1 50% 50% Z')\",\n            \"path('M 71.5% 71.5% A 26% 26% 0 0 0 50% 50% Z')\",\n            \"polygon(7.1% 7.1%, 15.5% 7.9%, 20.8% 20.8%, 7.9% 15.5%)\",\n            \"polygon(7.1% 92.9%, 15.5% 92.1%, 20.8% 79.2%, 7.9% 84.5%)\",\n            \"polygon(92.9% 92.9%, 84.5% 92.1%, 79.2% 79.2%, 92.1% 84.5%)\",\n            \"polygon(92.9% 7.1%, 84.5% 7.9%, 79.2% 20.8%, 92.1% 15.5%)\"\n        ],\n        \"area\": [\n            0.0206,\n            0.0206,\n            0.0206,\n            0.0206,\n            0.0206,\n            0.0206,\n            0.0206,\n            0.0206,\n            0.0082,\n            0.0082,\n            0.0082,\n            0.0082,\n            0.0082,\n            0.0082,\n            0.0082,\n            0.0082,\n            0.0046,\n            0.0046,\n            0.0046,\n            0.0046,\n            0.0046,\n            0.0046,\n            0.0046,\n            0.0046,\n            0.0061,\n            0.0061,\n            0.0061,\n            0.0061,\n            0.0061,\n            0.0061,\n            0.0061,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n            0.0037,\n        ],\n        \"profit\": 500\n    },\n    {\n        \"path\": \"qing/35\",\n        \"bg\": require('../images/bg/qing/35/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 0% 2.65%, 90.15% 2.65%, 90.15% 0%)\",\n            \"polygon(0 9.85%, 0% 7.2%, 90.15% 7.2%, 90.15% 9.85%)\",\n            \"polygon(90.15% 0, 92.8% 0, 92.8% 90.15%, 90.15% 90.15%)\",\n            \"polygon(97.35% 0, 100% 0, 100% 90.15%, 97.35% 90.15%)\",\n            \"polygon(100% 90.15%, 100% 92.8%, 9.85% 92.8%, 9.85% 90.15%)\",\n            \"polygon(100% 100%, 100% 97.35%, 9.85% 97.35%, 9.85% 100%)\",\n            \"polygon(0 9.85%, 2.65% 9.85%, 2.65% 100%, 0 100%)\",\n            \"polygon(9.85% 9.85%, 7.2% 9.85%, 7.2% 100%, 9.85% 100%)\",\n            \"polygon(0 2.65%, 0 7.2%, 90.15% 7.2%, 90.15% 2.65%)\",\n            \"polygon(97.35% 0, 92.8% 0, 92.8% 90.15%, 97.35% 90.15%)\",\n            \"polygon(100% 97.35%, 100% 92.8%, 9.85% 92.8%, 9.85% 97.35%)\",\n            \"polygon(2.65% 100%, 7.2% 100%, 7.2% 9.85%, 2.65% 9.85%)\",\n            \"path('M 33.3% 33.3% A 3.3% 3.3% 0 0 0 33.3% 38% A 17.6% 17.6% 0 0 1 33.3% 62% A 3.3% 3.3% 0 0 0 33.3% 66.7% L 50% 50% Z')\",\n            \"path('M 33.3% 33.3% A 3.3% 3.3% 0 0 1 38% 33.3% A 17.6% 17.6% 0 0 0 62% 33.3% A 3.3% 3.3% 0 0 1 66.7% 33.3% L 50% 50% Z')\",\n            \"path('M 33.3% 66.7% A 3.3% 3.3% 0 0 0 38% 66.7% A 17.6% 17.6% 0 0 1 62% 66.7% A 3.3% 3.3% 0 0 0 66.7% 66.7% L 50% 50% Z')\",\n            \"path('M 66.7% 33.3% A 3.3% 3.3% 0 0 1 66.7% 38% A 17.6% 17.6% 0 0 0 66.7% 62% A 3.3% 3.3% 0 0 1 66.7% 66.7% L 50% 50% Z')\",\n\n            \"path('M 9.85% 9.85% L 33.3% 33.3% A 3.3% 3.3% 0 0 0 33.3% 38% A 17.6% 17.6% 0 0 1 33.3% 62% A 3.3% 3.3% 0 0 0 33.3% 66.7% L 9.85% 90.15% Z')\",\n            \"path('M 9.85% 9.85% L 33.3% 33.3% A 3.3% 3.3% 0 0 1 38% 33.3% A 17.6% 17.6% 0 0 0 62% 33.3% A 3.3% 3.3% 0 0 1 66.7% 33.3% L 90.15% 9.85% Z')\",\n            \"path('M 9.85% 90.15% L 33.3% 66.7% A 3.3% 3.3% 0 0 0 38% 66.7% A 17.6% 17.6% 0 0 1 62% 66.7% A 3.3% 3.3% 0 0 0 66.7% 66.7% L 90.15% 90.15% Z')\",\n            \"path('M 90.15% 9.85% L 66.7% 33.3% A 3.3% 3.3% 0 0 1 66.7% 38% A 17.6% 17.6% 0 0 0 66.7% 62% A 3.3% 3.3% 0 0 1 66.7% 66.7% L 90.15% 90.15% Z')\",\n        ],\n        \"area\": [\n            0.0081,\n            0.0081,\n            0.0081,\n            0.0081,\n            0.0081,\n            0.0081,\n            0.0081,\n            0.0081,\n            0.0158,\n            0.0158,\n            0.0158,\n            0.0158,\n            0.0074,\n            0.0074,\n            0.0074,\n            0.0074,\n            0.0506,\n            0.0506,\n            0.0506,\n            0.0506,\n        ],\n        \"profit\": 500\n    }\n]\n\nexport const changgui = [\n    {\n        \"path\": \"changgui/人字拼\",\n        \"bg\": require('../images/bg/changgui/人字拼/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 0, 12.5% 0, 12.5% 60%, 0 60%)\",\n            \"polygon(25% 12.5%, 25% 25%, 85% 25%, 85% 12.5%)\",\n            \"polygon(37.5% 37.5%, 50% 37.5%, 50% 97.5%, 37.5% 97.5%)\",\n            \"polygon(62.5% 50%, 62.5% 62.5%, 100% 62.5%, 100% 50%)\",\n            \"polygon(75% 75%, 87.5% 75%, 87.5% 100%, 75% 100%)\",\n            \"polygon(0 72.5%, 25% 72.5%, 25% 85%, 0 85%)\",\n            \"polygon(12.5% 0, 12.5% 12.5%, 72.5% 12.5%, 72.5% 0)\",\n            \"polygon(25% 25%, 37.5% 25% , 37.5% 85%, 25% 85%)\",\n            \"polygon(50% 37.5%, 100% 37.5%, 100% 50%, 50% 50%)\",\n            \"polygon(62.5% 62.5%, 75% 62.5%, 75% 100%, 62.5% 100%)\",\n            \"polygon(87.5% 75%, 87.5% 87.5%, 100% 87.5%, 100% 75%)\",\n            \"polygon(0 60%, 12.5% 60%, 12.5% 72.5%, 0 72.5%)\",\n            \"polygon(0 97.5%, 50% 97.5%, 50% 100%, 0 100%)\",\n            \"polygon(85% 0, 97.5% 0, 97.5% 25%, 85% 25%)\",\n            \"polygon(12.5% 12.5%, 25% 12.5%, 25% 72.5%, 12.5% 72.5%)\",\n            \"polygon(37.5% 25%, 37.5% 37.5%, 97.5% 37.5%, 97.5% 25%)\",\n            \"polygon(50% 50%, 62.5% 50%, 62.5% 100%, 50% 100%)\",\n            \"polygon(75% 62.5%, 75% 75%, 100% 75%, 100% 62.5%)\",\n            \"polygon(87.5% 87.5%, 100% 87.5%, 100% 100%, 87.5% 100%)\",\n            \"polygon(0 85%, 37.5% 85%, 37.5% 97.5%, 0 97.5%)\",\n            \"polygon(72.5% 0, 85% 0, 85% 12.5%, 72.5% 12.5%)\",\n            \"polygon(97.5% 0, 100% 0, 100% 37.5%, 97.5% 37.5%)\"\n        ]\n    },\n    {\n        \"path\": \"changgui/鱼骨拼\",\n        \"bg\": require('../images/bg/changgui/鱼骨拼/bg.png'),\n        \"clipPath\": [\n            \"polygon(50% 0, 50% 17.7%, 0 67.7%, 0 50%)\",\n            \"polygon(50% 17.7%, 50% 35.4%, 100% 85.4%, 100% 67.7%)\",\n            \"polygon(50% 53.1%, 50% 70.8%, 0 120.8%, 0 103.1%)\",\n            \"polygon(50% 70.8%, 50% 88.5%, 100% 138.5%, 100% 120.8%)\",\n            \"polygon(50% -35.4%, 50% -17.7%, 0 32.3%, 0 14.6%)\",\n            \"polygon(50% -53.1%, 50% -35.4%, 100% 14.6%, 100% -3.1%)\",\n            \"polygon(50% 0, 50% 17.7%, 100% 67.7%, 100% 50%)\",\n            \"polygon(50% 35.4%, 50% 53.1%, 0 103.1%, 0 85.4%)\",\n            \"polygon(50% 53.1%, 50% 70.8%, 100% 120.8%, 100% 103.1%)\",\n            \"polygon(50% 88.5%, 50% 106.2%, 0 156.2%, 0 138.5%)\",\n            \"polygon(50% -17.7%, 50% 0, 0 50%, 0 32.3%)\",\n            \"polygon(50% -35.4%, 50% -17.7%, 100% 32.3%, 100% 14.6%)\",\n            \"polygon(50% 17.7%, 50% 35.4%, 0 85.4%, 0 67.7%)\",\n            \"polygon(50% 35.4%, 50% 53.1%, 100% 103.1%, 100% 85.4%)\",\n            \"polygon(50% 70.8%, 50% 88.5%, 0 138.5%, 0 120.8%)\",\n            \"polygon(50% 88.5%, 50% 106.2%, 100% 156.2%, 100% 138.5%)\",\n            \"polygon(50% -17.7%, 50% 0, 100% 50%, 100% 32.3%)\",\n            \"polygon(50% -53.1%, 50% -35.4%, 0 14.6%, 0 -3.1%)\"\n        ]\n    },\n    {\n        \"path\": \"changgui/长条\",\n        \"bg\": require('../images/bg/changgui/长条/bg.png'),\n        \"clipPath\": [\n            \"polygon(0 10.42%,100% 10.42%, 100% 20.84%, 0 20.84%)\",\n            \"polygon(0 52.1%,100% 52.1%, 100% 62.52%, 0 62.52%)\",\n            \"polygon(0 93.78%,100% 93.78%, 100% 100%, 0 100%)\",\n            \"polygon(0 0,50% 0, 50% 10.42%, 0 10.42%)\",\n            \"polygon(0 31.26%,50% 31.26%, 50% 20.84%, 0 20.84%)\",\n            \"polygon(0 41.68%,50% 41.68%, 50% 52.1%, 0 52.1%)\",\n            \"polygon(0 62.52%,50% 62.52%, 50% 72.94%, 0 72.94%)\",\n            \"polygon(0 83.36%,50% 83.36%, 50% 93.78%, 0 93.78%)\",\n            \"polygon(0 31.26%,100% 31.26%, 100% 41.68%, 0 41.68%)\",\n            \"polygon(0 72.94%,100% 72.94%, 100% 83.36%, 0 83.36%)\",\n            \"polygon(50% 0,100% 0, 100% 10.42%, 50% 10.42%)\",\n            \"polygon(50% 31.26%,100% 31.26%, 100% 20.84%, 50% 20.84%)\",\n            \"polygon(50% 41.68%,100% 41.68%, 100% 52.1%, 50% 52.1%)\",\n            \"polygon(50% 62.52%,100% 62.52%, 100% 72.94%, 50% 72.94%)\",\n            \"polygon(50% 83.36%,100% 83.36%, 100% 93.78%, 50% 93.78%)\",\n            \"polygon(25% 10.42%,75% 10.42%, 75% 20.84%, 25% 20.84%)\",\n            \"polygon(25% 31.26%,75% 31.26%, 75% 41.68%, 25% 41.68%)\",\n            \"polygon(25% 52.1%,75% 52.1%, 75% 62.52%, 25% 62.52%)\",\n            \"polygon(25% 72.94%,75% 72.94%, 75% 83.36%, 25% 83.36%)\",\n            \"polygon(25% 93.78%,75% 93.78%, 75% 104.2%, 25% 104.2%)\"\n        ]\n    }\n]"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEC,OAAO,CAAC,0BAA0B,CAAC;EAC1C,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,8BAA8B,CAAC;IAC5CI,cAAc,EAAE,aAAa;IAC7BC,MAAM,EAAE,iCAAiC;IACzCC,YAAY,EAAE,sDAAsD;IACpEC,OAAO,EAAE;EACb;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEP,OAAO,CAAC,0BAA0B,CAAC;EAC1C,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,6BAA6B,CAAC;IAC3CI,cAAc,EAAE,kBAAkB;IAClCC,MAAM,EAAE,eAAe;IACvBC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEP,OAAO,CAAC,yBAAyB,CAAC;EACzC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,4BAA4B,CAAC;IAC1CI,cAAc,EAAE,aAAa;IAC7BC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,+CAA+C;IAC7DC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEP,OAAO,CAAC,2BAA2B,CAAC;EAC3C,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,8BAA8B,CAAC;IAC5CI,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,YAAY;IACpBC,YAAY,EAAE,0BAA0B;IACxCC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEP,OAAO,CAAC,2BAA2B,CAAC;EAC3C,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,+BAA+B,CAAC;IAC7CI,cAAc,EAAE,qBAAqB;IACrCC,MAAM,EAAE,YAAY;IACpBC,YAAY,EAAE,oBAAoB;IAClCC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEP,OAAO,CAAC,0BAA0B,CAAC;EAC1C,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,6BAA6B,CAAC;IAC3CI,cAAc,EAAE,qBAAqB;IACrCC,MAAM,EAAE,YAAY;IACpBC,YAAY,EAAE,yCAAyC;IACvDC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,CAAC;EACP,KAAK,EAAEP,OAAO,CAAC,0BAA0B,CAAC;EAC1C,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,6BAA6B,CAAC;IAC3CI,cAAc,EAAE,qBAAqB;IACrCC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,iFAAiF;IAC/FC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,EAAE;EACR,KAAK,EAAEP,OAAO,CAAC,8BAA8B,CAAC;EAC9C,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,iCAAiC,CAAC;IAC/CI,cAAc,EAAE,sBAAsB;IACtCC,MAAM,EAAE,cAAc;IACtBC,YAAY,EAAE,6BAA6B;IAC3CC,OAAO,EAAE;EACb;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,EAAE,EAAE;EACR,KAAK,EAAEP,OAAO,CAAC,2BAA2B,CAAC;EAC3C,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,8BAA8B,CAAC;IAC5CI,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,gCAAgC;IAC9CC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,EAAE;EACR,KAAK,EAAEP,OAAO,CAAC,0BAA0B,CAAC;EAC1C,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,6BAA6B,CAAC;IAC3CI,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,gBAAgB;IAC9BC,OAAO,EAAE;EACb;AACJ,CAAC,EACD;EACI,IAAI,EAAE,EAAE;EACR,KAAK,EAAEP,OAAO,CAAC,2BAA2B,CAAC;EAC3C,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE;IACFC,GAAG,EAAEH,OAAO,CAAC,6BAA6B,CAAC;IAC3CI,cAAc,EAAE,gBAAgB;IAChCC,MAAM,EAAE,OAAO;IACfC,YAAY,EAAE,mDAAmD;IACjEC,OAAO,EAAE;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACH;AAED,OAAO,MAAMC,KAAK,GAAG;EACjB,MAAM,EAAE,OAAO;EACf,UAAU,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,IAAI,GAAG,CAChB;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAET,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,+BAA+B,EAC/B,qCAAqC,EACrC,qCAAqC,EACrC,+BAA+B,EAC/B,qDAAqD,EACrD,4CAA4C,EAC5C,4CAA4C,EAC5C,qDAAqD,EACrD,wCAAwC,EACxC,8CAA8C,CACjD;EACD,MAAM,EAAE,CACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACV;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEA,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,uCAAuC,EACvC,uCAAuC,EACvC,0CAA0C,EAC1C,6CAA6C,EAC7C,6CAA6C,EAC7C,0CAA0C,EAC1C,gDAAgD,EAChD,gDAAgD,EAChD,yCAAyC,EACzC,yCAAyC,EACzC,+CAA+C,EAC/C,+CAA+C,EAC/C,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,CACvC;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEA,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,uCAAuC,EACvC,gDAAgD,EAChD,gDAAgD,EAChD,uCAAuC,EACvC,6CAA6C,EAC7C,6CAA6C,EAC7C,0CAA0C,EAC1C,6CAA6C,EAC7C,6CAA6C,EAC7C,0CAA0C,EAC1C,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,CACvC;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEA,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,+BAA+B,EAC/B,qCAAqC,EACrC,+BAA+B,EAC/B,qCAAqC,EACrC,4CAA4C,EAC5C,4CAA4C,EAC5C,4CAA4C,EAC5C,4CAA4C,EAC5C,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,CACxD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,qCAAqC,EACrC,+BAA+B,EAC/B,qCAAqC,EACrC,gDAAgD,EAChD,gDAAgD,EAChD,uFAAuF,CAC1F;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,qCAAqC,EACrC,+BAA+B,EAC/B,qCAAqC,EACrC,yDAAyD,EACzD,yDAAyD,EACzD,yDAAyD,EACzD,yDAAyD,CAC5D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,qCAAqC,EACrC,+BAA+B,EAC/B,qCAAqC,EACrC,oEAAoE,EACpE,yEAAyE,EACzE,yEAAyE,EACzE,+EAA+E,CAClF;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,CACJ;AAED,OAAO,MAAMU,IAAI,GAAG,CAChB;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEV,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,8BAA8B,EAC9B,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,oCAAoC,EACpC,iCAAiC,EACjC,uCAAuC,EACvC,uCAAuC,EACvC,2CAA2C,EAC3C,iDAAiD,EACjD,2CAA2C,EAC3C,qCAAqC,EACrC,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,qDAAqD,EACrD,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,CAClE;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEA,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,sCAAsC,EACtC,sCAAsC,EACtC,4CAA4C,EAC5C,4CAA4C,EAC5C,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,2CAA2C,EAC3C,2CAA2C,EAC3C,8CAA8C,EAC9C,iDAAiD,EACjD,iDAAiD,EACjD,8CAA8C,EAC9C,oDAAoD,EACpD,oDAAoD,EACpD,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,CAChD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEA,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,qCAAqC,EACrC,6CAA6C,EAC7C,qCAAqC,EACrC,6CAA6C,EAC7C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,4CAA4C,EAC5C,4CAA4C,EAC5C,4CAA4C,EAC5C,4CAA4C,EAC5C,0CAA0C,EAC1C,yDAAyD,EACzD,yDAAyD,EACzD,yDAAyD,EACzD,yDAAyD,EACzD,qDAAqD,EACrD,sDAAsD,EACtD,qDAAqD,EACrD,sDAAsD,EACtD,qDAAqD,EACrD,sDAAsD,EACtD,qDAAqD,EACrD,sDAAsD,EACtD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,8CAA8C,EAC9C,8CAA8C,EAC9C,+CAA+C,EAC/C,gDAAgD,EAChD,gDAAgD,EAChD,+CAA+C,EAC/C,iDAAiD,EACjD,iDAAiD,CACpD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,+BAA+B,EAC/B,qCAAqC,EACrC,qCAAqC,EACrC,sDAAsD,EACtD,sDAAsD,EACtD,+DAA+D,EAC/D,+DAA+D,EAC/D,qEAAqE,EACrE,sEAAsE,CACzE;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,uCAAuC,EACvC,oCAAoC,EACpC,iCAAiC,EACjC,8BAA8B,EAC9B,qDAAqD,EACrD,2DAA2D,EAC3D,qDAAqD,EACrD,2DAA2D,EAC3D,6CAA6C,EAC7C,6CAA6C,EAC7C,gDAAgD,EAChD,mDAAmD,EACnD,mDAAmD,EACnD,gDAAgD,EAChD,sDAAsD,EACtD,sDAAsD,CACzD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,qCAAqC,EACrC,2CAA2C,EAC3C,iDAAiD,EACjD,2CAA2C,EAC3C,2CAA2C,EAC3C,8CAA8C,EAC9C,2CAA2C,EAC3C,8CAA8C,EAC9C,qEAAqE,EACrE,qEAAqE,EACrE,qEAAqE,EACrE,2CAA2C,EAC3C,iDAAiD,EACjD,iDAAiD,EACjD,uDAAuD,CAC1D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,4BAA4B,EAC5B,kCAAkC,EAClC,kCAAkC,EAClC,wCAAwC,EACxC,iDAAiD,EACjD,uDAAuD,EACvD,6DAA6D,EAC7D,wDAAwD,EACxD,wEAAwE,EACxE,wEAAwE,EACxE,wEAAwE,EACxE,wEAAwE,EACxE,yDAAyD,CAC5D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,+BAA+B,EAC/B,qCAAqC,EACrC,qCAAqC,EACrC,+DAA+D,EAC/D,2DAA2D,EAC3D,6DAA6D,EAC7D,6DAA6D,EAC7D,+DAA+D,EAC/D,2DAA2D,EAC3D,6DAA6D,EAC7D,iEAAiE,EACjE,+DAA+D,EAC/D,iEAAiE,CACpE;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,qCAAqC,EACrC,2CAA2C,EAC3C,2CAA2C,EAC3C,iDAAiD,EACjD,uCAAuC,EACvC,uCAAuC,EACvC,+CAA+C,EAC/C,+CAA+C,EAC/C,0DAA0D,EAC1D,0DAA0D,EAC1D,+DAA+D,EAC/D,gEAAgE,EAChE,+DAA+D,EAC/D,gEAAgE,EAChE,2EAA2E,EAC3E,kDAAkD,EAClD,sDAAsD,EACtD,oDAAoD,EACpD,oDAAoD,EACpD,kEAAkE,EAClE,kEAAkE,CACrE;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,EAAE,EACF,+CAA+C,EAC/C,+CAA+C,EAC/C,yCAAyC,EACzC,qDAAqD,EACrD,oDAAoD,EACpD,qDAAqD,EACrD,kDAAkD,EAClD,wDAAwD,EACxD,mDAAmD,EACnD,yDAAyD,EACzD,yDAAyD,EACzD,mDAAmD,EACnD,gCAAgC,EAChC,4CAA4C,EAC5C,sDAAsD,EACtD,oDAAoD,EACpD,oDAAoD,EACpD,sDAAsD,CACzD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,EAAE,EACF,iDAAiD,EACjD,yDAAyD,EACzD,iDAAiD,EACjD,yDAAyD,CAC5D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,CACJ;AAGD,OAAO,MAAMW,IAAI,GAAG,CAChB;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEX,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,4BAA4B,EAC5B,kCAAkC,EAClC,wCAAwC,EACxC,kCAAkC,EAClC,qCAAqC,EACrC,2CAA2C,EAC3C,2CAA2C,EAC3C,iDAAiD,EACjD,sCAAsC,EACtC,sCAAsC,EACtC,yCAAyC,EACzC,yCAAyC,EACzC,oCAAoC,EACpC,oCAAoC,EACpC,uCAAuC,EACvC,uCAAuC,EACvC,8DAA8D,EAC9D,8DAA8D,EAC9D,4DAA4D,CAC/D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,uCAAuC,EACvC,oCAAoC,EACpC,iCAAiC,EACjC,2CAA2C,EAC3C,8CAA8C,EAC9C,8CAA8C,EAC9C,2CAA2C,EAC3C,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,qEAAqE,EACrE,qEAAqE,EACrE,sEAAsE,EACtE,wDAAwD,EACxD,yDAAyD,EACzD,uDAAuD,EACvD,uDAAuD,CAC1D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,+BAA+B,EAC/B,qCAAqC,EACrC,qCAAqC,EACrC,iDAAiD,EACjD,iDAAiD,EACjD,uDAAuD,EACvD,oDAAoD,EACpD,oDAAoD,EACpD,uDAAuD,EACvD,0DAA0D,EAC1D,0DAA0D,EAC1D,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,CAE1D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,gCAAgC,EAChC,kDAAkD,EAClD,wDAAwD,EACxD,uDAAuD,EACvD,wCAAwC,EACxC,sDAAsD,EACtD,yDAAyD,EACzD,uDAAuD,EACvD,0DAA0D,EAC1D,gEAAgE,EAChE,6DAA6D,EAC7D,yDAAyD,EACzD,uCAAuC,EACvC,yDAAyD,EACzD,8DAA8D,EAC9D,6DAA6D,EAC7D,+CAA+C,EAC/C,6DAA6D,EAC7D,+DAA+D,EAC/D,6DAA6D,EAC7D,8DAA8D,EAC9D,iEAAiE,EACjE,6DAA6D,EAC7D,yDAAyD,EACzD,8CAA8C,EAC9C,8DAA8D,EAC9D,+DAA+D,EAC/D,6DAA6D,EAC7D,+CAA+C,EAC/C,6DAA6D,EAC7D,+DAA+D,EAC/D,4DAA4D,EAC5D,8DAA8D,EAC9D,iEAAiE,EACjE,6DAA6D,EAC7D,yDAAyD,EACzD,uCAAuC,EACvC,uDAAuD,EACvD,yDAAyD,EACzD,uDAAuD,EACvD,wCAAwC,EACxC,sDAAsD,EACtD,yDAAyD,EACzD,sDAAsD,EACtD,0DAA0D,EAC1D,gEAAgE,EAChE,6DAA6D,EAC7D,yDAAyD,EACzD,yDAAyD,CAC5D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,gCAAgC,EAChC,mCAAmC,EACnC,sCAAsC,EACtC,mCAAmC,EACnC,+CAA+C,EAC/C,+CAA+C,EAC/C,iDAAiD,EACjD,iDAAiD,EACjD,sDAAsD,EACtD,sDAAsD,EACtD,sDAAsD,EACtD,sDAAsD,EACtD,0DAA0D,EAC1D,0DAA0D,EAC1D,6DAA6D,EAC7D,0DAA0D,EAC1D,4DAA4D,EAC5D,4DAA4D,EAC5D,2DAA2D,EAC3D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,8DAA8D,EAC9D,6DAA6D,EAC7D,+DAA+D,EAC/D,+DAA+D,EAC/D,6DAA6D,EAC7D,8DAA8D,EAC9D,2CAA2C,EAC3C,mDAAmD,EACnD,uDAAuD,EACvD,+CAA+C,CAClD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,+BAA+B,EAC/B,qCAAqC,EACrC,qCAAqC,EACrC,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,qDAAqD,EACrD,qDAAqD,EACrD,wDAAwD,EACxD,wDAAwD,EACxD,+DAA+D,EAC/D,+DAA+D,EAC/D,gDAAgD,EAChD,gDAAgD,CACnD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,CACJ;AAED,OAAO,MAAMY,IAAI,GAAG,CAChB;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEZ,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,wCAAwC,EACxC,wCAAwC,EACxC,8CAA8C,EAC9C,8CAA8C,EAC9C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,CAChD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,QAAQ;EAChB,IAAI,EAAEA,OAAO,CAAC,4BAA4B,CAAC;EAC3C,UAAU,EAAE,CACR,gCAAgC,EAChC,sCAAsC,EACtC,sCAAsC,EACtC,4CAA4C,EAC5C,kCAAkC,EAClC,kCAAkC,EAClC,wCAAwC,EACxC,wCAAwC,EACxC,kCAAkC,EAClC,wCAAwC,EACxC,kCAAkC,EAClC,wCAAwC,EACxC,0CAA0C,EAC1C,6CAA6C,EAC7C,6CAA6C,EAC7C,gDAAgD,EAChD,2DAA2D,EAC3D,iEAAiE,EACjE,2DAA2D,EAC3D,iEAAiE,EACjE,oFAAoF,EACpF,oFAAoF,EACpF,oFAAoF,EACpF,oFAAoF,EACpF,oFAAoF,EACpF,oFAAoF,EACpF,oFAAoF,EACpF,oFAAoF,EACpF,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,gDAAgD,EAChD,gDAAgD,EAChD,gDAAgD,EAChD,gDAAgD,EAChD,yDAAyD,CAC5D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,8CAA8C,EAC9C,8CAA8C,EAC9C,kDAAkD,EAClD,sDAAsD,EACtD,0DAA0D,EAC1D,0DAA0D,EAC1D,kDAAkD,EAClD,sDAAsD,EACtD,kCAAkC,EAClC,kCAAkC,EAClC,qCAAqC,EACrC,qCAAqC,EACrC,kCAAkC,EAClC,kCAAkC,EAClC,qCAAqC,EACrC,qCAAqC,EACrC,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,0DAA0D,EAC1D,0DAA0D,EAC1D,4DAA4D,EAC5D,4DAA4D,EAC5D,2DAA2D,EAC3D,2DAA2D,EAC3D,6DAA6D,EAC7D,6DAA6D,CAChE;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,mEAAmE,EACnE,mEAAmE,EACnE,mEAAmE,EACnE,mEAAmE,EACnE,mEAAmE,EACnE,mEAAmE,EACnE,mEAAmE,EACnE,mEAAmE,EACnE,4CAA4C,EAC5C,4CAA4C,EAC5C,4CAA4C,EAC5C,4CAA4C,EAC5C,gDAAgD,EAChD,gDAAgD,EAChD,gDAAgD,EAChD,gDAAgD,EAChD,2CAA2C,EAC3C,qDAAqD,EACrD,iDAAiD,EACjD,2DAA2D,EAC3D,2DAA2D,EAC3D,uDAAuD,EACvD,sDAAsD,EACtD,kDAAkD,EAClD,uDAAuD,EACvD,4CAA4C,EAC5C,0DAA0D,EAC1D,4CAA4C,EAC5C,0DAA0D,EAC1D,8CAA8C,EAC9C,uDAAuD,EACvD,8CAA8C,CACjD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,+BAA+B,EAC/B,qCAAqC,EACrC,+BAA+B,EAC/B,qCAAqC,EACrC,kDAAkD,EAClD,oDAAoD,EACpD,oDAAoD,EACpD,sDAAsD,EACtD,gDAAgD,EAChD,gDAAgD,EAChD,gDAAgD,EAChD,gDAAgD,EAChD,qEAAqE,EACrE,qEAAqE,EACrE,qEAAqE,EACrE,qEAAqE,EACrE,qEAAqE,EACrE,mEAAmE,EACnE,oEAAoE,EACpE,oEAAoE,EACpE,qEAAqE,EACrE,iEAAiE,EACjE,kEAAkE,EAClE,kEAAkE,EAClE,iEAAiE,EACjE,kEAAkE,EAClE,kEAAkE,EAClE,iEAAiE,EACjE,iEAAiE,EACjE,+CAA+C,EAC/C,uDAAuD,EACvD,2DAA2D,EAC3D,mDAAmD,CACtD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,qCAAqC,EACrC,2CAA2C,EAC3C,iDAAiD,EACjD,2CAA2C,EAC3C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,6CAA6C,EAC7C,+CAA+C,EAC/C,uDAAuD,EACvD,2DAA2D,EAC3D,mDAAmD,CACtD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,8BAA8B,EAC9B,oCAAoC,EACpC,iCAAiC,EACjC,uCAAuC,EACvC,mEAAmE,EACnE,mEAAmE,EACnE,yEAAyE,EACzE,yEAAyE,EACzE,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,uDAAuD,EACvD,mEAAmE,EACnE,yEAAyE,EACzE,mEAAmE,EACnE,yEAAyE,EACzE,kEAAkE,EAClE,yEAAyE,EACzE,+EAA+E,EAC/E,yEAAyE,CAC5E;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,qCAAqC,EACrC,2CAA2C,EAC3C,iDAAiD,EACjD,2CAA2C,EAC3C,uDAAuD,EACvD,6DAA6D,EAC7D,6DAA6D,EAC7D,uDAAuD,EACvD,8DAA8D,EAC9D,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE,EAChE,oDAAoD,EACpD,0DAA0D,EAC1D,0DAA0D,EAC1D,oDAAoD,EACpD,oDAAoD,EACpD,wDAAwD,EACxD,wDAAwD,EACxD,8DAA8D,EAC9D,mDAAmD,EACnD,yDAAyD,EACzD,mDAAmD,EACnD,yDAAyD,EACzD,uDAAuD,EACvD,8DAA8D,EAC9D,uDAAuD,EACvD,8DAA8D,EAC9D,2DAA2D,EAC3D,6DAA6D,EAC7D,2DAA2D,EAC3D,6DAA6D,EAC7D,4DAA4D,EAC5D,8DAA8D,EAC9D,4DAA4D,EAC5D,8DAA8D,EAC9D,iDAAiD,EACjD,uDAAuD,EACvD,iDAAiD,EACjD,uDAAuD,EACvD,oDAAoD,EACpD,0DAA0D,EAC1D,oDAAoD,EACpD,0DAA0D,EAC1D,mDAAmD,EACnD,2DAA2D,EAC3D,mDAAmD,EACnD,2DAA2D,CAC9D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,8BAA8B,EAC9B,oCAAoC,EACpC,iCAAiC,EACjC,uCAAuC,EACvC,gFAAgF,EAChF,oFAAoF,EACpF,kFAAkF,EAClF,kFAAkF,EAClF,gCAAgC,EAChC,mCAAmC,EACnC,mCAAmC,EACnC,sCAAsC,EACtC,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C,EAC/C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,qEAAqE,EACrE,mDAAmD,EACnD,2DAA2D,EAC3D,uDAAuD,EACvD,+CAA+C,CAClD;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,8BAA8B,EAC9B,oCAAoC,EACpC,iCAAiC,EACjC,uCAAuC,EACvC,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,wCAAwC,EACxC,wCAAwC,EACxC,2CAA2C,EAC3C,2CAA2C,EAC3C,wCAAwC,EACxC,wCAAwC,EACxC,2CAA2C,EAC3C,2CAA2C,CAC9C;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,uCAAuC,EACvC,8BAA8B,EAC9B,oCAAoC,EACpC,iCAAiC,EACjC,uCAAuC,EACvC,wCAAwC,EACxC,wCAAwC,EACxC,2CAA2C,EAC3C,2CAA2C,EAC3C,wCAAwC,EACxC,wCAAwC,EACxC,2CAA2C,EAC3C,2CAA2C,EAC3C,sCAAsC,EACtC,sCAAsC,EACtC,yCAAyC,EACzC,yCAAyC,EACzC,yCAAyC,EACzC,yCAAyC,EACzC,4CAA4C,EAC5C,4CAA4C,CAC/C;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,qCAAqC,EACrC,2CAA2C,EAC3C,2CAA2C,EAC3C,iDAAiD,EACjD,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,+DAA+D,EAC/D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,oDAAoD,EACpD,0DAA0D,EAC1D,oDAAoD,EACpD,0DAA0D,CAC7D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,CACJ;AAED,OAAO,MAAMa,IAAI,GAAG,CAChB;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEb,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,sGAAsG,EACtG,qEAAqE,EACrE,qEAAqE,EACrE,sGAAsG,EACtG,qEAAqE,EACrE,qEAAqE,EACrE,4GAA4G,EAC5G,qEAAqE,EACrE,qEAAqE,EACrE,4GAA4G,EAC5G,qEAAqE,EACrE,qEAAqE,EACrE,uGAAuG,EACvG,uGAAuG,EACvG,uGAAuG,EACvG,uGAAuG,EACvG,uGAAuG,EACvG,uGAAuG,EACvG,uGAAuG,EACvG,uGAAuG,CAC1G;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,8BAA8B,EAC9B,8BAA8B,EAC9B,iCAAiC,EACjC,oCAAoC,EACpC,oCAAoC,EACpC,iCAAiC,EACjC,uCAAuC,EACvC,uCAAuC,EACvC,2CAA2C,EAC3C,2CAA2C,EAC3C,2CAA2C,EAC3C,4CAA4C,EAC5C,4CAA4C,EAC5C,2CAA2C,EAC3C,4CAA4C,EAC5C,4CAA4C,EAC5C,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,oCAAoC,EACpC,iDAAiD,EACjD,iDAAiD,EACjD,iDAAiD,EACjD,iDAAiD,EACjD,iDAAiD,EACjD,iDAAiD,EACjD,iDAAiD,EACjD,iDAAiD,EACjD,yDAAyD,EACzD,2DAA2D,EAC3D,6DAA6D,EAC7D,2DAA2D,CAC9D;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,EACD;EACI,MAAM,EAAE,SAAS;EACjB,IAAI,EAAEA,OAAO,CAAC,6BAA6B,CAAC;EAC5C,UAAU,EAAE,CACR,iDAAiD,EACjD,sDAAsD,EACtD,yDAAyD,EACzD,uDAAuD,EACvD,6DAA6D,EAC7D,2DAA2D,EAC3D,mDAAmD,EACnD,yDAAyD,EACzD,qDAAqD,EACrD,yDAAyD,EACzD,6DAA6D,EAC7D,yDAAyD,EACzD,2HAA2H,EAC3H,2HAA2H,EAC3H,2HAA2H,EAC3H,2HAA2H,EAE3H,8IAA8I,EAC9I,8IAA8I,EAC9I,gJAAgJ,EAChJ,gJAAgJ,CACnJ;EACD,MAAM,EAAE,CACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;EACD,QAAQ,EAAE;AACd,CAAC,CACJ;AAED,OAAO,MAAMc,QAAQ,GAAG,CACpB;EACI,MAAM,EAAE,cAAc;EACtB,IAAI,EAAEd,OAAO,CAAC,kCAAkC,CAAC;EACjD,UAAU,EAAE,CACR,yCAAyC,EACzC,iDAAiD,EACjD,yDAAyD,EACzD,uDAAuD,EACvD,mDAAmD,EACnD,6CAA6C,EAC7C,qDAAqD,EACrD,kDAAkD,EAClD,mDAAmD,EACnD,uDAAuD,EACvD,uDAAuD,EACvD,iDAAiD,EACjD,+CAA+C,EAC/C,6CAA6C,EAC7C,yDAAyD,EACzD,yDAAyD,EACzD,mDAAmD,EACnD,mDAAmD,EACnD,yDAAyD,EACzD,iDAAiD,EACjD,iDAAiD,EACjD,mDAAmD;AAE3D,CAAC,EACD;EACI,MAAM,EAAE,cAAc;EACtB,IAAI,EAAEA,OAAO,CAAC,kCAAkC,CAAC;EACjD,UAAU,EAAE,CACR,2CAA2C,EAC3C,uDAAuD,EACvD,mDAAmD,EACnD,yDAAyD,EACzD,mDAAmD,EACnD,yDAAyD,EACzD,iDAAiD,EACjD,kDAAkD,EAClD,yDAAyD,EACzD,oDAAoD,EACpD,4CAA4C,EAC5C,yDAAyD,EACzD,iDAAiD,EACjD,wDAAwD,EACxD,mDAAmD,EACnD,0DAA0D,EAC1D,kDAAkD,EAClD,mDAAmD;AAE3D,CAAC,EACD;EACI,MAAM,EAAE,aAAa;EACrB,IAAI,EAAEA,OAAO,CAAC,iCAAiC,CAAC;EAChD,UAAU,EAAE,CACR,sDAAsD,EACtD,oDAAoD,EACpD,kDAAkD,EAClD,0CAA0C,EAC1C,oDAAoD,EACpD,kDAAkD,EAClD,oDAAoD,EACpD,oDAAoD,EACpD,sDAAsD,EACtD,sDAAsD,EACtD,gDAAgD,EAChD,0DAA0D,EAC1D,wDAAwD,EACxD,0DAA0D,EAC1D,0DAA0D,EAC1D,wDAAwD,EACxD,wDAAwD,EACxD,sDAAsD,EACtD,wDAAwD,EACxD,wDAAwD;AAEhE,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}