{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Drawer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport './Drawer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Drawer = ({\n  isOpen,\n  onClose,\n  items,\n  onSelect\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEsc = event => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEsc);\n      // 阻止滚动\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEsc);\n      // 恢复滚动\n      document.body.style.overflow = '';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const handleOverlayClick = e => {\n    // 确保点击的是遮罩层而不是抽屉内容\n    if (e.target.classList.contains('drawer-overlay')) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"drawer-overlay\",\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"drawer\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"drawer-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u9009\\u62E9\\u6837\\u5F0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"drawer-content\",\n        children: items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"drawer-item\",\n          onClick: () => onSelect(item),\n          style: {\n            border: '1px solid #999',\n            position: 'relative'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              aspectRatio: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.bg,\n              alt: `朝代${index + 1}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 29\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 9\n  }, this);\n};\n_s(Drawer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Drawer;\nexport default Drawer;\nvar _c;\n$RefreshReg$(_c, \"Drawer\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "Drawer", "isOpen", "onClose", "items", "onSelect", "_s", "handleEsc", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "handleOverlayClick", "e", "target", "classList", "contains", "className", "onClick", "children", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "border", "position", "width", "aspectRatio", "src", "bg", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/electronProjects/boer-floor/pages/my-react-app/src/components/Drawer.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './Drawer.css';\n\nconst Drawer = ({ isOpen, onClose, items, onSelect }) => {\n    useEffect(() => {\n        const handleEsc = (event) => {\n            if (event.key === 'Escape') {\n                onClose();\n            }\n        };\n\n        if (isOpen) {\n            document.addEventListener('keydown', handleEsc);\n            // 阻止滚动\n            document.body.style.overflow = 'hidden';\n        }\n\n        return () => {\n            document.removeEventListener('keydown', handleEsc);\n            // 恢复滚动\n            document.body.style.overflow = '';\n        };\n    }, [isOpen, onClose]);\n\n    if (!isOpen) return null;\n\n    const handleOverlayClick = (e) => {\n        // 确保点击的是遮罩层而不是抽屉内容\n        if (e.target.classList.contains('drawer-overlay')) {\n            onClose();\n        }\n    };\n\n    return (\n        <div className=\"drawer-overlay\" onClick={handleOverlayClick}>\n            <div className=\"drawer\" onClick={e => e.stopPropagation()}>\n                <div className=\"drawer-header\">\n                    <h3>选择样式</h3>\n                    <button className=\"close-button\" onClick={onClose}>×</button>\n                </div>\n                <div className=\"drawer-content\">\n                    {items.map((item, index) => (\n                        <div\n                            key={index}\n                            className=\"drawer-item\"\n                            onClick={() => onSelect(item)}\n                            style={{ border: '1px solid #999', position: 'relative' }} \n                        >\n                            <div style={{width: '100%', aspectRatio: 1}}>\n                                <img src={item.bg} alt={`朝代${index + 1}`}/>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default Drawer; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrDR,SAAS,CAAC,MAAM;IACZ,MAAMS,SAAS,GAAIC,KAAK,IAAK;MACzB,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;QACxBN,OAAO,CAAC,CAAC;MACb;IACJ,CAAC;IAED,IAAID,MAAM,EAAE;MACRQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,SAAS,CAAC;MAC/C;MACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAC3C;IAEA,OAAO,MAAM;MACTJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,SAAS,CAAC;MAClD;MACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACrC,CAAC;EACL,CAAC,EAAE,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMc,kBAAkB,GAAIC,CAAC,IAAK;IAC9B;IACA,IAAIA,CAAC,CAACC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MAC/CjB,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EAED,oBACIH,OAAA;IAAKqB,SAAS,EAAC,gBAAgB;IAACC,OAAO,EAAEN,kBAAmB;IAAAO,QAAA,eACxDvB,OAAA;MAAKqB,SAAS,EAAC,QAAQ;MAACC,OAAO,EAAEL,CAAC,IAAIA,CAAC,CAACO,eAAe,CAAC,CAAE;MAAAD,QAAA,gBACtDvB,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAC1BvB,OAAA;UAAAuB,QAAA,EAAI;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACb5B,OAAA;UAAQqB,SAAS,EAAC,cAAc;UAACC,OAAO,EAAEnB,OAAQ;UAAAoB,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACN5B,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,EAC1BnB,KAAK,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnB/B,OAAA;UAEIqB,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAEA,CAAA,KAAMjB,QAAQ,CAACyB,IAAI,CAAE;UAC9BjB,KAAK,EAAE;YAAEmB,MAAM,EAAE,gBAAgB;YAAEC,QAAQ,EAAE;UAAW,CAAE;UAAAV,QAAA,eAE1DvB,OAAA;YAAKa,KAAK,EAAE;cAACqB,KAAK,EAAE,MAAM;cAAEC,WAAW,EAAE;YAAC,CAAE;YAAAZ,QAAA,eACxCvB,OAAA;cAAKoC,GAAG,EAAEN,IAAI,CAACO,EAAG;cAACC,GAAG,EAAE,KAAKP,KAAK,GAAG,CAAC;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC,GAPDG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQT,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACtB,EAAA,CAtDIL,MAAM;AAAAsC,EAAA,GAANtC,MAAM;AAwDZ,eAAeA,MAAM;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}